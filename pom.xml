<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <modules>
        <module>car-market-activity-service-api</module>
        <module>car-market-activity-service-svc</module>
        <module>car-market-activity-service-repository</module>
        <module>car-market-activity-service-core</module>
    </modules>

    <parent>
        <groupId>com.ctrip</groupId>
        <artifactId>super-pom</artifactId>
        <version>1.0.7</version>
    </parent>

    <groupId>com.ctrip.car.market</groupId>
    <artifactId>car-market-activity-service</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0</version>

    <properties>
        <car.market.activity.service.version>0.0.1</car.market.activity.service.version>
        <releases.repo>http://maven.release.ctripcorp.com/nexus/content/repositories/carrelease</releases.repo>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>21</java.version>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <lombok.version>1.18.32</lombok.version>
        <jmockit.version>1.52.0-jdk21</jmockit.version>
        <jacoco.version>0.8.11</jacoco.version>

        <junit.version>4.12</junit.version>
        <jackson.version>2.10.3</jackson.version>
        <org.mapstruct.version>1.2.0.Final</org.mapstruct.version>
        <aspectjweaver.version>1.9.7</aspectjweaver.version>
        <jmockit.version>1.49</jmockit.version>

        <car.market.activity.contract.version>1.0.21</car.market.activity.contract.version>
        <market.common.version>2.0.22</market.common.version>
        <market.common.util.version>1.4.0</market.common.util.version>
        <car.sd.common.version>2.11.47</car.sd.common.version>
        <cdp.client.version>1.0.2</cdp.client.version>
        <maiar.version>1.1.0</maiar.version>
        <metric.version>4.0.8</metric.version>
        <canal.json.version>1.0.0</canal.json.version>
        <car.commodity.service.version>0.0.26</car.commodity.service.version>
        <tms.version>1.0.5</tms.version>

        <carcache.version>1.0.24</carcache.version>
        <market.job.version>1.0.13	</market.job.version>
        <caffeine.version>2.6.2</caffeine.version>
        <car.order.version>1.0.65</car.order.version>
        <lom.version>1.18.8</lom.version>
        <ckafka.version>0.2.0</ckafka.version>

        <argLine>
            -javaagent:"${settings.localRepository}"/org/jmockit/jmockit/${jmockit.version}/jmockit-${jmockit.version}.jar
        </argLine>
    </properties>

    <dependencies>
        <!-- spock 核心包 -->
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <version>2.3-groovy-4.0</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>groovy</artifactId>
                    <groupId>org.apache.groovy</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- groovy -->
        <dependency> <!-- use a specific Groovy version rather than the one specified by spock-core -->
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy</artifactId>
            <version>4.0.19</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>5.11.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.dianping.cat</groupId>
            <artifactId>cat-prometheus-exporter</artifactId>
            <version>3.6.6</version>
        </dependency>

    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.ctrip.framework.ckafka</groupId>
                <artifactId>client</artifactId>
                <version>${ckafka.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.framework.ckafka</groupId>
                <artifactId>codec</artifactId>
                <version>${ckafka.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.car.sd.ctqrestfulshopping.v1</groupId>
                <artifactId>ctqrestfulshopping</artifactId>
                <version>0.6.85</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.ctrip.car.osd.framework</groupId>
                        <artifactId>dto</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 基础组件依赖 -->
            <dependency>
                <groupId>com.ctrip.framework</groupId>
                <artifactId>framework-bom</artifactId>
                <version>8.31.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>1.4.3.RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.ctrip.framework.ckafka</groupId>
                <artifactId>common</artifactId>
                <version>${ckafka.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>car-market-activity-service-svc</artifactId>
                <version>${project.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy-all</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>car-market-activity-service-repository</artifactId>
                <version>${project.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy-all</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>car-market-activity-service-core</artifactId>
                <version>${project.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy-all</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.ctrip.framework</groupId>
                <artifactId>canal-json</artifactId>
                <version>${canal.json.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>car-market-activity-contract</artifactId>
                <version>${car.market.activity.contract.version}</version>
            </dependency>

            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>3.27.0-GA</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>car-market-common</artifactId>
                <version>${market.common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>common</artifactId>
                <version>${market.common.util.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.ctrip.soa.22084</groupId>
                <artifactId>cdpsoaservice</artifactId>
                <version>${cdp.client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car</groupId>
                <artifactId>car-sd-common</artifactId>
                <version>${car.sd.common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.jacoco</groupId>
                        <artifactId>org.jacoco.agent</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cglib</groupId>
                        <artifactId>cglib-nodep</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cglib</groupId>
                        <artifactId>cglib</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.projectlombok</groupId>
                        <artifactId>lombok</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>${aspectjweaver.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-protobuf</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-xml</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-module-jaxb-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car</groupId>
                <artifactId>maiar-client</artifactId>
                <version>${maiar.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>javassist</artifactId>
                        <groupId>org.javassist</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.ctrip.flight.intl.common</groupId>
                <artifactId>metric-client</artifactId>
                <version>${metric.version}</version>
            </dependency>
          <dependency>
            <groupId>com.ctrip.car.25057</groupId>
            <artifactId>carcommoditycommonservice</artifactId>
            <version>${car.commodity.service.version}</version>
          </dependency>

          <dependency>
            <groupId>com.ctrip.car.osd.translate</groupId>
            <artifactId>client</artifactId>
            <version>${tms.version}</version>
          </dependency>

            <dependency>
                <groupId>com.ctrip.car.sd</groupId>
                <artifactId>carcache-all</artifactId>
                <version>${carcache.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>cjetcache-qschedule</artifactId>
                        <groupId>com.ctrip.car.sd</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>car-market-job-common</artifactId>
                <version>${market.job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lom.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.soa.21994</groupId>
                <artifactId>carsdqueryorderservice</artifactId>
                <version>${car.order.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>

        <plugins>
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>3.0.2</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>compileTests</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <id>default-instrument</id>
                        <goals>
                            <goal>instrument</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-restore-instrumented-classes</id>
                        <goals>
                            <goal>restore-instrumented-classes</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <argLine>
                        -javaagent:"${settings.localRepository}"/org/jacoco/org.jacoco.agent/${jacoco.version}/org.jacoco.agent-${jacoco.version}-runtime.jar=destfile=${project.build.directory}/jacoco.exec,excludes=*
                        -javaagent:"${settings.localRepository}"/org/jmockit/jmockit/${jmockit.version}/jmockit-${jmockit.version}.jar
                        --add-opens java.base/java.text=ALL-UNNAMED
                        --add-opens java.base/java.lang=ALL-UNNAMED
                        --add-opens java.base/java.lang.reflect=ALL-UNNAMED
                        --add-opens java.base/sun.reflect.annotation=ALL-UNNAMED
                        --add-opens java.base/java.math=ALL-UNNAMED
                        --add-opens java.base/java.util=ALL-UNNAMED
                        --add-opens java.base/sun.util.calendar=ALL-UNNAMED
                        --add-opens java.base/java.util.concurrent=ALL-UNNAMED
                        --add-opens java.base/java.util.concurrent.atomic=ALL-UNNAMED
                        --add-opens java.base/java.io=ALL-UNNAMED
                        --add-opens java.base/java.net=ALL-UNNAMED
                        --add-opens java.xml/com.sun.org.apache.xerces.internal.jaxp.datatype=ALL-UNNAMED
                        --add-opens java.management/sun.management=ALL-UNNAMED
                        --add-opens java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED
                        --add-opens jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.vm.annotation=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.access=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.util.random=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.misc=ALL-UNNAMED
                        --add-opens jdk.jfr/jdk.jfr.internal.tool=ALL-UNNAMED
                    </argLine>
                    <systemPropertyVariables>
                        <jacoco-agent.destfile>target/jacoco.exec</jacoco-agent.destfile>
                    </systemPropertyVariables>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>1.2.0.Final</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <forceJavacCompilerUse>true</forceJavacCompilerUse>
                </configuration>
            </plugin>

        </plugins>
    </build>
</project>
