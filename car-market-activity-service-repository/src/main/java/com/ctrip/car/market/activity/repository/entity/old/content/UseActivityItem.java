package com.ctrip.car.market.activity.repository.entity.old.content;

import java.math.BigDecimal;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/20.
 */
public class UseActivityItem {

    private Long configId;

    private BigDecimal orderOriginalAmount;

    private BigDecimal activityAmount;

    private Long orderID;

    private String uid;

    private String intlCode;

    private String tel;

    private String idCardNo;

    private String sn;

    private String clientIP;

    private String vendorActivityCode;

    //结算key，默认0，其他都是特殊结算
    private String settlementKey;

    //0 优惠券前结佣，1优惠券后结佣
    private Integer commissionType;

    private Integer SettlementType;

    //折扣类型 1 供应商Code，2动态调价
    private Integer discountType;

    public Integer getDiscountType() {
        return discountType;
    }

    public void setDiscountType(Integer discountType) {
        this.discountType = discountType;
    }

    public Integer getSettlementType() {
        return SettlementType;
    }

    public void setSettlementType(Integer settlementType) {
        SettlementType = settlementType;
    }

    public Long getConfigId() {
        return configId;
    }

    public void setConfigId(Long configId) {
        this.configId = configId;
    }

    public BigDecimal getOrderOriginalAmount() {
        return orderOriginalAmount;
    }

    public void setOrderOriginalAmount(BigDecimal orderOriginalAmount) {
        this.orderOriginalAmount = orderOriginalAmount;
    }

    public BigDecimal getActivityAmount() {
        return activityAmount;
    }

    public void setActivityAmount(BigDecimal activityAmount) {
        this.activityAmount = activityAmount;
    }

    public Long getOrderID() {
        return orderID;
    }

    public void setOrderID(Long orderID) {
        this.orderID = orderID;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getIntlCode() {
        return intlCode;
    }

    public void setIntlCode(String intlCode) {
        this.intlCode = intlCode;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getClientIP() {
        return clientIP;
    }

    public void setClientIP(String clientIP) {
        this.clientIP = clientIP;
    }

    public String getVendorActivityCode() {
        return vendorActivityCode;
    }

    public void setVendorActivityCode(String vendorActivityCode) {
        this.vendorActivityCode = vendorActivityCode;
    }

    public String getSettlementKey() {
        return settlementKey;
    }

    public void setSettlementKey(String settlementKey) {
        this.settlementKey = settlementKey;
    }

    public Integer getCommissionType() {
        return commissionType;
    }

    public void setCommissionType(Integer commissionType) {
        this.commissionType = commissionType;
    }
}
