package com.ctrip.car.market.activity.repository.entity.old.enums;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/20.
 */
public enum CalcActivityTypeEnum {
    TOTAL(0),
    UID(1),
    SN(2),
    ID_CARD(3);

    private final int code;

    CalcActivityTypeEnum(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public static CalcActivityTypeEnum findByCode(Integer code) {
        switch (code) {
            case 0:
                return TOTAL;
            case 1:
                return UID;
            case 2:
                return SN;
            case 99:
                return ID_CARD;
            default:
                return null;
        }
    }
}
