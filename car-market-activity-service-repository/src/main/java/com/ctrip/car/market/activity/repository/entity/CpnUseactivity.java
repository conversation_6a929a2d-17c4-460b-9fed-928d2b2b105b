package com.ctrip.car.market.activity.repository.entity;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;

@Entity
@Database(name = "carmarketingdb_dalcluster")
@Table(name = "cpn_useactivity")
public class CpnUseactivity implements DalPojo {

    //自动标识
    @Id
    @Column(name="FlowID")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value= Types.BIGINT)
    private Long flowID;

    //0使用，1取消使用
    @Column(name="Status")
    @Type(value=Types.INTEGER)
    private Integer status;

    //订单金额
    @Column(name="OrderAmount")
    @Type(value=Types.DECIMAL)
    private BigDecimal orderAmount;

    //活动金额
    @Column(name="ActivityAmount")
    @Type(value=Types.DECIMAL)
    private BigDecimal activityAmount;

    //使用订单号
    @Column(name="OrderID")
    @Type(value=Types.BIGINT)
    private Long orderID;

    //同OFFLINE配置
    @Column(name="PayOffType")
    @Type(value=Types.VARCHAR)
    private String payOffType;

    //UID
    @Column(name="UID")
    @Type(value=Types.VARCHAR)
    private String uID;

    //配置ID
    @Column(name="ConfigId")
    @Type(value=Types.BIGINT)
    private Long configId;

    //活动ID
    @Column(name="ActivityId")
    @Type(value=Types.INTEGER)
    private Integer activityId;

    //区号
    @Column(name="IntlCode")
    @Type(value=Types.VARCHAR)
    private String intlCode;

    //手机号
    @Column(name="Tel")
    @Type(value=Types.VARCHAR)
    private String tel;

    //身份证
    @Column(name="IDCardNo")
    @Type(value=Types.VARCHAR)
    private String iDCardNo;

    //IP
    @Column(name="ClientIP")
    @Type(value=Types.VARCHAR)
    private String clientIP;

    //SN
    @Column(name="SN")
    @Type(value=Types.VARCHAR)
    private String sN;

    //创建时间
    @Column(name="DataChange_CreateTime")
    @Type(value=Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    //最后修改时间
    @Column(name="DataChange_LastTime", insertable=false, updatable=false)
    @Type(value=Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    //供应商活动code
    @Column(name="VendorActivityCode")
    @Type(value=Types.VARCHAR)
    private String vendorActivityCode;

    //结算key，默认0，其他都是特殊结算
    @Column(name="SettlementKey")
    @Type(value=Types.VARCHAR)
    private String settlementKey;

    //0 优惠券前结佣，1优惠券后结佣
    @Column(name="CommissionType")
    @Type(value=Types.INTEGER)
    private Integer commissionType;

    public Long getFlowID() {
        return flowID;
    }

    public void setFlowID(Long flowID) {
        this.flowID = flowID;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getActivityAmount() {
        return activityAmount;
    }

    public void setActivityAmount(BigDecimal activityAmount) {
        this.activityAmount = activityAmount;
    }

    public Long getOrderID() {
        return orderID;
    }

    public void setOrderID(Long orderID) {
        this.orderID = orderID;
    }

    public String getPayOffType() {
        return payOffType;
    }

    public void setPayOffType(String payOffType) {
        this.payOffType = payOffType;
    }

    public String getUID() {
        return uID;
    }

    public void setUID(String uID) {
        this.uID = uID;
    }

    public Long getConfigId() {
        return configId;
    }

    public void setConfigId(Long configId) {
        this.configId = configId;
    }

    public Integer getActivityId() {
        return activityId;
    }

    public void setActivityId(Integer activityId) {
        this.activityId = activityId;
    }

    public String getIntlCode() {
        return intlCode;
    }

    public void setIntlCode(String intlCode) {
        this.intlCode = intlCode;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getIDCardNo() {
        return iDCardNo;
    }

    public void setIDCardNo(String iDCardNo) {
        this.iDCardNo = iDCardNo;
    }

    public String getClientIP() {
        return clientIP;
    }

    public void setClientIP(String clientIP) {
        this.clientIP = clientIP;
    }

    public String getSN() {
        return sN;
    }

    public void setSN(String sN) {
        this.sN = sN;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getVendorActivityCode() {
        return vendorActivityCode;
    }

    public void setVendorActivityCode(String vendorActivityCode) {
        this.vendorActivityCode = vendorActivityCode;
    }

    public String getSettlementKey() {
        return settlementKey;
    }

    public void setSettlementKey(String settlementKey) {
        this.settlementKey = settlementKey;
    }

    public Integer getCommissionType() {
        return commissionType;
    }

    public void setCommissionType(Integer commissionType) {
        this.commissionType = commissionType;
    }

}
