package com.ctrip.car.market.activity.repository.entity.old;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;

@Entity
@Database(name="CarMarketingDB_W")
@Table(name="cpn_activitycalc")
public class CpnActivitycalc implements DalPojo {

    //主键
    @Id
    @Column(name="CalcID")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value=Types.BIGINT)
    private Long calcID;

    //ConfigId_calctype_typevalue
    @Column(name="CalcKey")
    @Type(value=Types.VARCHAR)
    private String calcKey;

    //共使用次数
    @Column(name="TotalNum")
    @Type(value=Types.BIGINT)
    private Long totalNum;

    //总共使用总额
    @Column(name="TotalAmount")
    @Type(value=Types.DECIMAL)
    private BigDecimal totalAmount;

    //创建时间
    @Column(name="DataChange_CreateTime")
    @Type(value=Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    //最后修改时间
    @Column(name="DataChange_LastTime", insertable=false, updatable=false)
    @Type(value=Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public Long getCalcID() {
        return calcID;
    }

    public void setCalcID(Long calcID) {
        this.calcID = calcID;
    }

    public String getCalcKey() {
        return calcKey;
    }

    public void setCalcKey(String calcKey) {
        this.calcKey = calcKey;
    }

    public Long getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Long totalNum) {
        this.totalNum = totalNum;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

}