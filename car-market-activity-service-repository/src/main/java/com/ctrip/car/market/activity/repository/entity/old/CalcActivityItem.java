package com.ctrip.car.market.activity.repository.entity.old;

import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Types;

public class CalcActivityItem {
    //总量
    @Column(name = "totalNum")
    @Type(value = Types.BIGINT)
    private Long totalNum;

    //总额
    @Column(name = "totalAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal totalAmount;

    public Long getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Long totalNum) {
        this.totalNum = totalNum;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }
}
