package com.ctrip.car.market.activity.repository.po;

import java.util.List;

public class QueryActivityPara {

    private List<Long> activityIds;

    private String dataChangeLastTime;

    public List<Long> getActivityIds() {
        return activityIds;
    }

    public void setActivityIds(List<Long> activityIds) {
        this.activityIds = activityIds;
    }

    public String getDataChangeLastTime() {
        return dataChangeLastTime;
    }

    public void setDataChangeLastTime(String dataChangeLastTime) {
        this.dataChangeLastTime = dataChangeLastTime;
    }
}
