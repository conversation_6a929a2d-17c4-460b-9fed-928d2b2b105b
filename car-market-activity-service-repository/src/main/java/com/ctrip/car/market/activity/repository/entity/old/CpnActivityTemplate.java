package com.ctrip.car.market.activity.repository.entity.old;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2018-12-26
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "cpn_activity_template")
public class CpnActivityTemplate implements DalPojo {

    /**
     * id
     */
    @Id
    @Column(name = "templateId")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long templateId;

    /**
     * 报名模板 1-促销类
     */
    @Column(name = "category")
    @Type(value = Types.INTEGER)
    private Integer category;

    /**
     * 活动名称
     */
    @Column(name = "name")
    @Type(value = Types.VARCHAR)
    private String name;

    /**
     * 活动描述  给pms使用
     */
    @Column(name = "activityDesc")
    @Type(value = Types.VARCHAR)
    private String activityDesc;

    /**
     * 规则条款  给前端标签展示用
     */
    @Column(name = "activityRule")
    @Type(value = Types.VARCHAR)
    private String activityRule;

    /**
     * label的id
     */
    @Column(name = "labelCode")
    @Type(value = Types.INTEGER)
    private Integer labelCode;

    /**
     * 开始注册的时间
     */
    @Column(name = "registerStartTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp registerStartTime;

    /**
     * 注册结束时间
     */
    @Column(name = "registerEndTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp registerEndTime;

    /**
     * 活动开始时间   留空意味这即时生效
     */
    @Column(name = "activeStartTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp activeStartTime;

    /**
     * 活动结束时间  null 为永久生效
     */
    @Column(name = "activeEndTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp activeEndTime;

    /**
     * 0-全部  1-国内租车首页特惠位  2-广告专题位
     */
    @Column(name = "showLocation")
    @Type(value = Types.TINYINT)
    private Integer showLocation;

    /**
     * 最低服务分要求
     */
    @Column(name = "basicServiceScore")
    @Type(value = Types.REAL)
    private Float basicServiceScore;

    /**
     * 0-不限 1-优选供应商 2-直连供应商 3-pms供应商 4-自定义
     */
    @Column(name = "vendorType")
    @Type(value = Types.TINYINT)
    private Integer vendorType;

    /**
     * 报名表单的json配置
     */
    @Column(name = "registerFormConfig")
    @Type(value = Types.VARCHAR)
    private String registerFormConfig;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 是否激活 0-未激活  1-激活
     */
    @Column(name = "active")
    @Type(value = Types.SMALLINT)
    private Integer active;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getActivityDesc() {
        return activityDesc;
    }

    public void setActivityDesc(String activityDesc) {
        this.activityDesc = activityDesc;
    }

    public String getActivityRule() {
        return activityRule;
    }

    public void setActivityRule(String activityRule) {
        this.activityRule = activityRule;
    }

    public Integer getLabelCode() {
        return labelCode;
    }

    public void setLabelCode(Integer labelCode) {
        this.labelCode = labelCode;
    }

    public Timestamp getRegisterStartTime() {
        return registerStartTime;
    }

    public void setRegisterStartTime(Timestamp registerStartTime) {
        this.registerStartTime = registerStartTime;
    }

    public Timestamp getRegisterEndTime() {
        return registerEndTime;
    }

    public void setRegisterEndTime(Timestamp registerEndTime) {
        this.registerEndTime = registerEndTime;
    }

    public Timestamp getActiveStartTime() {
        return activeStartTime;
    }

    public void setActiveStartTime(Timestamp activeStartTime) {
        this.activeStartTime = activeStartTime;
    }

    public Timestamp getActiveEndTime() {
        return activeEndTime;
    }

    public void setActiveEndTime(Timestamp activeEndTime) {
        this.activeEndTime = activeEndTime;
    }

    public Integer getShowLocation() {
        return showLocation;
    }

    public void setShowLocation(Integer showLocation) {
        this.showLocation = showLocation;
    }

    public Float getBasicServiceScore() {
        return basicServiceScore;
    }

    public void setBasicServiceScore(Float basicServiceScore) {
        this.basicServiceScore = basicServiceScore;
    }

    public Integer getVendorType() {
        return vendorType;
    }

    public void setVendorType(Integer vendorType) {
        this.vendorType = vendorType;
    }

    public String getRegisterFormConfig() {
        return registerFormConfig;
    }

    public void setRegisterFormConfig(String registerFormConfig) {
        this.registerFormConfig = registerFormConfig;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public Integer getActive() {
        return active;
    }

    public void setActive(Integer active) {
        this.active = active;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

}
