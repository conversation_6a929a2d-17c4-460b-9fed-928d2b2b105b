package com.ctrip.car.market.activity.repository.entity.old;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2020-03-19
 */
@Entity
@Database(name = "CarMarketingDB_W")
@Table(name = "cpn_activityconfig")
public class CpnActivityconfig implements DalPojo {

    /**
     * 自动标识列
     */
    @Id
    @Column(name = "ConfigId")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long configId;

    /**
     * 活动ID
     */
    @Column(name = "ActivityID")
    @Type(value = Types.INTEGER)
    private Integer activityID;

    /**
     * 活动名称
     */
    @Column(name = "ActivityName")
    @Type(value = Types.VARCHAR)
    private String activityName;

    /**
     * 一级产线无None = 0,国内代驾CHF = 32,海外代驾OCH = 33,海外自驾OSD = 34,国内自驾ISD = 35,X产品X = 40,全球租车,34 35的组合体 SD = 41
     */
    @Column(name = "ProductCategory")
    @Type(value = Types.INTEGER)
    private Integer productCategory;

    /**
     * 二级产线
     */
    @Column(name = "ProductPattern")
    @Type(value = Types.INTEGER)
    private Integer productPattern;

    /**
     * 供应商IDs
     */
    @Column(name = "VendorIds")
    @Type(value = Types.VARCHAR)
    private String vendorIds;

    /**
     * 供应商Names
     */
    @Column(name = "VendorNames")
    @Type(value = Types.VARCHAR)
    private String vendorNames;

    /**
     * 排除供应商0默认非，1是
     */
    @Column(name = "ReverseVendor")
    @Type(value = Types.INTEGER)
    private Integer reverseVendor;

    /**
     * 活动时间
     */
    @Column(name = "ActivityTime")
    @Type(value = Types.VARCHAR)
    private String activityTime;

    /**
     * 租期
     */
    @Column(name = "RentDay")
    @Type(value = Types.VARCHAR)
    private String rentDay;

    /**
     * 取车时间
     */
    @Column(name = "PickTime")
    @Type(value = Types.VARCHAR)
    private String pickTime;

    /**
     * 还车时间
     */
    @Column(name = "DropOffTime")
    @Type(value = Types.VARCHAR)
    private String dropOffTime;

    /**
     * 国家
     */
    @Column(name = "CountryIds")
    @Type(value = Types.VARCHAR)
    private String countryIds;

    /**
     * 国家名称
     */
    @Column(name = "CountryNames")
    @Type(value = Types.VARCHAR)
    private String countryNames;

    /**
     * 排除国家0默认不排除 1排除
     */
    @Column(name = "ReverseCountry")
    @Type(value = Types.INTEGER)
    private Integer reverseCountry;

    /**
     * 城市
     */
    @Column(name = "CityIds")
    @Type(value = Types.VARCHAR)
    private String cityIds;

    /**
     * 城市名称
     */
    @Column(name = "CityNames")
    @Type(value = Types.VARCHAR)
    private String cityNames;

    /**
     * 排除城市
     */
    @Column(name = "ReverseCity")
    @Type(value = Types.INTEGER)
    private Integer reverseCity;

    /**
     * 活动渠道
     */
    @Column(name = "DisChannelIds")
    @Type(value = Types.VARCHAR)
    private String disChannelIds;

    /**
     * 活动渠道名称
     */
    @Column(name = "DisChannelNames")
    @Type(value = Types.VARCHAR)
    private String disChannelNames;

    /**
     * 默认0非，1是
     */
    @Column(name = "ReverseDisChannel")
    @Type(value = Types.INTEGER)
    private Integer reverseDisChannel;

    /**
     * 车型组Ids
     */
    @Column(name = "VehicleGroupIds")
    @Type(value = Types.VARCHAR)
    private String vehicleGroupIds;

    /**
     * 车型组Names
     */
    @Column(name = "VehicleGroupNames")
    @Type(value = Types.VARCHAR)
    private String vehicleGroupNames;

    /**
     * 默认0 否，1 是
     */
    @Column(name = "ReverseVehicleGroup")
    @Type(value = Types.INTEGER)
    private Integer reverseVehicleGroup;

    /**
     * 车型Ids
     */
    @Column(name = "VehicleIds")
    @Type(value = Types.VARCHAR)
    private String vehicleIds;

    /**
     * 排除车型 默认0非 1是
     */
    @Column(name = "ReverseVehicle")
    @Type(value = Types.INTEGER)
    private Integer reverseVehicle;

    /**
     * 0默认，1返现 2立减
     */
    @Column(name = "PayOffType")
    @Type(value = Types.INTEGER)
    private Integer payOffType;

    /**
     * 默认0 现付1 预付2
     */
    @Column(name = "PayMode")
    @Type(value = Types.INTEGER)
    private Integer payMode;

    /**
     * 折扣类型 1 供应商Code，2动态调价
     */
    @Column(name = "DiscountType")
    @Type(value = Types.INTEGER)
    private Integer discountType;

    /**
     * 供应商Code
     */
    @Column(name = "VendorCode")
    @Type(value = Types.VARCHAR)
    private String vendorCode;

    /**
     * 不限-0
     * 供应商新客-1 活动系统还是兼容，为了兼容pms供应商也是写的1，这里默认逻辑还是需要判断是否pms
     * 供应商在携程的新客-2 直连供应商也可以选这个。pms供应商肯定是这个
     * 真正的携程新客-3 用车携程新客
     */
    @Column(name = "IsVendorFirstRent")
    @Type(value = Types.INTEGER)
    private Integer isVendorFirstRent;

    /**
     * 0默认 否 1是 是否限最大首日租车费
     */
    @Column(name = "IsMaxFirstRent")
    @Type(value = Types.INTEGER)
    private Integer isMaxFirstRent;

    /**
     * 参加活动的费用
     */
    @Column(name = "ExCludeFees")
    @Type(value = Types.VARCHAR)
    private String exCludeFees;

    /**
     * 默认0 不同享 1同享
     */
    @Column(name = "IsShareCoupon")
    @Type(value = Types.INTEGER)
    private Integer isShareCoupon;

    /**
     * 标签
     */
    @Column(name = "LabelId")
    @Type(value = Types.INTEGER)
    private Integer labelId;

    /**
     * 活动标题
     */
    @Column(name = "LabelContent")
    @Type(value = Types.VARCHAR)
    private String labelContent;

    /**
     * 活动说明
     */
    @Column(name = "ActivityDes")
    @Type(value = Types.VARCHAR)
    private String activityDes;

    /**
     * 优先级
     */
    @Column(name = "SortIndex")
    @Type(value = Types.INTEGER)
    private Integer sortIndex;

    /**
     * 结算类型0默认；1特殊结算KEY
     */
    @Column(name = "SettlementType")
    @Type(value = Types.INTEGER)
    private Integer settlementType;

    /**
     * 结算Key
     */
    @Column(name = "SettlementKey")
    @Type(value = Types.VARCHAR)
    private String settlementKey;

    /**
     * 状态 0创建;1已生效 2已删除 3:已过期 4:待审核
     */
    @Column(name = "Status")
    @Type(value = Types.INTEGER)
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "DataChange_CreateTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 最后修改时间
     */
    @Column(name = "DataChange_LastTime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 创建人
     */
    @Column(name = "CreateMan")
    @Type(value = Types.VARCHAR)
    private String createMan;

    /**
     * 最后修改人
     */
    @Column(name = "UpdateMan")
    @Type(value = Types.VARCHAR)
    private String updateMan;

    /**
     * 价格折扣类型0供应商Code；1动态调价
     */
    @Column(name = "PriceDiscountValue")
    @Type(value = Types.DECIMAL)
    private BigDecimal priceDiscountValue;

    /**
     * 库存量
     */
    @Column(name = "StockNum")
    @Type(value = Types.BIGINT)
    private Long stockNum;

    /**
     * 活动金额
     */
    @Column(name = "StockAmount")
    @Type(value = Types.DECIMAL)
    private BigDecimal stockAmount;

    /**
     * 优惠时段
     */
    @Column(name = "DiscountTime")
    @Type(value = Types.VARCHAR)
    private String discountTime;

    /**
     * 是否允许跨优惠时段使用  0-默认  允许
     * 1-不允许
     */
    @Column(name = "enableCrossPeriod")
    @Type(value = Types.TINYINT)
    private Integer enableCrossPeriod;

    /**
     * 0固定金额1百分比
     */
    @Column(name = "PriceDiscountType")
    @Type(value = Types.INTEGER)
    private Integer priceDiscountType;

    /**
     * 满减
     */
    @Column(name = "FullReduce")
    @Type(value = Types.VARCHAR)
    private String fullReduce;

    /**
     * 0不限制 >0限制 UID领券限制
     */
    @Column(name = "CountPerUid")
    @Type(value = Types.INTEGER)
    private Integer countPerUid;

    /**
     * 0不限制 >0限制 身份证领取限制
     */
    @Column(name = "CountPerIDC")
    @Type(value = Types.INTEGER)
    private Integer countPerIDC;

    /**
     * cpn_activitypower外键
     */
    @Column(name = "Powers")
    @Type(value = Types.VARCHAR)
    private String powers;

    /**
     * 活动小时数
     */
    @Column(name = "RentHour")
    @Type(value = Types.VARCHAR)
    private String rentHour;

    /**
     * 当前是否有效，默认无效
     */
    @Column(name = "NowActive")
    @Type(value = Types.INTEGER)
    private Integer nowActive;

    /**
     * 活动渠道组
     */
    @Column(name = "DisChannelGroup")
    @Type(value = Types.VARCHAR)
    private String disChannelGroup;

    /**
     * 减少xx日
     */
    @Column(name = "PriceDiscountDayValue")
    @Type(value = Types.INTEGER)
    private Integer priceDiscountDayValue;

    /**
     * 结算方式0 优惠券，1优惠后
     */
    @Column(name = "CommissionType")
    @Type(value = Types.TINYINT)
    private Integer commissionType;

    /**
     * 默认0 表示携程，1表示PMS平台
     */
    @Column(name = "Platform")
    @Type(value = Types.TINYINT)
    private Integer platform;

    /**
     * 关联活动配置ID，逗号分隔
     */
    @Column(name = "RelationConfigIds")
    @Type(value = Types.VARCHAR)
    private String relationConfigIds;

    /**
     * 供应商门店ID，逗号分隔
     */
    @Column(name = "VendorStorecCodes")
    @Type(value = Types.VARCHAR)
    private String vendorStorecCodes;

    /**
     * 默认0 否，1是 是否排除
     */
    @Column(name = "ReverseVendorStore")
    @Type(value = Types.TINYINT)
    private Integer reverseVendorStore;

    /**
     * 是否与套餐价同享 0-默认 与套餐价同享  1-不与套餐价同享
     */
    @Column(name = "IsShareWithPackage")
    @Type(value = Types.TINYINT)
    private Integer isShareWithPackage;

    /**
     * 提前天数，逗号分隔，小的在前，大的在后
     */
    @Column(name = "EarlyDays")
    @Type(value = Types.VARCHAR)
    private String earlyDays;

    /**
     * 甩尾周期，前面是时间，逗号分隔后是持续小时数
     */
    @Column(name = "LeftOverPeriods")
    @Type(value = Types.VARCHAR)
    private String leftOverPeriods;

    /**
     * 此活动属于哪个活动模板下的，null-不属于任何模板
     */
    @Column(name = "templateId")
    @Type(value = Types.BIGINT)
    private Long templateId;

    /**
     * json 格式数据
     */
    @Column(name = "content")
    @Type(value = Types.VARCHAR)
    private String content;

    /**
     * 海外套餐
     */
    @Column(name = "packageIds")
    @Type(value = Types.VARCHAR)
    private String packageIds;

    /**
     * 排除套餐
     */
    @Column(name = "reversePackage")
    @Type(value = Types.TINYINT)
    private Integer reversePackage;

    /**
     * 不能包含的费用项
     */
    @Column(name = "feeMustNotIn")
    @Type(value = Types.VARCHAR)
    private String feeMustNotIn;

    public Long getConfigId() {
        return configId;
    }

    public void setConfigId(Long configId) {
        this.configId = configId;
    }

    public Integer getActivityID() {
        return activityID;
    }

    public void setActivityID(Integer activityID) {
        this.activityID = activityID;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public Integer getProductCategory() {
        return productCategory;
    }

    public void setProductCategory(Integer productCategory) {
        this.productCategory = productCategory;
    }

    public Integer getProductPattern() {
        return productPattern;
    }

    public void setProductPattern(Integer productPattern) {
        this.productPattern = productPattern;
    }

    public String getVendorIds() {
        return vendorIds;
    }

    public void setVendorIds(String vendorIds) {
        this.vendorIds = vendorIds;
    }

    public String getVendorNames() {
        return vendorNames;
    }

    public void setVendorNames(String vendorNames) {
        this.vendorNames = vendorNames;
    }

    public Integer getReverseVendor() {
        return reverseVendor;
    }

    public void setReverseVendor(Integer reverseVendor) {
        this.reverseVendor = reverseVendor;
    }

    public String getActivityTime() {
        return activityTime;
    }

    public void setActivityTime(String activityTime) {
        this.activityTime = activityTime;
    }

    public String getRentDay() {
        return rentDay;
    }

    public void setRentDay(String rentDay) {
        this.rentDay = rentDay;
    }

    public String getPickTime() {
        return pickTime;
    }

    public void setPickTime(String pickTime) {
        this.pickTime = pickTime;
    }

    public String getDropOffTime() {
        return dropOffTime;
    }

    public void setDropOffTime(String dropOffTime) {
        this.dropOffTime = dropOffTime;
    }

    public String getCountryIds() {
        return countryIds;
    }

    public void setCountryIds(String countryIds) {
        this.countryIds = countryIds;
    }

    public String getCountryNames() {
        return countryNames;
    }

    public void setCountryNames(String countryNames) {
        this.countryNames = countryNames;
    }

    public Integer getReverseCountry() {
        return reverseCountry;
    }

    public void setReverseCountry(Integer reverseCountry) {
        this.reverseCountry = reverseCountry;
    }

    public String getCityIds() {
        return cityIds;
    }

    public void setCityIds(String cityIds) {
        this.cityIds = cityIds;
    }

    public String getCityNames() {
        return cityNames;
    }

    public void setCityNames(String cityNames) {
        this.cityNames = cityNames;
    }

    public Integer getReverseCity() {
        return reverseCity;
    }

    public void setReverseCity(Integer reverseCity) {
        this.reverseCity = reverseCity;
    }

    public String getDisChannelIds() {
        return disChannelIds;
    }

    public void setDisChannelIds(String disChannelIds) {
        this.disChannelIds = disChannelIds;
    }

    public String getDisChannelNames() {
        return disChannelNames;
    }

    public void setDisChannelNames(String disChannelNames) {
        this.disChannelNames = disChannelNames;
    }

    public Integer getReverseDisChannel() {
        return reverseDisChannel;
    }

    public void setReverseDisChannel(Integer reverseDisChannel) {
        this.reverseDisChannel = reverseDisChannel;
    }

    public String getVehicleGroupIds() {
        return vehicleGroupIds;
    }

    public void setVehicleGroupIds(String vehicleGroupIds) {
        this.vehicleGroupIds = vehicleGroupIds;
    }

    public String getVehicleGroupNames() {
        return vehicleGroupNames;
    }

    public void setVehicleGroupNames(String vehicleGroupNames) {
        this.vehicleGroupNames = vehicleGroupNames;
    }

    public Integer getReverseVehicleGroup() {
        return reverseVehicleGroup;
    }

    public void setReverseVehicleGroup(Integer reverseVehicleGroup) {
        this.reverseVehicleGroup = reverseVehicleGroup;
    }

    public String getVehicleIds() {
        return vehicleIds;
    }

    public void setVehicleIds(String vehicleIds) {
        this.vehicleIds = vehicleIds;
    }

    public Integer getReverseVehicle() {
        return reverseVehicle;
    }

    public void setReverseVehicle(Integer reverseVehicle) {
        this.reverseVehicle = reverseVehicle;
    }

    public Integer getPayOffType() {
        return payOffType;
    }

    public void setPayOffType(Integer payOffType) {
        this.payOffType = payOffType;
    }

    public Integer getPayMode() {
        return payMode;
    }

    public void setPayMode(Integer payMode) {
        this.payMode = payMode;
    }

    public Integer getDiscountType() {
        return discountType;
    }

    public void setDiscountType(Integer discountType) {
        this.discountType = discountType;
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode;
    }

    public Integer getIsVendorFirstRent() {
        return isVendorFirstRent;
    }

    public void setIsVendorFirstRent(Integer isVendorFirstRent) {
        this.isVendorFirstRent = isVendorFirstRent;
    }

    public Integer getIsMaxFirstRent() {
        return isMaxFirstRent;
    }

    public void setIsMaxFirstRent(Integer isMaxFirstRent) {
        this.isMaxFirstRent = isMaxFirstRent;
    }

    public String getExCludeFees() {
        return exCludeFees;
    }

    public void setExCludeFees(String exCludeFees) {
        this.exCludeFees = exCludeFees;
    }

    public Integer getIsShareCoupon() {
        return isShareCoupon;
    }

    public void setIsShareCoupon(Integer isShareCoupon) {
        this.isShareCoupon = isShareCoupon;
    }

    public Integer getLabelId() {
        return labelId;
    }

    public void setLabelId(Integer labelId) {
        this.labelId = labelId;
    }

    public String getLabelContent() {
        return labelContent;
    }

    public void setLabelContent(String labelContent) {
        this.labelContent = labelContent;
    }

    public String getActivityDes() {
        return activityDes;
    }

    public void setActivityDes(String activityDes) {
        this.activityDes = activityDes;
    }

    public Integer getSortIndex() {
        return sortIndex;
    }

    public void setSortIndex(Integer sortIndex) {
        this.sortIndex = sortIndex;
    }

    public Integer getSettlementType() {
        return settlementType;
    }

    public void setSettlementType(Integer settlementType) {
        this.settlementType = settlementType;
    }

    public String getSettlementKey() {
        return settlementKey;
    }

    public void setSettlementKey(String settlementKey) {
        this.settlementKey = settlementKey;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getCreateMan() {
        return createMan;
    }

    public void setCreateMan(String createMan) {
        this.createMan = createMan;
    }

    public String getUpdateMan() {
        return updateMan;
    }

    public void setUpdateMan(String updateMan) {
        this.updateMan = updateMan;
    }

    public BigDecimal getPriceDiscountValue() {
        return priceDiscountValue;
    }

    public void setPriceDiscountValue(BigDecimal priceDiscountValue) {
        this.priceDiscountValue = priceDiscountValue;
    }

    public Long getStockNum() {
        return stockNum;
    }

    public void setStockNum(Long stockNum) {
        this.stockNum = stockNum;
    }

    public BigDecimal getStockAmount() {
        return stockAmount;
    }

    public void setStockAmount(BigDecimal stockAmount) {
        this.stockAmount = stockAmount;
    }

    public String getDiscountTime() {
        return discountTime;
    }

    public void setDiscountTime(String discountTime) {
        this.discountTime = discountTime;
    }

    public Integer getEnableCrossPeriod() {
        return enableCrossPeriod;
    }

    public void setEnableCrossPeriod(Integer enableCrossPeriod) {
        this.enableCrossPeriod = enableCrossPeriod;
    }

    public Integer getPriceDiscountType() {
        return priceDiscountType;
    }

    public void setPriceDiscountType(Integer priceDiscountType) {
        this.priceDiscountType = priceDiscountType;
    }

    public String getFullReduce() {
        return fullReduce;
    }

    public void setFullReduce(String fullReduce) {
        this.fullReduce = fullReduce;
    }

    public Integer getCountPerUid() {
        return countPerUid;
    }

    public void setCountPerUid(Integer countPerUid) {
        this.countPerUid = countPerUid;
    }

    public Integer getCountPerIDC() {
        return countPerIDC;
    }

    public void setCountPerIDC(Integer countPerIDC) {
        this.countPerIDC = countPerIDC;
    }

    public String getPowers() {
        return powers;
    }

    public void setPowers(String powers) {
        this.powers = powers;
    }

    public String getRentHour() {
        return rentHour;
    }

    public void setRentHour(String rentHour) {
        this.rentHour = rentHour;
    }

    public Integer getNowActive() {
        return nowActive;
    }

    public void setNowActive(Integer nowActive) {
        this.nowActive = nowActive;
    }

    public String getDisChannelGroup() {
        return disChannelGroup;
    }

    public void setDisChannelGroup(String disChannelGroup) {
        this.disChannelGroup = disChannelGroup;
    }

    public Integer getPriceDiscountDayValue() {
        return priceDiscountDayValue;
    }

    public void setPriceDiscountDayValue(Integer priceDiscountDayValue) {
        this.priceDiscountDayValue = priceDiscountDayValue;
    }

    public Integer getCommissionType() {
        return commissionType;
    }

    public void setCommissionType(Integer commissionType) {
        this.commissionType = commissionType;
    }

    public Integer getPlatform() {
        return platform;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    public String getRelationConfigIds() {
        return relationConfigIds;
    }

    public void setRelationConfigIds(String relationConfigIds) {
        this.relationConfigIds = relationConfigIds;
    }

    public String getVendorStorecCodes() {
        return vendorStorecCodes;
    }

    public void setVendorStorecCodes(String vendorStorecCodes) {
        this.vendorStorecCodes = vendorStorecCodes;
    }

    public Integer getReverseVendorStore() {
        return reverseVendorStore;
    }

    public void setReverseVendorStore(Integer reverseVendorStore) {
        this.reverseVendorStore = reverseVendorStore;
    }

    public Integer getIsShareWithPackage() {
        return isShareWithPackage;
    }

    public void setIsShareWithPackage(Integer isShareWithPackage) {
        this.isShareWithPackage = isShareWithPackage;
    }

    public String getEarlyDays() {
        return earlyDays;
    }

    public void setEarlyDays(String earlyDays) {
        this.earlyDays = earlyDays;
    }

    public String getLeftOverPeriods() {
        return leftOverPeriods;
    }

    public void setLeftOverPeriods(String leftOverPeriods) {
        this.leftOverPeriods = leftOverPeriods;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getPackageIds() {
        return packageIds;
    }

    public void setPackageIds(String packageIds) {
        this.packageIds = packageIds;
    }

    public Integer getReversePackage() {
        return reversePackage;
    }

    public void setReversePackage(Integer reversePackage) {
        this.reversePackage = reversePackage;
    }

    public String getFeeMustNotIn() {
        return feeMustNotIn;
    }

    public void setFeeMustNotIn(String feeMustNotIn) {
        this.feeMustNotIn = feeMustNotIn;
    }

}
