package com.ctrip.car.market.activity.repository.service;

import com.ctrip.car.market.activity.repository.entity.*;
import com.ctrip.car.market.activity.repository.po.QueryActivityPara;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.base.DalTableOperations;
import com.ctrip.platform.dal.dao.base.SQLResult;
import com.ctrip.platform.dal.dao.client.DalOperationsFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.List;

@Component
public class ActivityService {

    private final DalTableOperations<ActCtripactinfo> actTableOperations = DalOperationsFactory.getDalTableOperations(ActCtripactinfo.class);

    private final DalTableOperations<ActCtriptempinfo> tempTableOperations = DalOperationsFactory.getDalTableOperations(ActCtriptempinfo.class);

    private final DalTableOperations<ActCityinfo> cityTableOperations = DalOperationsFactory.getDalTableOperations(ActCityinfo.class);

    private final DalTableOperations<ActReturnCityinfo> returnCityTableOperations = DalOperationsFactory.getDalTableOperations(ActReturnCityinfo.class);

    private final DalTableOperations<ActProductids> productTableOperations = DalOperationsFactory.getDalTableOperations(ActProductids.class);

    private final DalTableOperations<CpnUseactivity> useActivityOperations = DalOperationsFactory.getDalTableOperations(CpnUseactivity.class);

    private final DalTableOperations<ActOsdactinfo> osdActivityOperations = DalOperationsFactory.getDalTableOperations(ActOsdactinfo.class);

    private final DalTableOperations<ActOsdlabel> osdLabelOperations = DalOperationsFactory.getDalTableOperations(ActOsdlabel.class);

    private final DalTableOperations<CpnLabel> labelOperations = DalOperationsFactory.getDalTableOperations(CpnLabel.class);

    public List<ActCtripactinfo> queryActivityByPage(QueryActivityPara para, int pageNo, int pageSize) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from act_ctripactinfo where status = 1 ");
        if (CollectionUtils.isNotEmpty(para.getActivityIds())) {
            sql.append("and id in (#{activityIds}) ");
        }
        if (StringUtils.isNotBlank(para.getDataChangeLastTime())) {
            sql.append("and datachange_lasttime >= #{dataChangeLastTime} ");
        }
        sql.append("order by id ");
        return actTableOperations.queryFrom(sql.toString(), new DalHints(), (pageNo - 1) * pageSize, pageSize, SQLResult.type(ActCtripactinfo.class), para);
    }

    public int queryActivityCount(QueryActivityPara para) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("select count(1) from act_ctripactinfo where status = 1 ");
        if (CollectionUtils.isNotEmpty(para.getActivityIds())) {
            sql.append("and id in (#{activityIds}) ");
        }
        if (StringUtils.isNotBlank(para.getDataChangeLastTime())) {
            sql.append("and datachange_lasttime >= #{dataChangeLastTime} ");
        }
        return actTableOperations.count(sql.toString(), new DalHints(), para).intValue();
    }

    public List<ActCtriptempinfo> queryActivityTemplate(List<Long> templateIds) throws SQLException {
        return tempTableOperations.query("select * from act_ctriptempinfo where tmpId in (?)", new DalHints(), templateIds);
    }

    public ActCtriptempinfo queryByTempId(Long tempId) throws SQLException {
        return tempTableOperations.queryByPk(tempId, new DalHints());
    }

    public List<ActCtripactinfo> queryAllActivity() throws Exception {
        List<ActCtripactinfo> result = Lists.newArrayList();
        long id = 0L;
        List<ActCtripactinfo> temp;
        do {
            temp = actTableOperations.query("select * from act_ctripactinfo where status=1 and Id>? order by Id limit 5000;", new DalHints(), id);
            if (CollectionUtils.isNotEmpty(temp)) {
                id = temp.stream().mapToLong(ActCtripactinfo::getId).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    public List<ActCtriptempinfo> queryAllTemp() throws SQLException {
        return tempTableOperations.query("select * from act_ctriptempinfo where status = 1", new DalHints(), Maps.newHashMap());
    }

    public List<ActCityinfo> queryActivityCity(Long actId) throws SQLException {
        return cityTableOperations.query("select * from act_cityinfo where activityId = ? and isactive=1", new DalHints(), actId);
    }

    public List<ActCityinfo> queryActivityCity() throws SQLException {
        List<ActCityinfo> result = Lists.newArrayList();
        long id = 0L;
        List<ActCityinfo> temp;
        do {
            temp = cityTableOperations.query("select city.* from act_cityinfo city inner join act_ctripactinfo act on act.Id=city.activityId and act.status=1 where city.id>? and city.isActive=1 order by city.id limit 5000;", new DalHints(), id);
            if (CollectionUtils.isNotEmpty(temp)) {
                id = temp.stream().mapToLong(ActCityinfo::getId).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    public List<ActReturnCityinfo> queryActivityReturnCity(Long actId) throws SQLException {
        return returnCityTableOperations.query("select * from act_returncityinfo where activityId = ? and isactive=1", new DalHints(), actId);
    }

    public List<ActReturnCityinfo> queryActivityReturnCity() throws SQLException {
        List<ActReturnCityinfo> result = Lists.newArrayList();
        long id = 0L;
        List<ActReturnCityinfo> temp;
        do {
            temp = returnCityTableOperations.query("select city.* from act_returncityinfo city inner join act_ctripactinfo act on act.Id=city.activityId and act.status=1 where city.id>? and city.isActive=1 order by city.id limit 5000;", new DalHints(), id);
            if (CollectionUtils.isNotEmpty(temp)) {
                id = temp.stream().mapToLong(ActReturnCityinfo::getId).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    public List<ActProductids> queryActivityProduct(Long actId) throws SQLException {
        return productTableOperations.query("select * from act_productids where activityId = ? and isactive=1", new DalHints(), actId);
    }

    public List<ActProductids> queryActivityProduct() throws Exception {
        List<ActProductids> result = Lists.newArrayList();
        long id = 0L;
        List<ActProductids> temp;
        do {
            temp = productTableOperations.query("select pro.* from act_productids pro inner join act_ctripactinfo act on act.Id=pro.activityId and act.status=1 where pro.id>? and pro.isActive=1 order by pro.Id limit 5000;", new DalHints(), id);
            if (CollectionUtils.isNotEmpty(temp)) {
                id = temp.stream().mapToLong(ActProductids::getId).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    public ActCtripactinfo queryByPk(Long activityId) throws SQLException {
        return actTableOperations.queryByPk(activityId, new DalHints());
    }

    public int updateActivity(ActCtripactinfo act) throws SQLException {
        return actTableOperations.update(new DalHints(), act);
    }

    public List<CpnUseactivity> queryUseActivity(Long orderId, Long activityId, String uid) throws SQLException {
        return useActivityOperations.query("select * from cpn_useactivity where OrderID=? and ConfigId=? and UID=? and Status=0", new DalHints(), orderId, activityId, uid);
    }

    public int insertUseActivity(CpnUseactivity item) throws SQLException {
        return useActivityOperations.insert(new DalHints(), item);
    }

    public int updateUseActivity(CpnUseactivity item) throws SQLException {
        return useActivityOperations.update(new DalHints(), item);
    }

    public CpnLabel queryLabel(Integer labelId) {
        try {
            return labelOperations.queryByPk(labelId.longValue(), new DalHints());
        } catch (Exception e) {
            return null;
        }
    }

    public List<ActOsdactinfo> queryAllOsdActivity() throws Exception {
        List<ActOsdactinfo> result = Lists.newArrayList();
        long id = 0L;
        List<ActOsdactinfo> temp;
        do {
            temp = osdActivityOperations.query("select * from act_osdactinfo where status=1 and id>? order by id limit 5000;", new DalHints(), id);
            if (CollectionUtils.isNotEmpty(temp)) {
                id = temp.stream().mapToLong(ActOsdactinfo::getId).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    public List<ActOsdactinfo> queryOsdActivityByPage(QueryActivityPara para, Integer pageNo, Integer pageSize) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from act_osdactinfo where status = 1 ");
        if (CollectionUtils.isNotEmpty(para.getActivityIds())) {
            sql.append("and id in (#{activityIds}) ");
        }
        sql.append("order by id ");
        return osdActivityOperations.queryFrom(sql.toString(), new DalHints(), (pageNo - 1) * pageSize, pageSize, SQLResult.type(ActOsdactinfo.class), para);
    }

    public int queryOsdActivityCount(QueryActivityPara para) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("select count(1) from act_osdactinfo where status = 1 ");
        if (CollectionUtils.isNotEmpty(para.getActivityIds())) {
            sql.append("and id in (#{activityIds}) ");
        }
        return osdActivityOperations.count(sql.toString(), new DalHints(), para).intValue();
    }

    public ActOsdactinfo queryOsdAct(Long id) throws Exception {
        return osdActivityOperations.queryByPk(id, new DalHints());
    }

    public List<ActOsdlabel> queryAllOsdActivityLabel() throws Exception {
        List<ActOsdlabel> result = Lists.newArrayList();
        long id = 0L;
        List<ActOsdlabel> temp;
        do {
            temp = osdLabelOperations.query("select * from act_osdlabel where isActivity=1 and id>? order by id limit 5000;", new DalHints(), id);
            if (CollectionUtils.isNotEmpty(temp)) {
                id = temp.stream().mapToLong(ActOsdlabel::getId).max().orElse(0);
                result.addAll(temp);
            }
        } while (CollectionUtils.isNotEmpty(temp));
        return result;
    }

    public List<ActOsdlabel> queryOsdActivityLabel(List<Long> ids) throws SQLException {
        return osdLabelOperations.query("select * from act_osdlabel where isActivity=1 and activityId in (?)", new DalHints(), ids);
    }

    public List<ActOsdlabel> queryOsdActivityLabel(Long activityId) throws SQLException {
        return osdLabelOperations.query("select * from act_osdlabel where isActivity=1 and activityId = ?", new DalHints(), activityId);
    }

    public List<CpnLabel> queryLabel(List<Long> codes) throws SQLException {
        return labelOperations.query("select * from cpn_label where IsActive =1 and Code in (?)", new DalHints(), codes);
    }
}
