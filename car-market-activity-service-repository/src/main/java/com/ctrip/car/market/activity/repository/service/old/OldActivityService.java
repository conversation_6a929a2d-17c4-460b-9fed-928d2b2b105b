package com.ctrip.car.market.activity.repository.service.old;

import com.ctrip.car.market.activity.repository.entity.CpnUseactivity;
import com.ctrip.car.market.activity.repository.entity.old.CalcActivityItem;
import com.ctrip.car.market.activity.repository.entity.old.CpnActivityTemplate;
import com.ctrip.car.market.activity.repository.entity.old.CpnActivitycalc;
import com.ctrip.car.market.activity.repository.entity.old.CpnActivityconfig;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.base.DalTableOperations;
import com.ctrip.platform.dal.dao.base.SQLResult;
import com.ctrip.platform.dal.dao.client.DalOperationsFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.List;

@Component
public class OldActivityService {

    protected DalTableOperations<CpnActivityconfig> activityConfigDao = DalOperationsFactory.getDalTableOperations(CpnActivityconfig.class);

    protected DalTableOperations<CpnActivityTemplate> activityTemplateDao = DalOperationsFactory.getDalTableOperations(CpnActivityTemplate.class);

    protected DalTableOperations<CpnUseactivity> useActivityDao = DalOperationsFactory.getDalTableOperations(CpnUseactivity.class);

    protected DalTableOperations<CpnActivitycalc> activityCalcDao = DalOperationsFactory.getDalTableOperations(CpnActivitycalc.class);

    public CpnActivityconfig queryActivityByConfigId(Long configId) {
        try {
            return activityConfigDao.queryByPk(configId, new DalHints());
        } catch (Exception e) {
            return null;
        }
    }

    public CpnActivityTemplate queryActivityTemp(Long tempId) {
        try {
            return activityTemplateDao.queryByPk(tempId, new DalHints());
        } catch (Exception e) {
            return null;
        }
    }

    public int updateActivityByEntity(CpnActivityconfig entity) throws Exception {
        return activityConfigDao.update(new DalHints(), entity);
    }

    public List<CpnUseactivity> getUseActivityList(Long configId, Long orderId, String userId) throws SQLException {
        String sql = "SELECT * FROM cpn_useactivity WHERE 1 = 1 AND ConfigId = ? AND OrderID = ? AND UID = ? ORDER BY DataChange_LastTime DESC ";
        return useActivityDao.query(sql, new DalHints(), SQLResult.type(CpnUseactivity.class), configId, orderId, userId);
    }

    public int insertUseActivity(CpnUseactivity item) throws SQLException {
        return useActivityDao.insert(new DalHints(), item);
    }

    public int updateUseActivity(CpnUseactivity item) throws SQLException {
        return useActivityDao.update(new DalHints(), item);
    }

    public CpnActivitycalc queryCalcByCalcKey(String calcKey) throws SQLException {
        CpnActivitycalc calc = null;
        String sql = "SELECT * FROM cpn_activitycalc WHERE CalcKey = ? ORDER BY DataChange_LastTime DESC ";
        List<CpnActivitycalc> calcList = activityCalcDao.query(sql, new DalHints(), SQLResult.type(CpnActivitycalc.class), calcKey);
        if (CollectionUtils.isNotEmpty(calcList)) {
            calc = calcList.get(0);
        }
        return calc;
    }

    public int insertActivityCalc(CpnActivitycalc calc) throws SQLException {
        return activityCalcDao.insert(new DalHints(), calc);
    }

    public int updateActivityCalc(CpnActivitycalc calc) throws SQLException {
        return activityCalcDao.update(new DalHints(), calc);
    }

    // 查询活动使用次数和使用总额
    public CalcActivityItem queryActivityNumAndAmount(Long configId) throws SQLException {
        CalcActivityItem item = null;
        String sql = "select Count(FlowID) AS totalNum, SUM(ActivityAmount) AS totalAmount from cpn_useactivity WHERE ConfigId = ? AND Status = 0 ";
        List<CalcActivityItem> list = activityCalcDao.query(sql, new DalHints(), SQLResult.type(CalcActivityItem.class), configId);
        if (CollectionUtils.isNotEmpty(list)) {
            item = list.get(0);
        }
        return item;
    }

}
