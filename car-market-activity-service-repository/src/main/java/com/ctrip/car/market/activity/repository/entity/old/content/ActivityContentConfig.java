package com.ctrip.car.market.activity.repository.entity.old.content;

import java.util.List;

public class ActivityContentConfig {

    private Integer minEarlyHours;

    private Integer maxEarlyHours;

    private List<StockItem> vehicleActivityStock;

    private List<StockItem> vehicleLeftStock;

    private String holidayTimes;

    //0普通门店 , 1机场门店 , 3高铁/火车站门店  ,2其他
    private List<Integer> vendorStoreTypeList;

    private String activityDesc;

    //价格类型 0-不限 1-现付 2-预付 3-套餐价 原始需求一嗨
    private List<Integer> priceType;

    //0-不限 1-仅支持上门  2-仅支持非上门
    private Integer onSiteService;

    //0-严格区间 1-广义区间
    private Integer sectionType;

    public Integer getMinEarlyHours() {
        return minEarlyHours;
    }

    public void setMinEarlyHours(Integer minEarlyHours) {
        this.minEarlyHours = minEarlyHours;
    }

    public Integer getOnSiteService() {
        return onSiteService;
    }

    public void setOnSiteService(Integer onSiteService) {
        this.onSiteService = onSiteService;
    }

    public String getHolidayTimes() {
        return holidayTimes;
    }

    public List<Integer> getVendorStoreTypeList() {
        return vendorStoreTypeList;
    }

    public void setVendorStoreTypeList(List<Integer> vendorStoreTypeList) {
        this.vendorStoreTypeList = vendorStoreTypeList;
    }

    public void setHolidayTimes(String holidayTimes) {
        this.holidayTimes = holidayTimes;
    }

    public String getActivityDesc() {
        return activityDesc;
    }

    public List<Integer> getPriceType() {
        return priceType;
    }

    public void setPriceType(List<Integer> priceType) {
        this.priceType = priceType;
    }

    public void setActivityDesc(String activityDesc) {
        this.activityDesc = activityDesc;
    }

    public Integer getMaxEarlyHours() {
        return maxEarlyHours;
    }

    public void setMaxEarlyHours(Integer maxEarlyHours) {
        this.maxEarlyHours = maxEarlyHours;
    }

    public List<StockItem> getVehicleActivityStock() {
        return vehicleActivityStock;
    }

    public void setVehicleActivityStock(List<StockItem> vehicleActivityStock) {
        this.vehicleActivityStock = vehicleActivityStock;
    }

    public List<StockItem> getVehicleLeftStock() {
        return vehicleLeftStock;
    }

    public void setVehicleLeftStock(List<StockItem> vehicleLeftStock) {
        this.vehicleLeftStock = vehicleLeftStock;
    }

    public Integer getSectionType() {
        return sectionType;
    }

    public void setSectionType(Integer sectionType) {
        this.sectionType = sectionType;
    }

    public static class StockItem {
        // 起始时间
        private Integer vehicleId;

        // 结束时间
        private Integer limitNumber;

        public Integer getVehicleId() {
            return vehicleId;
        }

        public void setVehicleId(Integer vehicleId) {
            this.vehicleId = vehicleId;
        }

        public Integer getLimitNumber() {
            return limitNumber;
        }

        public void setLimitNumber(Integer limitNumber) {
            this.limitNumber = limitNumber;
        }
    }

}

