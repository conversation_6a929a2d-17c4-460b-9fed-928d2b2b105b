package com.ctrip.car.market.activity.repository.entity;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-09-19
 */
@Entity
@Database(name = "carmarketingdb_dalcluster")
@Table(name = "act_ctriptempinfo")
public class ActCtriptempinfo implements DalPojo {

    /**
     * 主键
     */
    @Id
    @Column(name = "tmpId")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long tmpId;

    /**
     * 1
     */
    @Column(name = "templateType")
    @Type(value = Types.INTEGER)
    private Integer templateType;

    /**
     * 活动名称
     */
    @Column(name = "name")
    @Type(value = Types.VARCHAR)
    private String name;

    /**
     * 备注
     */
    @Column(name = "remark")
    @Type(value = Types.VARCHAR)
    private String remark;

    /**
     * 标签ID
     */
    @Column(name = "labelId")
    @Type(value = Types.INTEGER)
    private Integer labelId;

    /**
     * 优先级，越小优先级越高
     */
    @Column(name = "priority")
    @Type(value = Types.TINYINT)
    private Integer priority;

    /**
     * 报名开始时间
     */
    @Column(name = "registerStartTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp registerStartTime;

    /**
     * 报名结束时间
     */
    @Column(name = "registerEndTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp registerEndTime;

    /**
     * 活动开始时间
     */
    @Column(name = "activityStartTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp activityStartTime;

    /**
     * 活动结束时间
     */
    @Column(name = "activityEndTime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp activityEndTime;

    /**
     * 排除时间
     */
    @Column(name = "excludeDate")
    @Type(value = Types.VARCHAR)
    private String excludeDate;

    /**
     * 重复周期
     */
    @Column(name = "repetitionPeriod")
    @Type(value = Types.VARCHAR)
    private String repetitionPeriod;

    /**
     * json格式内容
     */
    @Column(name = "tempContent")
    @Type(value = Types.VARCHAR)
    private String tempContent;

    /**
     * 创建人
     */
    @Column(name = "createUser")
    @Type(value = Types.VARCHAR)
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 修改人
     */
    @Column(name = "modifyUser")
    @Type(value = Types.VARCHAR)
    private String modifyUser;

    /**
     * 优惠折扣方式
     */
    @Column(name = "deductionType")
    @Type(value = Types.INTEGER)
    private Integer deductionType;

    /**
     * 成本承担方
     */
    @Column(name = "costShare")
    @Type(value = Types.INTEGER)
    private Integer costShare;

    /**
     * 活动状态，0已创建
     */
    @Column(name = "status")
    @Type(value = Types.TINYINT)
    private Integer status;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 活动分组id
     */
    @Column(name = "groupId")
    @Type(value = Types.INTEGER)
    private Integer groupId;

    /**
     * 是否支持自动报名
     */
    @Column(name = "autoSignUp")
    @Type(value = Types.BIT)
    private Boolean autoSignUp;

    /**
     * 优惠计算base
     */
    @Column(name = "discountBase")
    @Type(value = Types.INTEGER)
    private Integer discountBase;

    /**
     * 是否支持修改订单
     */
    @Column(name = "supportModifyOrder")
    @Type(value = Types.INTEGER)
    private Integer supportModifyOrder;

    /**
     * 是否需要报名
     */
    @Column(name = "needRegister")
    @Type(value = Types.INTEGER)
    private Integer needRegister;

    public Long getTmpId() {
        return tmpId;
    }

    public void setTmpId(Long tmpId) {
        this.tmpId = tmpId;
    }

    public Integer getTemplateType() {
        return templateType;
    }

    public void setTemplateType(Integer templateType) {
        this.templateType = templateType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getLabelId() {
        return labelId;
    }

    public void setLabelId(Integer labelId) {
        this.labelId = labelId;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Timestamp getRegisterStartTime() {
        return registerStartTime;
    }

    public void setRegisterStartTime(Timestamp registerStartTime) {
        this.registerStartTime = registerStartTime;
    }

    public Timestamp getRegisterEndTime() {
        return registerEndTime;
    }

    public void setRegisterEndTime(Timestamp registerEndTime) {
        this.registerEndTime = registerEndTime;
    }

    public Timestamp getActivityStartTime() {
        return activityStartTime;
    }

    public void setActivityStartTime(Timestamp activityStartTime) {
        this.activityStartTime = activityStartTime;
    }

    public Timestamp getActivityEndTime() {
        return activityEndTime;
    }

    public void setActivityEndTime(Timestamp activityEndTime) {
        this.activityEndTime = activityEndTime;
    }

    public String getExcludeDate() {
        return excludeDate;
    }

    public void setExcludeDate(String excludeDate) {
        this.excludeDate = excludeDate;
    }

    public String getRepetitionPeriod() {
        return repetitionPeriod;
    }

    public void setRepetitionPeriod(String repetitionPeriod) {
        this.repetitionPeriod = repetitionPeriod;
    }

    public String getTempContent() {
        return tempContent;
    }

    public void setTempContent(String tempContent) {
        this.tempContent = tempContent;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public Integer getDeductionType() {
        return deductionType;
    }

    public void setDeductionType(Integer deductionType) {
        this.deductionType = deductionType;
    }

    public Integer getCostShare() {
        return costShare;
    }

    public void setCostShare(Integer costShare) {
        this.costShare = costShare;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public Boolean getAutoSignUp() {
        return autoSignUp;
    }

    public void setAutoSignUp(Boolean autoSignUp) {
        this.autoSignUp = autoSignUp;
    }

    public Integer getDiscountBase() {
        return discountBase;
    }

    public void setDiscountBase(Integer discountBase) {
        this.discountBase = discountBase;
    }

    public Integer getSupportModifyOrder() {
        return supportModifyOrder;
    }

    public void setSupportModifyOrder(Integer supportModifyOrder) {
        this.supportModifyOrder = supportModifyOrder;
    }

    public Integer getNeedRegister() {
        return needRegister;
    }

    public void setNeedRegister(Integer needRegister) {
        this.needRegister = needRegister;
    }
}
