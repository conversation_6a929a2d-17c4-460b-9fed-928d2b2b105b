package com.ctrip.car.market.activity.repository.entity.old.enums;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/15. 0已创建，1生效，2禁用，3已过期，99 删除
 */
public enum CarActivityStatusEnum {
    CREATED(0),
    GO_VALID(1),
    OUT_OF_DATE(2),
    DISABLED(2),
    DELETED(99);

    private final Integer code;

    CarActivityStatusEnum(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public static CarActivityStatusEnum findByCode(Integer code) {
        switch (code) {
            case 0:
                return CREATED;
            case 1:
                return GO_VALID;
            case 2:
                return OUT_OF_DATE;
            case 3:
                return DISABLED;
            case 99:
                return DELETED;
            default:
                return null;
        }
    }
}
