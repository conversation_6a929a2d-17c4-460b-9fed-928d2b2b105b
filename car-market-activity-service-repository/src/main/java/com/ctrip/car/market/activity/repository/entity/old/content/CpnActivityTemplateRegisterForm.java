package com.ctrip.car.market.activity.repository.entity.old.content;

import com.ctrip.car.market.common.util.Period;

import java.math.BigDecimal;
import java.util.List;

public class CpnActivityTemplateRegisterForm {

    private List<Integer> showLocations;

    private List<Integer> userType;

    private List<Integer> memberType;

    private List<String> vendors;

    private Boolean vendorsReverse;

    private Period activityTime;

    private List<String> startTimeInDay;

    private List<Integer> durationTime;

    private Period usePeriod;

    private List<Integer> activityPersistTime;

    private List<Integer> vehicleGroup;

    private Boolean referToStore;

    private Boolean limitRentalDay;

    private CpnActivityTemplateRegisterForm.Limit rentalDayLimit;

    private Integer tenancyLimit;

    private CpnActivityTemplateRegisterForm.Limit tenancyLimitOfDay;

    private CpnActivityTemplateRegisterForm.Limit tenancyLimitOfHour;

    private List<Integer> newer;

    private List<Integer> discountPayType;

    private List<Integer> discountType;

    private CpnActivityTemplateRegisterForm.DiscountTypeDetail discountTypeDetail;

    private Boolean notAllowedOverFirstRentalFee;

    private Integer feeType;

    private Boolean isShareCoupon;

    private List<Integer> channelIds;

    private Integer groupRelation;

    private Integer sectionType;

    private CpnActivityTemplateRegisterForm.ShareDetailDTO shareDetailDTO;

    private Integer sortIndex;

    private CpnActivityTemplateRegisterForm.ProvinceInfoDTO provinceInfoDTO;

    private CpnActivityTemplateRegisterForm.CityInfoDTO cityInfoDTO;

    public List<Integer> getShowLocations() {
        return showLocations;
    }

    public void setShowLocations(List<Integer> showLocations) {
        this.showLocations = showLocations;
    }

    public List<Integer> getUserType() {
        return userType;
    }

    public void setUserType(List<Integer> userType) {
        this.userType = userType;
    }

    public List<Integer> getMemberType() {
        return memberType;
    }

    public void setMemberType(List<Integer> memberType) {
        this.memberType = memberType;
    }

    public List<String> getVendors() {
        return vendors;
    }

    public void setVendors(List<String> vendors) {
        this.vendors = vendors;
    }

    public Boolean getVendorsReverse() {
        return vendorsReverse;
    }

    public void setVendorsReverse(Boolean vendorsReverse) {
        this.vendorsReverse = vendorsReverse;
    }

    public Period getActivityTime() {
        return activityTime;
    }

    public void setActivityTime(Period activityTime) {
        this.activityTime = activityTime;
    }

    public List<String> getStartTimeInDay() {
        return startTimeInDay;
    }

    public void setStartTimeInDay(List<String> startTimeInDay) {
        this.startTimeInDay = startTimeInDay;
    }

    public List<Integer> getDurationTime() {
        return durationTime;
    }

    public void setDurationTime(List<Integer> durationTime) {
        this.durationTime = durationTime;
    }

    public Period getUsePeriod() {
        return usePeriod;
    }

    public void setUsePeriod(Period usePeriod) {
        this.usePeriod = usePeriod;
    }

    public List<Integer> getActivityPersistTime() {
        return activityPersistTime;
    }

    public void setActivityPersistTime(List<Integer> activityPersistTime) {
        this.activityPersistTime = activityPersistTime;
    }

    public List<Integer> getVehicleGroup() {
        return vehicleGroup;
    }

    public void setVehicleGroup(List<Integer> vehicleGroup) {
        this.vehicleGroup = vehicleGroup;
    }

    public Boolean getReferToStore() {
        return referToStore;
    }

    public void setReferToStore(Boolean referToStore) {
        this.referToStore = referToStore;
    }

    public Boolean getLimitRentalDay() {
        return limitRentalDay;
    }

    public void setLimitRentalDay(Boolean limitRentalDay) {
        this.limitRentalDay = limitRentalDay;
    }

    public Limit getRentalDayLimit() {
        return rentalDayLimit;
    }

    public void setRentalDayLimit(Limit rentalDayLimit) {
        this.rentalDayLimit = rentalDayLimit;
    }

    public Integer getTenancyLimit() {
        return tenancyLimit;
    }

    public void setTenancyLimit(Integer tenancyLimit) {
        this.tenancyLimit = tenancyLimit;
    }

    public Limit getTenancyLimitOfDay() {
        return tenancyLimitOfDay;
    }

    public void setTenancyLimitOfDay(Limit tenancyLimitOfDay) {
        this.tenancyLimitOfDay = tenancyLimitOfDay;
    }

    public Limit getTenancyLimitOfHour() {
        return tenancyLimitOfHour;
    }

    public void setTenancyLimitOfHour(Limit tenancyLimitOfHour) {
        this.tenancyLimitOfHour = tenancyLimitOfHour;
    }

    public List<Integer> getNewer() {
        return newer;
    }

    public void setNewer(List<Integer> newer) {
        this.newer = newer;
    }

    public List<Integer> getDiscountPayType() {
        return discountPayType;
    }

    public void setDiscountPayType(List<Integer> discountPayType) {
        this.discountPayType = discountPayType;
    }

    public List<Integer> getDiscountType() {
        return discountType;
    }

    public void setDiscountType(List<Integer> discountType) {
        this.discountType = discountType;
    }

    public DiscountTypeDetail getDiscountTypeDetail() {
        return discountTypeDetail;
    }

    public void setDiscountTypeDetail(DiscountTypeDetail discountTypeDetail) {
        this.discountTypeDetail = discountTypeDetail;
    }

    public Boolean getNotAllowedOverFirstRentalFee() {
        return notAllowedOverFirstRentalFee;
    }

    public void setNotAllowedOverFirstRentalFee(Boolean notAllowedOverFirstRentalFee) {
        this.notAllowedOverFirstRentalFee = notAllowedOverFirstRentalFee;
    }

    public Integer getFeeType() {
        return feeType;
    }

    public void setFeeType(Integer feeType) {
        this.feeType = feeType;
    }

    public Boolean isShareCoupon() {
        return isShareCoupon;
    }

    public void setShareCoupon(Boolean shareCoupon) {
        isShareCoupon = shareCoupon;
    }

    public List<Integer> getChannelIds() {
        return channelIds;
    }

    public void setChannelIds(List<Integer> channelIds) {
        this.channelIds = channelIds;
    }

    public Integer getGroupRelation() {
        return groupRelation;
    }

    public void setGroupRelation(Integer groupRelation) {
        this.groupRelation = groupRelation;
    }

    public Integer getSectionType() {
        return sectionType;
    }

    public void setSectionType(Integer sectionType) {
        this.sectionType = sectionType;
    }

    public ShareDetailDTO getShareDetailDTO() {
        return shareDetailDTO;
    }

    public void setShareDetailDTO(ShareDetailDTO shareDetailDTO) {
        this.shareDetailDTO = shareDetailDTO;
    }

    public Integer getSortIndex() {
        return sortIndex;
    }

    public void setSortIndex(Integer sortIndex) {
        this.sortIndex = sortIndex;
    }

    public ProvinceInfoDTO getProvinceInfoDTO() {
        return provinceInfoDTO;
    }

    public void setProvinceInfoDTO(ProvinceInfoDTO provinceInfoDTO) {
        this.provinceInfoDTO = provinceInfoDTO;
    }

    public CityInfoDTO getCityInfoDTO() {
        return cityInfoDTO;
    }

    public void setCityInfoDTO(CityInfoDTO cityInfoDTO) {
        this.cityInfoDTO = cityInfoDTO;
    }

    public static class CityInfo {
        private Long cityId;

        private String cityName;

        public CityInfo() {
        }

        public Long getCityId() {
            return this.cityId;
        }

        public void setCityId(Long cityId) {
            this.cityId = cityId;
        }

        public String getCityName() {
            return this.cityName;
        }

        public void setCityName(String cityName) {
            this.cityName = cityName;
        }
    }

    public static class CityInfoDTO {
        private List<CpnActivityTemplateRegisterForm.CityInfo> cityInfos;

        private Boolean exclude;

        public CityInfoDTO() {
        }

        public List<CpnActivityTemplateRegisterForm.CityInfo> getCityInfos() {
            return this.cityInfos;
        }

        public void setCityInfos(List<CpnActivityTemplateRegisterForm.CityInfo> cityInfos) {
            this.cityInfos = cityInfos;
        }

        public Boolean isExclude() {
            return this.exclude;
        }

        public void setExclude(Boolean exclude) {
            this.exclude = exclude;
        }
    }

    public static class ProvinceInfo {
        private Long provinceId;

        private String provinceName;

        public ProvinceInfo() {
        }

        public Long getProvinceId() {
            return this.provinceId;
        }

        public void setProvinceId(Long provinceId) {
            this.provinceId = provinceId;
        }

        public String getProvinceName() {
            return this.provinceName;
        }

        public void setProvinceName(String provinceName) {
            this.provinceName = provinceName;
        }
    }

    public static class ProvinceInfoDTO {
        private List<CpnActivityTemplateRegisterForm.ProvinceInfo> provinceIds;

        private Boolean exclude;

        public ProvinceInfoDTO() {
        }

        public List<CpnActivityTemplateRegisterForm.ProvinceInfo> getProvinceIds() {
            return this.provinceIds;
        }

        public void setProvinceIds(List<CpnActivityTemplateRegisterForm.ProvinceInfo> provinceIds) {
            this.provinceIds = provinceIds;
        }

        public Boolean isExclude() {
            return this.exclude;
        }

        public void setExclude(Boolean exclude) {
            this.exclude = exclude;
        }
    }

    public static class ShareDetailDTO {
        private BigDecimal percentage; //shareType=0百分比

        private BigDecimal fixedAmount; //shareType=1固定金额

        private BigDecimal realAmount; //真实分摊金额

        private Integer costType; //成本类型 0-活动优惠金额

        private Integer costShare; //成本分摊方 0-供应商 1-携程

        private Integer shareType; //分摊方式 0-百分比 1-金额

        public ShareDetailDTO() {
        }

        public BigDecimal getPercentage() {
            return this.percentage;
        }

        public void setPercentage(BigDecimal percentage) {
            this.percentage = percentage;
        }

        public BigDecimal getFixedAmount() {
            return this.fixedAmount;
        }

        public void setFixedAmount(BigDecimal fixedAmount) {
            this.fixedAmount = fixedAmount;
        }

        public BigDecimal getRealAmount() {
            return realAmount;
        }

        public void setRealAmount(BigDecimal realAmount) {
            this.realAmount = realAmount;
        }

        public Integer getCostType() {
            return this.costType;
        }

        public void setCostType(Integer costType) {
            this.costType = costType;
        }

        public Integer getCostShare() {
            return this.costShare;
        }

        public void setCostShare(Integer costShare) {
            this.costShare = costShare;
        }

        public Integer getShareType() {
            return this.shareType;
        }

        public void setShareType(Integer shareType) {
            this.shareType = shareType;
        }
    }

    public static class PeriodDiscount {
        private Integer enough;

        private Integer minus;

        public PeriodDiscount() {
        }

        public Integer getEnough() {
            return this.enough;
        }

        public void setEnough(Integer enough) {
            this.enough = enough;
        }

        public Integer getMinus() {
            return this.minus;
        }

        public void setMinus(Integer minus) {
            this.minus = minus;
        }
    }

    public static class DiscountTypeDetail {
        private List<Integer> fixedSelect;

        private List<Integer> discountSelect;

        private CpnActivityTemplateRegisterForm.Limit dayLimit;

        private List<CpnActivityTemplateRegisterForm.PeriodDiscount> periodDiscounts;

        private List<Integer> firstRentalDayRebate;

        private List<Integer> firstRentalDayFix;

        public DiscountTypeDetail() {
        }

        public List<Integer> getFirstRentalDayRebate() {
            return this.firstRentalDayRebate;
        }

        public void setFirstRentalDayRebate(List<Integer> firstRentalDayRebate) {
            this.firstRentalDayRebate = firstRentalDayRebate;
        }

        public List<Integer> getFirstRentalDayFix() {
            return this.firstRentalDayFix;
        }

        public void setFirstRentalDayFix(List<Integer> firstRentalDayFix) {
            this.firstRentalDayFix = firstRentalDayFix;
        }

        public List<Integer> getFixedSelect() {
            return this.fixedSelect;
        }

        public void setFixedSelect(List<Integer> fixedSelect) {
            this.fixedSelect = fixedSelect;
        }

        public List<Integer> getDiscountSelect() {
            return this.discountSelect;
        }

        public void setDiscountSelect(List<Integer> discountSelect) {
            this.discountSelect = discountSelect;
        }

        public CpnActivityTemplateRegisterForm.Limit getDayLimit() {
            return this.dayLimit;
        }

        public void setDayLimit(CpnActivityTemplateRegisterForm.Limit dayLimit) {
            this.dayLimit = dayLimit;
        }

        public List<CpnActivityTemplateRegisterForm.PeriodDiscount> getPeriodDiscounts() {
            return this.periodDiscounts;
        }

        public void setPeriodDiscounts(List<CpnActivityTemplateRegisterForm.PeriodDiscount> periodDiscounts) {
            this.periodDiscounts = periodDiscounts;
        }
    }

    public static class Limit {
        private Integer floor;

        private Integer upline;

        public Limit() {
        }

        public static Boolean enable(CpnActivityTemplateRegisterForm.Limit limit) {
            if (limit == null) {
                return false;
            } else {
                return limit.floor != null && limit.floor > 0 || limit.upline != null && limit.upline > 0;
            }
        }

        public Integer getFloor() {
            return this.floor;
        }

        public void setFloor(Integer floor) {
            this.floor = floor;
        }

        public Integer getUpline() {
            return this.upline;
        }

        public void setUpline(Integer upline) {
            this.upline = upline;
        }
    }

}
