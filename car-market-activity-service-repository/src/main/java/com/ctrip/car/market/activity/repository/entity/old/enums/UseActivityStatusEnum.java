package com.ctrip.car.market.activity.repository.entity.old.enums;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/20.
 */
public enum UseActivityStatusEnum {
    //已使用
    USED(0),
    //取消使用
    CANCELED(1);

    private final Integer code;

    UseActivityStatusEnum(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public static UseActivityStatusEnum findByCode(Integer code) {
        switch (code) {
            case 0:
                return USED;
            case 1:
                return CANCELED;
            default:
                return null;
        }
    }
}
