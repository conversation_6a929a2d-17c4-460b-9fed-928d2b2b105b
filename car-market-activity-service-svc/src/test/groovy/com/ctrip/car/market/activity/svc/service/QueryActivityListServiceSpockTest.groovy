package com.ctrip.car.market.activity.svc.service

import com.ctrip.car.market.activity.contract.BaseRequest
import com.ctrip.car.market.activity.contract.QueryActivityListRequestType
import com.ctrip.car.market.activity.contract.QueryActivityListResponseType
import com.ctrip.car.market.activity.contract.dto.ProductCondition
import com.ctrip.car.market.activity.svc.bo.HolidayDto
import com.ctrip.car.market.activity.svc.bo.SourceFromMappingDto
import com.ctrip.car.market.activity.svc.bo.UserGroupDTO
import com.ctrip.car.market.activity.svc.cache.neww.ActivityCityCache
import com.ctrip.car.market.activity.svc.cache.neww.ActivityInfoCache
import com.ctrip.car.market.activity.svc.cache.neww.ActivityTempCache
import com.ctrip.car.market.activity.svc.config.BaseConfig
import com.ctrip.car.market.activity.svc.proxy.CarCommodityServiceProxy
import com.ctrip.car.market.activity.svc.proxy.CdpServiceProxy
import com.ctrip.car.market.activity.svc.proxy.QueryQunarUserLabelProxy
import com.ctrip.car.market.activity.svc.util.ActivityListConditionUtil
import com.ctrip.car.market.activity.svc.util.FilterSecretUtils
import com.ctrip.car.market.common.entity.act.ActivityTempContent
import com.ctrip.car.market.common.entity.act.UserCondition
import com.ctrip.car.market.job.common.entity.ActInfoDO
import com.ctrip.car.market.job.common.entity.ActTempInfoDO
import com.google.common.collect.Sets
import org.apache.commons.collections.CollectionUtils
import org.checkerframework.checker.units.qual.A
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification

import java.sql.Timestamp
import java.util.function.Predicate


class QueryActivityListServiceSpockTest extends Specification {

    private activityTempCache = Mock(ActivityTempCache)

    private activityInfoCache = Mock(ActivityInfoCache)

    private activityListConditionUtil = Mock(ActivityListConditionUtil)

    private baseConfig = Mock(BaseConfig)

    private queryQunarUserLabelProxy = Mock(QueryQunarUserLabelProxy)

    private activityCityCache = Mock(ActivityCityCache)

    private testInstance = new QueryActivityListNewService(
            activityTempCache: activityTempCache,
            activityInfoCache: activityInfoCache,
            activityListConditionUtil: activityListConditionUtil,
            baseConfig: baseConfig,
            queryQunarUserLabelProxy: queryQunarUserLabelProxy,
            activityCityCache: activityCityCache
    )

    def test_checkRequest() {
        def testMethod = testInstance.getClass().getDeclaredMethod("checkRequest", QueryActivityListRequestType.class, QueryActivityListResponseType.class)
        testMethod.setAccessible(true)

        expect:
        testMethod.invoke(testInstance, request as QueryActivityListRequestType, new QueryActivityListResponseType()) == result

        where:
        request                                                                              || result
        new QueryActivityListRequestType()                                                   || false
        new QueryActivityListRequestType(baseRequest: new BaseRequest())                     || false
        new QueryActivityListRequestType(baseRequest: new BaseRequest()
                , productCondition: new ProductCondition(tenancy: 1, pickUpTime: getTime(1),
                returnTime: getTime(2), cityIds: [1]))                                       || true
    }

    def getTime(int day) {
        Calendar time = Calendar.getInstance()
        time.add(Calendar.DATE, day)
        return time
    }

    def getTime2(int day) {
        Calendar time = Calendar.getInstance()
        time.add(Calendar.DATE, day)
        return new Timestamp(time.getTimeInMillis())
    }

    def getAct() {
        ActInfoDO act = new ActInfoDO()
        act.setTempId(1L)
        act.setId(1L)
        return act
    }

    def getTemp() {
        ActTempInfoDO temp = new ActTempInfoDO()
        temp.setContent(new ActivityTempContent(userCondition: new UserCondition(userAttributes: ["test"])))
        temp.setTmpId(1L)
        temp.setActivityStartTime(getTime2(-10))
        temp.setActivityEndTime(getTime2(10))
        return temp
    }

    def test_getAct() {
        def testMethod = testInstance.getClass().getDeclaredMethod("getAct", QueryActivityListRequestType.class)
        testMethod.setAccessible(true)

        Predicate<ActTempInfoDO> predicate = () -> true
        Predicate<Long> predicateLong = () -> true

        given:
        activityTempCache.getAll() >> tempList
        activityInfoCache.queryActId(_ as Long) >> [1L]

        baseConfig.getSourceFromMapping() >> [new SourceFromMappingDto(sourceFrom: 1, mapping: ["ISD_C_APP"])]
        baseConfig.getChannelActivity(_ as Integer) >> Sets.newHashSet()
        baseConfig.getDistributionChannel() >> Sets.newHashSet()

        activityListConditionUtil.filterSecret(_ as Set<String>) >> predicate
        activityListConditionUtil.filterActivityTime(_ as Calendar) >> predicate
        activityListConditionUtil.filterExcludeData(_ as Calendar) >> predicate
        activityListConditionUtil.filterTenancy(_ as Integer, _ as Boolean) >> predicate
        activityListConditionUtil.filterPickupTime(_ as Calendar, _ as Calendar) >> predicate
        activityListConditionUtil.filterSourceFrom(_ as Integer) >> predicate
        activityListConditionUtil.filterChannel(_ as Integer, _ as Integer, _ as Set<Integer>, _ as Set<Long>) >> predicate
        activityListConditionUtil.filterBlindBox(_ as Boolean) >> predicate
        activityListConditionUtil.filterTakeTimeType(_ as Calendar) >> predicate
        activityListConditionUtil.filterAdvanceTime(_ as Long) >> predicate
        activityListConditionUtil.filterRepetitionPeriod(_ as Calendar) >> predicate
        activityListConditionUtil.filterTempCity(_ as Set<Integer>) >> predicate
        activityListConditionUtil.filterHoliday(_ as HolidayDto, _ as ActTempInfoDO) >> predicateLong
        activityListConditionUtil.filterActCity(_ as Set<Integer>) >> predicateLong

        expect:
        CollectionUtils.isNotEmpty((List<Long>) testMethod.invoke(testInstance, request as QueryActivityListRequestType)) == result

        where:
        tempList    | request                                                                                                                   || result
        []          | new QueryActivityListRequestType()                                                                                        || false

        [getTemp()] | new QueryActivityListRequestType(baseRequest: new BaseRequest(sourceFrom: "ISD_C_APP", channelId: 17671, distributionChannelId: 17671)
                , productCondition: new ProductCondition(tenancy: 1, pickUpTime: getTime(1),
                returnTime: getTime(2), cityIds: [1]))                                                                                          || false
    }

    def test_getCitySet() {
        def testMethod = testInstance.getClass().getDeclaredMethod("getCitySet", List.class)
        testMethod.setAccessible(true)

        expect:
        CollectionUtils.isNotEmpty((Set<Integer>) testMethod.invoke(testInstance, cityIds as List<Integer>)) == result

        where:
        cityIds || result
        []      || false
        [1]     || true
    }

    def "getUserSecret should return correct results"() {
        given:
        def qunarUserTags = [new UserGroupDTO(secret: "attr1"), new UserGroupDTO(secret: "attr3")]
        def qunarSourceFromList = ["ISD_Q_APP"]
        def qunarUserTagMap = ["attr1": "1", "attr3": "0"]

        baseConfig.getQunarUserTags() >> qunarUserTags
        baseConfig.getQunarSourceFromList() >> qunarSourceFromList
        queryQunarUserLabelProxy.queryQunarUid(_ as String , _ as List<String>, _ as Boolean ) >> qunarUserTagMap

        when:
        def result = testInstance.getUserSecret(userId, actTempList, sourceFrom)

        then:
        result == expectedResult

        where:
        userId                | actTempList         | sourceFrom              || expectedResult
        null                     | null                        | null                            ||  [] as Set
        "testUid"           | null                        | null                             ||  [] as Set
        "testUid"           | []                            | null                             ||  [] as Set
        "testUid"           | Arrays.asList(
                new ActTempInfoDO(content: [userCondition: [userAttributes: ["attr1", "attr2"]]]),
                new ActTempInfoDO(content: [userCondition: [userAttributes: ["attr2", "attr3"]]])
        )                      | "ISD_Q_APP"           ||  ["attr1"] as Set
        "testUid"           | Arrays.asList(
                new ActTempInfoDO(content: [userCondition: [userAttributes: ["attr1", "attr2"]]]),
                new ActTempInfoDO(content: [userCondition: [userAttributes: ["attr2", "attr3"]]])
        )                      | "OTHER_SOURCE"  || [] as Set
    }
}
