package com.ctrip.car.market.activity.svc.service

import com.ctrip.car.market.activity.contract.BaseRequest
import com.ctrip.car.market.activity.contract.CalculateActivityRequestType
import com.ctrip.car.market.activity.contract.CalculateActivityResponseType
import com.ctrip.car.market.activity.contract.dto.FeeItem
import com.ctrip.car.market.activity.contract.dto.ProductCondition
import com.ctrip.car.market.activity.contract.dto.ProductPrice
import com.ctrip.car.market.activity.repository.entity.ActCtripactinfo
import com.ctrip.car.market.activity.repository.entity.ActCtriptempinfo
import com.ctrip.car.market.activity.repository.service.ActivityService
import com.ctrip.car.market.activity.svc.bo.Activity
import com.ctrip.car.market.activity.svc.bo.ActivityGroupConfig
import com.ctrip.car.market.activity.svc.config.BaseConfig
import com.ctrip.car.market.activity.svc.mapper.ActivityMapper
import com.ctrip.car.market.activity.svc.util.JsonUtil
import com.ctrip.car.market.common.entity.act.ActivityTempContent
import com.ctrip.car.market.common.entity.act.DeductionStrategy
import com.ctrip.car.market.common.entity.act.SupplierCondition
import com.ctrip.car.market.common.entity.act.UserCondition
import com.ctrip.car.market.common.enums.ActivityGroup
import com.ctrip.car.market.job.common.entity.ActInfoDO
import com.ctrip.car.market.job.common.entity.ActTempInfoDO
import com.google.common.collect.Sets
import spock.lang.Specification

import java.lang.reflect.Method
import java.math.BigDecimal
import java.math.RoundingMode
import java.sql.Timestamp

class CalculateActivityServiceSpockTest extends Specification {

    def service = Mock(ActivityService)

    def baseConfig = Mock(BaseConfig)

    def activityMapper = Mock(ActivityMapper)

    def activityGroupConfig = Mock(ActivityGroupConfig)

    private testInstance = new CalculateActivityService(
            service: service,
            baseConfig: baseConfig,
            activityMapper: activityMapper,
            activityGroupConfig: activityGroupConfig
    )

    def test_checkRequest() {
        def testMethod = testInstance.getClass().getDeclaredMethod("checkRequest", CalculateActivityRequestType.class, CalculateActivityResponseType.class)
        testMethod.setAccessible(true)

        expect:
        ((boolean) testMethod.invoke(testInstance, request as CalculateActivityRequestType, new CalculateActivityResponseType())) == result

        where:
        request                                                                                                                                        || result
        new CalculateActivityRequestType()                                                                                                             || false
        new CalculateActivityRequestType(baseRequest: new BaseRequest())                                                                               || false
        new CalculateActivityRequestType(baseRequest: new BaseRequest(), activityId: 1L)                                                               || false
        new CalculateActivityRequestType(baseRequest: new BaseRequest(), activityId: 2000001L)                                                         || false
        new CalculateActivityRequestType(baseRequest: new BaseRequest(), activityId: 2000001L, productCondition: new ProductCondition())               || false
        new CalculateActivityRequestType(baseRequest: new BaseRequest(), activityId: 2000001L,
                productCondition: new ProductCondition(cityIds: [1]))                                                                                  || false

        new CalculateActivityRequestType(baseRequest: new BaseRequest(), activityId: 2000001L,
                productCondition: new ProductCondition(cityIds: [1], vendorId: 1L))                                                                    || false

        new CalculateActivityRequestType(baseRequest: new BaseRequest(), activityId: 2000001L,
                productCondition: new ProductCondition(cityIds: [1], vendorId: 1L, storeId: 1L))                                                       || false

        new CalculateActivityRequestType(baseRequest: new BaseRequest(), activityId: 2000001L,
                productCondition: new ProductCondition(cityIds: [1], vendorId: 1L, storeId: 1L, standardProductId: 1L))                                || false

        new CalculateActivityRequestType(baseRequest: new BaseRequest(), activityId: 2000001L,
                productCondition: new ProductCondition(cityIds: [1], vendorId: 1L, storeId: 1L, standardProductId: 1L,
                        payMode: 2, vehicleGroupId: 2L))                                                                                               || false

        new CalculateActivityRequestType(baseRequest: new BaseRequest(), activityId: 2000001L,
                productCondition: new ProductCondition(cityIds: [1], vendorId: 1L, storeId: 1L, standardProductId: 1L,
                        payMode: 2, vehicleGroupId: 2L), productPrice: new ProductPrice(feeList: [new FeeItem(code: "1000")]))                         || false

        new CalculateActivityRequestType(baseRequest: new BaseRequest(), activityId: 2000001L,
                productCondition: new ProductCondition(cityIds: [1], vendorId: 1L, storeId: 1L, standardProductId: 1L,
                        payMode: 2, vehicleGroupId: 2L), productPrice: new ProductPrice(feeList: [new FeeItem(code: "1001", amount: BigDecimal.ONE)])) || true
    }

    def test_doBusiness() {
        given:
        service.queryByPk(_ as Long) >> new ActCtripactinfo(tempId: 1L, vendorId: 1L, startTime: getTime2(-10), endTime: getTime2(10), id: 200001L)
        service.queryByTempId(_ as Long) >> new ActCtriptempinfo(tmpId: 1L, groupId: 100001, tempContent: JsonUtil.toString(new ActivityTempContent(supplierCondition: new SupplierCondition(payModes: [2]), deductionStrategy: new DeductionStrategy(deductionType: 1, deductionAmount: BigDecimal.ONE),
                userCondition: new UserCondition())))
        service.queryActivityCity(_ as Long) >> []
        service.queryActivityReturnCity(_ as Long) >> []
        service.queryActivityProduct(_ as Long) >> []
        baseConfig.getHolidayList() >> []
        baseConfig.getChannelActivity(_ as Integer) >> Sets.newHashSet()
        baseConfig.getSourceFromMapping() >> []
        activityMapper.toAct(_ as ActCtripactinfo) >> actInfo
        activityMapper.toTemp(_ as ActCtriptempinfo) >> tmpInfo
        activityGroupConfig.findPriority(_ as Integer) >> 1

        expect:
        Objects.nonNull(testInstance.doBusiness(request as CalculateActivityRequestType).getActivityInfo()) == result

        where:
        tmpInfo                                                                                                                                    | actInfo                                                                                               | request                                                                                                   || result
        new ActTempInfoDO(tmpId: 1L, groupId: 100001, tempContent: JsonUtil.toString(new ActivityTempContent(supplierCondition:
                new SupplierCondition(payModes: [2]), deductionStrategy: new DeductionStrategy(deductionType: 1, deductionAmount: BigDecimal.ONE),
                userCondition: new UserCondition())))                                                                                              | null                                                                                                  | new CalculateActivityRequestType()                                                                        || false

        null                                                                                                                                       | new ActInfoDO(tempId: 1L, vendorId: 1L, startTime: getTime2(-10), endTime: getTime2(10), id: 200001L) | new CalculateActivityRequestType()                                                                        || false

        new ActTempInfoDO(tmpId: 1L, groupId: 100001, tempContent: JsonUtil.toString(new ActivityTempContent(supplierCondition:
                new SupplierCondition(payModes: [2]), deductionStrategy: new DeductionStrategy(deductionType: 1, deductionAmount: BigDecimal.ONE),
                userCondition: new UserCondition())))                                                                                              | new ActInfoDO(tempId: 1L, vendorId: 1L, startTime: getTime2(-10), endTime: getTime2(10), id: 200001L) | new CalculateActivityRequestType()                                                                        || false
        new ActTempInfoDO(tmpId: 1L, groupId: 100001, tempContent: JsonUtil.toString(new ActivityTempContent(supplierCondition:
                new SupplierCondition(payModes: [2]), deductionStrategy: new DeductionStrategy(deductionType: 1, deductionAmount: BigDecimal.ONE),
                userCondition: new UserCondition())))                                                                                              | new ActInfoDO(tempId: 1L, vendorId: 1L, startTime: getTime2(-10), endTime: getTime2(10), id: 200001L) | new CalculateActivityRequestType(baseRequest: new BaseRequest(channelId: 17671, sourceFrom: "ISD_C_APP"),
                productCondition: new ProductCondition(vendorId: 1L, storeId: 1L, pickUpTime: getTime(1), returnTime: getTime(3),
                        cityIds: [1], tenancy: 1, standardProductId: 1L, payMode: 2, vehicleGroupId: 1), activityId: 1L)                                                                                                                                                                                                                                               || false

        new ActTempInfoDO(tmpId: 1L, groupId: 100001, tempContent: JsonUtil.toString(new ActivityTempContent(supplierCondition:
                new SupplierCondition(payModes: [2]), deductionStrategy: new DeductionStrategy(deductionType: 1, deductionAmount: BigDecimal.ONE),
                userCondition: new UserCondition())))                                                                                              | new ActInfoDO(tempId: 1L, vendorId: 1L, startTime: getTime2(-10), endTime: getTime2(10), id: 200001L) | new CalculateActivityRequestType(baseRequest: new BaseRequest(channelId: 17671, sourceFrom: "ISD_C_APP"),
                productCondition: new ProductCondition(vendorId: 1L, storeId: 1L, pickUpTime: getTime(1), returnTime: getTime(3),
                        cityIds: [1], tenancy: 1, standardProductId: 1L, payMode: 2, vehicleGroupId: 1)
                , productPrice: new ProductPrice(rentAmount: BigDecimal.TEN,
                feeList: [new FeeItem(code: "1001", amount: BigDecimal.TEN)]), activityId: 200001L)                                                                                                                                                                                                                                                                    || true
    }

    def getTime(int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, day)
        return calendar
    }

    def getTime2(int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, day)
        return new Timestamp(calendar.getTimeInMillis())
    }
}
