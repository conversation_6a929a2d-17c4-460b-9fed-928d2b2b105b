package com.ctrip.car.market.activity.svc.service

import com.ctrip.car.market.activity.contract.BaseRequest
import com.ctrip.car.market.activity.contract.ValidationActivityRequestType
import com.ctrip.car.market.activity.contract.ValidationActivityResponseType
import com.ctrip.car.market.activity.contract.dto.ActivityPriceItem
import com.ctrip.car.market.activity.contract.dto.FeeItem
import com.ctrip.car.market.activity.contract.dto.ProductCondition
import com.ctrip.car.market.activity.contract.dto.ProductPrice
import com.ctrip.car.market.activity.svc.bo.Activity
import com.ctrip.car.market.activity.svc.bo.ActivityGroupConfig
import com.ctrip.car.market.activity.svc.cache.neww.ActivityCityCache
import com.ctrip.car.market.activity.svc.cache.neww.ActivityInfoCache
import com.ctrip.car.market.activity.svc.cache.neww.ActivityProductCache
import com.ctrip.car.market.activity.svc.cache.neww.ActivityTempCache
import com.ctrip.car.market.activity.svc.cache.neww.ActivityVendorSkuCache
import com.ctrip.car.market.activity.svc.config.BaseConfig
import com.ctrip.car.market.activity.svc.util.FilterSecretUtils
import com.ctrip.car.market.common.entity.act.ActivityTempContent
import com.ctrip.car.market.common.entity.act.DeductionStrategy
import com.ctrip.car.market.common.entity.act.SupplierCondition
import com.ctrip.car.market.common.entity.act.UserCondition
import com.ctrip.car.market.job.common.entity.ActInfoDO
import com.ctrip.car.market.job.common.entity.ActTempInfoDO
import com.google.common.collect.Sets
import spock.lang.Specification
import spock.lang.Unroll

import java.math.BigDecimal
import java.sql.Timestamp

/**
 * ValidationActivityService的Spock单元测试
 */
class ValidationActivityServiceSpockTest extends Specification {

    // Mock依赖
    def activityInfoCache = Mock(ActivityInfoCache)
    def activityTempCache = Mock(ActivityTempCache)
    def activityCityCache = Mock(ActivityCityCache)
    def activityProductCache = Mock(ActivityProductCache)
    def activityVendorSkuCache = Mock(ActivityVendorSkuCache)
    def baseConfig = Mock(BaseConfig)
    def filterSecretUtils = Mock(FilterSecretUtils)
    def activityGroupConfig = Mock(ActivityGroupConfig)

    // 被测试的服务实例
    ValidationActivityService validationActivityService

    def setup() {
        validationActivityService = new ValidationActivityService()
        validationActivityService.activityInfoCache = activityInfoCache
        validationActivityService.activityTempCache = activityTempCache
        validationActivityService.activityCityCache = activityCityCache
        validationActivityService.activityProductCache = activityProductCache
        validationActivityService.activityVendorSkuCache = activityVendorSkuCache
        validationActivityService.baseConfig = baseConfig
        validationActivityService.filterSecretUtils = filterSecretUtils
        validationActivityService.activityGroupConfig = activityGroupConfig
    }

    /**
     * 测试doBusiness方法 - 请求参数校验失败的场景
     */
    @Unroll
    def "test doBusiness with invalid request parameters"() {
        when:
        def response = validationActivityService.doBusiness(request)

        then:
        response.baseResponse.code != "000000"
        response.baseResponse.message == expectedMessage

        where:
        request                                                                                    || expectedMessage
        new ValidationActivityRequestType()                                                       || "base request is null"
        new ValidationActivityRequestType(baseRequest: new BaseRequest())                        || "activity id is null"
        new ValidationActivityRequestType(
                baseRequest: new BaseRequest(),
                activityId: 1L
        )                                                                                          || "product condition is null"
        new ValidationActivityRequestType(
                baseRequest: new BaseRequest(),
                activityId: 1L,
                productCondition: new ProductCondition()
        )                                                                                          || "city id is null"
    }

    /**
     * 测试doBusiness方法 - 活动不存在的场景
     */
    def "test doBusiness when activity does not exist"() {
        given:
        def request = createValidRequest()
        activityInfoCache.queryById(1L) >> null

        when:
        def response = validationActivityService.doBusiness(request)

        then:
        response.baseResponse.code != "000000"
        response.baseResponse.message == "activity non-existent"
    }

    /**
     * 测试doBusiness方法 - 成功场景
     */
    def "test doBusiness success scenario"() {
        given:
        def request = createValidRequest()
        def actInfo = createMockActInfo()
        def tempInfo = createMockTempInfo()

        // Mock缓存调用
        activityInfoCache.queryById(1L) >> actInfo
        activityTempCache.queryByTmpId(1L) >> tempInfo
        activityCityCache.queryCityByActId(1L) >> [1]
        activityProductCache.queryByActId(1L) >> [1L]
        activityVendorSkuCache.queryByActId(1L) >> [1L]
        activityGroupConfig.findPriority(_) >> 1

        // Mock配置
        baseConfig.getHolidayList() >> []
        baseConfig.getChannelActivity(_) >> Sets.newHashSet()
        baseConfig.getSourceFromMapping() >> []

        // Mock用户过滤
        filterSecretUtils.filterSecret(_, _, _) >> { uid, activities, sourceFrom ->
            return activities
        }

        when:
        def response = validationActivityService.doBusiness(request)

        then:
        response.baseResponse.code == "000000"
        response.activityInfo != null
    }

    /**
     * 测试getAct私有方法
     */
    def "test getAct method"() {
        given:
        def activityIds = [1L, 2L]
        def actInfo1 = createMockActInfo(1L, 1L)
        def actInfo2 = createMockActInfo(2L, 2L)
        def tempInfo1 = createMockTempInfo(1L)
        def tempInfo2 = createMockTempInfo(2L)

        activityInfoCache.queryById(1L) >> actInfo1
        activityInfoCache.queryById(2L) >> actInfo2
        activityTempCache.queryByTmpId(1L) >> tempInfo1
        activityTempCache.queryByTmpId(2L) >> tempInfo2
        activityCityCache.queryCityByActId(_) >> []
        activityProductCache.queryByActId(_) >> []
        activityVendorSkuCache.queryByActId(_) >> []
        activityGroupConfig.findPriority(_) >> 1

        when:
        def method = ValidationActivityService.getDeclaredMethod("getAct", List.class)
        method.setAccessible(true)
        def result = method.invoke(validationActivityService, activityIds) as List<Activity>

        then:
        result.size() == 2
        result[0].act.id == 1L
        result[1].act.id == 2L
    }

    /**
     * 测试checkRequest私有方法
     */
    @Unroll
    def "test checkRequest method with various scenarios"() {
        given:
        def response = new ValidationActivityResponseType()

        when:
        def method = ValidationActivityService.getDeclaredMethod("checkRequest", ValidationActivityRequestType.class, ValidationActivityResponseType.class)
        method.setAccessible(true)
        def result = method.invoke(validationActivityService, request, response) as Boolean

        then:
        result == expectedResult
        if (!expectedResult) {
            response.baseResponse.message == expectedMessage
        }

        where:
        request                                                                                    || expectedResult | expectedMessage
        new ValidationActivityRequestType()                                                       || false          | "base request is null"
        new ValidationActivityRequestType(baseRequest: new BaseRequest())                        || false          | "activity id is null"
        new ValidationActivityRequestType(
                baseRequest: new BaseRequest(),
                activityId: 1L
        )                                                                                          || false          | "product condition is null"
        new ValidationActivityRequestType(
                baseRequest: new BaseRequest(),
                activityId: 1L,
                productCondition: createValidProductCondition()
        )                                                                                          || false          | "product price is null"
        new ValidationActivityRequestType(
                baseRequest: new BaseRequest(),
                activityId: 1L,
                productCondition: createValidProductCondition(),
                productPrice: createValidProductPrice()
        )                                                                                          || true           | null
    }

    /**
     * 测试activityAmountMonitor方法
     */
    def "test activityAmountMonitor method"() {
        given:
        def request = createValidRequest()
        def activity = createMockActivity()
        activity.setActivityAmount(new BigDecimal("50"))
        request.productPrice.orderAmount = new BigDecimal("100")

        baseConfig.getActivityAmountLimit() >> new BigDecimal("3")

        when:
        validationActivityService.activityAmountMonitor(request, [activity])

        then:
        noExceptionThrown()
        1 * baseConfig.getActivityAmountLimit()
    }

    /**
     * 测试doBusiness方法 - 用户过滤失败场景
     */
    def "test doBusiness when user filter fails"() {
        given:
        def request = createValidRequest()
        def actInfo = createMockActInfo()
        def tempInfo = createMockTempInfo()

        // Mock缓存调用
        activityInfoCache.queryById(1L) >> actInfo
        activityTempCache.queryByTmpId(1L) >> tempInfo
        activityCityCache.queryCityByActId(1L) >> [1]
        activityProductCache.queryByActId(1L) >> [1L]
        activityVendorSkuCache.queryByActId(1L) >> [1L]
        activityGroupConfig.findPriority(_) >> 1

        // Mock配置
        baseConfig.getHolidayList() >> []
        baseConfig.getChannelActivity(_) >> Sets.newHashSet()
        baseConfig.getSourceFromMapping() >> []

        // Mock用户过滤返回空列表
        filterSecretUtils.filterSecret(_, _, _) >> []

        when:
        def response = validationActivityService.doBusiness(request)

        then:
        response.baseResponse.code != "000000"
        response.baseResponse.message == "activity verification failed"
    }

    /**
     * 测试doBusiness方法 - 异常处理
     */
    def "test doBusiness exception handling"() {
        given:
        def request = createValidRequest()
        activityInfoCache.queryById(_) >> { throw new RuntimeException("Database error") }

        when:
        def response = validationActivityService.doBusiness(request)

        then:
        response.baseResponse.code != "000000"
        response.baseResponse.message.contains("system error") || response.baseResponse.message.contains("系统错误")
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建有效的请求对象
     */
    private ValidationActivityRequestType createValidRequest() {
        return new ValidationActivityRequestType(
                baseRequest: new BaseRequest(
                        uid: "test_user",
                        channelId: 17671,
                        sourceFrom: "ISD_C_APP"
                ),
                activityId: 1L,
                productCondition: createValidProductCondition(),
                productPrice: createValidProductPrice()
        )
    }

    /**
     * 创建有效的产品条件
     */
    private ProductCondition createValidProductCondition() {
        def calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_MONTH, 1)
        def pickUpTime = calendar

        def calendar2 = Calendar.getInstance()
        calendar2.add(Calendar.DAY_OF_MONTH, 3)
        def returnTime = calendar2

        return new ProductCondition(
                vendorId: 1L,
                storeId: 1L,
                pickUpTime: pickUpTime,
                returnTime: returnTime,
                cityIds: [1],
                tenancy: 3,
                standardProductId: 1L,
                payMode: 2,
                vehicleGroupId: 1
        )
    }

    /**
     * 创建有效的产品价格
     */
    private ProductPrice createValidProductPrice() {
        return new ProductPrice(
                rentAmount: new BigDecimal("300"),
                orderAmount: new BigDecimal("350"),
                firstRentAmount: new BigDecimal("100"),
                dailyPrice: new BigDecimal("100")
        )
    }

    /**
     * 创建模拟的活动信息
     */
    private ActInfoDO createMockActInfo(Long id = 1L, Long tempId = 1L) {
        def calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_MONTH, -1)
        def startTime = new Timestamp(calendar.timeInMillis)

        calendar.add(Calendar.DAY_OF_MONTH, 10)
        def endTime = new Timestamp(calendar.timeInMillis)

        return new ActInfoDO(
                id: id,
                tempId: tempId,
                vendorId: 1L,
                vendorCouponCode: "TEST_COUPON",
                startTime: startTime,
                endTime: endTime,
                status: 1,
                excludeCity: false,
                excludeReturnCity: false,
                excludeProduct: false,
                signUpMode: 0
        )
    }

    /**
     * 创建模拟的活动模板信息
     */
    private ActTempInfoDO createMockTempInfo(Long tmpId = 1L) {
        def content = new ActivityTempContent(
                supplierCondition: new SupplierCondition(
                        payModes: [2]
                ),
                deductionStrategy: new DeductionStrategy(
                        deductionType: 1,
                        deductionAmount: new BigDecimal("50")
                ),
                userCondition: new UserCondition()
        )

        return new ActTempInfoDO(
                tmpId: tmpId,
                name: "测试活动",
                groupId: 1,
                priority: 1,
                content: content,
                supportModifyOrder: 1
        )
    }

    /**
     * 创建模拟的Activity对象
     */
    private Activity createMockActivity() {
        def actInfo = createMockActInfo()
        def tempInfo = createMockTempInfo()
        def citySet = Sets.newHashSet(1)
        def returnCitySet = Sets.newHashSet()
        def productSet = Sets.newHashSet(1L)
        def skuSet = Sets.newHashSet(1L)

        return new Activity(actInfo, tempInfo, citySet, returnCitySet, productSet, skuSet, 1)
    }

    /**
     * 创建指定时间的Calendar对象
     */
    private Calendar createCalendar(int daysFromNow) {
        def calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_MONTH, daysFromNow)
        return calendar
    }

    /**
     * 创建指定时间的Timestamp对象
     */
    private Timestamp createTimestamp(int daysFromNow) {
        def calendar = createCalendar(daysFromNow)
        return new Timestamp(calendar.timeInMillis)
    }
}
