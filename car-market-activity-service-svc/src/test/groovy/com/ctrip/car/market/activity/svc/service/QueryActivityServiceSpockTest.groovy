package com.ctrip.car.market.activity.svc.service

import com.ctrip.car.market.activity.contract.BaseRequest
import com.ctrip.car.market.activity.contract.QueryActivityRequestType
import com.ctrip.car.market.activity.contract.QueryActivityResponseType
import com.ctrip.car.market.activity.contract.dto.ProductCondition
import com.ctrip.car.market.activity.contract.dto.ProductPrice
import com.ctrip.car.market.activity.svc.bo.Activity
import com.ctrip.car.market.activity.svc.bo.ActivityGroupConfig
import com.ctrip.car.market.activity.svc.bo.HolidayDto
import com.ctrip.car.market.activity.svc.cache.neww.ActivityCityCache
import com.ctrip.car.market.activity.svc.cache.neww.ActivityInfoCache
import com.ctrip.car.market.activity.svc.cache.neww.ActivityProductCache
import com.ctrip.car.market.activity.svc.cache.neww.ActivityTempCache
import com.ctrip.car.market.activity.svc.cache.neww.ActivityVendorSkuCache
import com.ctrip.car.market.activity.svc.config.BaseConfig
import com.ctrip.car.market.activity.svc.util.FilterSecretUtils
import com.ctrip.car.market.common.entity.act.ActivityTempContent
import com.ctrip.car.market.common.entity.act.DeductionStrategy
import com.ctrip.car.market.common.entity.act.SupplierCondition
import com.ctrip.car.market.common.entity.act.UserCondition
import com.ctrip.car.market.common.enums.ActivityGroup
import com.ctrip.car.market.job.common.entity.ActInfoDO
import com.ctrip.car.market.job.common.entity.ActTempInfoDO
import com.google.common.collect.Lists
import com.google.common.collect.Sets
import org.apache.commons.collections.CollectionUtils
import spock.lang.Specification

import java.math.BigDecimal
import java.sql.Timestamp

class QueryActivityServiceSpockTest extends Specification {

    def activityInfoCache = Mock(ActivityInfoCache)
    def activityTempCache = Mock(ActivityTempCache)
    def activityCityCache = Mock(ActivityCityCache)
    def activityProductCache = Mock(ActivityProductCache)
    def activityVendorSkuCache = Mock(ActivityVendorSkuCache)
    def baseConfig = Mock(BaseConfig)
    def filterSecretUtils = Mock(FilterSecretUtils)
    def activityGroupConfig = Mock(ActivityGroupConfig)

    def testInstance = new QueryActivityService(
            activityInfoCache: activityInfoCache,
            activityTempCache: activityTempCache,
            activityCityCache: activityCityCache,
            activityProductCache: activityProductCache,
            activityVendorSkuCache: activityVendorSkuCache,
            baseConfig: baseConfig,
            filterSecretUtils: filterSecretUtils,
            activityGroupConfig: activityGroupConfig
    )

    def "test checkRequest method"() {
        def testMethod = testInstance.getClass().getDeclaredMethod("checkRequest", QueryActivityRequestType.class, QueryActivityResponseType.class)
        testMethod.setAccessible(true)

        expect:
        ((boolean) testMethod.invoke(testInstance, request as QueryActivityRequestType, new QueryActivityResponseType())) == result

        where:
        request                                                                                                                   || result
        new QueryActivityRequestType()                                                                                            || false
        new QueryActivityRequestType(baseRequest: new BaseRequest(channelId: 17671, sourceFrom: "ISD_C_APP"),
                productCondition: new ProductCondition(vendorId: 1L, storeId: 1L, pickUpTime: getTime(1), returnTime: getTime(3),
                        cityIds: [1], tenancy: 1, standardProductId: 1L, payMode: 2, vehicleGroupId: 1))                          || false

        new QueryActivityRequestType(baseRequest: new BaseRequest(channelId: 17671, sourceFrom: "ISD_C_APP", uid: "1111"),
                productCondition: new ProductCondition(vendorId: 1L, storeId: 1L, pickUpTime: getTime(1), returnTime: getTime(3),
                        cityIds: [1], tenancy: 1, standardProductId: 1L, payMode: 2, vehicleGroupId: 1)
                , productPrice: new ProductPrice(rentAmount: BigDecimal.TEN))                                                     || true
    }

    def "test doBusiness with null vendor activities"() {
        given:
        def request = createValidRequest()

        when:
        activityInfoCache.queryByVendorId(1L) >> []
        activityInfoCache.queryByVendorId(0L) >> []
        def response = testInstance.doBusiness(request)

        then:
        response != null
        response.activityList == null || response.activityList.isEmpty()
    }

    def "test doBusiness with valid activities"() {
        given:
        def request = createValidRequest()
        def actInfoDO = createActInfoDO()
        def actTempInfoDO = createActTempInfoDO()
        def activity = createActivity(actInfoDO, actTempInfoDO)

        when:
        activityInfoCache.queryByVendorId(1L) >> [actInfoDO]
        activityInfoCache.queryByVendorId(0L) >> []
        activityTempCache.queryByTmpId(1L) >> actTempInfoDO
        activityCityCache.queryCityByActId(1L) >> [1, 2]
        activityProductCache.queryByActId(1L) >> [100L, 200L]
        activityVendorSkuCache.queryByActId(1L) >> [1000L, 2000L]
        activityGroupConfig.findPriority(ActivityGroup.Regular.getId()) >> 1
        baseConfig.getHolidayList() >> []
        baseConfig.getSourceFromMapping() >> []
        baseConfig.getChannelActivity(_) >> Sets.newHashSet()
        filterSecretUtils.filterSecret(_, _, _) >> [activity]

        def response = testInstance.doBusiness(request)

        then:
        response != null
        response.getBaseResponse().getCode() == "000000"
    }

    def "test doBusiness with exception handling"() {
        given:
        def request = createValidRequest()

        when:
        activityInfoCache.queryByVendorId(_) >> { throw new RuntimeException("Cache error") }
        def response = testInstance.doBusiness(request)

        then:
        response != null
        response.baseResponse.code == "500000"
    }

    def "test getVendorAct method with vendor activities"() {
        given:
        def vendorId = 1L
        def actInfoDO = createActInfoDO()
        def actTempInfoDO = createActTempInfoDO()
        def getVendorActMethod = testInstance.getClass().getDeclaredMethod("getVendorAct", Long.class)
        getVendorActMethod.setAccessible(true)

        when:
        activityInfoCache.queryByVendorId(vendorId) >> [actInfoDO]
        activityInfoCache.queryByVendorId(0L) >> []
        activityTempCache.queryByTmpId(1L) >> actTempInfoDO
        activityCityCache.queryCityByActId(1L) >> [1, 2]
        activityProductCache.queryByActId(1L) >> [100L, 200L]
        activityVendorSkuCache.queryByActId(vendorId) >> [1000L, 2000L]
        activityGroupConfig.findPriority(ActivityGroup.Regular.getId()) >> 1

        def result = getVendorActMethod.invoke(testInstance, vendorId)

        then:
        result != null
    }

    def "test getVendorAct method with no activities"() {
        given:
        def vendorId = 1L
        def getVendorActMethod = testInstance.getClass().getDeclaredMethod("getVendorAct", Long.class)
        getVendorActMethod.setAccessible(true)

        when:
        activityInfoCache.queryByVendorId(vendorId) >> []
        activityInfoCache.queryByVendorId(0L) >> []

        def result = getVendorActMethod.invoke(testInstance, vendorId)

        then:
        result != null
    }

    def "test getVendorAct method with null temp"() {
        given:
        def vendorId = 1L
        def actInfoDO = createActInfoDO()
        def getVendorActMethod = testInstance.getClass().getDeclaredMethod("getVendorAct", Long.class)
        getVendorActMethod.setAccessible(true)

        when:
        activityInfoCache.queryByVendorId(vendorId) >> [actInfoDO]
        activityInfoCache.queryByVendorId(0L) >> []
        activityTempCache.queryByTmpId(1L) >> null

        def result = getVendorActMethod.invoke(testInstance, vendorId)

        then:
        result != null
    }

    def "test doBusiness with invalid base request"() {
        given:
        def request = new QueryActivityRequestType()

        when:
        def response = testInstance.doBusiness(request)

        then:
        response != null
    }

    def "test doBusiness with invalid product condition"() {
        given:
        def request = new QueryActivityRequestType(
                baseRequest: new BaseRequest(channelId: 17671, sourceFrom: "ISD_C_APP"),
                productCondition: new ProductCondition() // Missing required fields
        )

        when:
        def response = testInstance.doBusiness(request)

        then:
        response != null
    }

    def "test doBusiness with invalid product price"() {
        given:
        def request = new QueryActivityRequestType(
                baseRequest: new BaseRequest(channelId: 17671, sourceFrom: "ISD_C_APP"),
                productCondition: new ProductCondition(
                        vendorId: 1L,
                        pickUpTime: getTime(1),
                        returnTime: getTime(3),
                        cityIds: [1]
                ),
                productPrice: null // Missing product price
        )

        when:
        def response = testInstance.doBusiness(request)

        then:
        response != null
        response.baseResponse.message != null
    }

    def "test doBusiness with complex filtering scenario"() {
        given:
        def request = createValidRequest()
        def actInfoDO1 = createActInfoDO()
        def actInfoDO2 = createActInfoDO()
        actInfoDO2.id = 2L
        actInfoDO2.tempId = 2L

        def actTempInfoDO1 = createActTempInfoDO()
        def actTempInfoDO2 = createActTempInfoDO()
        actTempInfoDO2.tmpId = 2L

        def activity1 = createActivity(actInfoDO1, actTempInfoDO1)
        def activity2 = createActivity(actInfoDO2, actTempInfoDO2)

        when:
        activityInfoCache.queryByVendorId(1L) >> [actInfoDO1, actInfoDO2]
        activityInfoCache.queryByVendorId(0L) >> []
        activityTempCache.queryByTmpId(1L) >> actTempInfoDO1
        activityTempCache.queryByTmpId(2L) >> actTempInfoDO2
        activityCityCache.queryCityByActId(1L) >> [1, 2]
        activityCityCache.queryCityByActId(2L) >> [1, 2]
        activityProductCache.queryByActId(1L) >> [100L, 200L]
        activityProductCache.queryByActId(2L) >> [100L, 200L]
        activityVendorSkuCache.queryByActId(1L) >> [1000L, 2000L]
        activityGroupConfig.findPriority(ActivityGroup.Regular.getId()) >> 1
        baseConfig.getHolidayList() >> []
        baseConfig.getSourceFromMapping() >> []
        baseConfig.getChannelActivity(_) >> Sets.newHashSet()
        filterSecretUtils.filterSecret(_, _, _) >> [activity1, activity2]

        def response = testInstance.doBusiness(request)

        then:
        response != null
        response.getBaseResponse().getCode() == "000000"
    }

    // Helper methods for creating test data
    def createValidRequest() {
        return new QueryActivityRequestType(
                baseRequest: new BaseRequest(
                        channelId: 17671,
                        sourceFrom: "ISD_C_APP",
                        uid: "test_uid_123",
                        requestId: "test_request_123"
                ),
                productCondition: new ProductCondition(
                        vendorId: 1L,
                        storeId: 1L,
                        pickUpTime: getTime(1),
                        returnTime: getTime(3),
                        cityIds: [1, 2],
                        tenancy: 2,
                        standardProductId: 100L,
                        payMode: 2,
                        vehicleGroupId: 1,
                        storeType: 1
                ),
                productPrice: new ProductPrice(
                        rentAmount: new BigDecimal("1000"),
                        adjustAmount: new BigDecimal("100")
                ),
                activityIdList: []
        )
    }

    def createActInfoDO() {
        def actInfo = new ActInfoDO()
        actInfo.id = 1L
        actInfo.tempId = 1L
        actInfo.vendorId = 1L
        actInfo.startTime = getTime2(-1)
        actInfo.endTime = getTime2(30)
        return actInfo
    }

    def createActTempInfoDO() {
        def actTemp = new ActTempInfoDO()
        actTemp.tmpId = 1L
        actTemp.groupId = ActivityGroup.Regular.getId()
        actTemp.supportModifyOrder = 1
        actTemp.content = createActivityTempContent()
        return actTemp
    }

    def createActivityTempContent() {
        def content = new ActivityTempContent()
        content.supplierCondition = new SupplierCondition()
        content.userCondition = new UserCondition()
        content.deductionStrategy = new DeductionStrategy()
        return content
    }

    def createActivity(ActInfoDO actInfo, ActTempInfoDO actTemp) {
        return new Activity(
                actInfo,
                actTemp,
                Sets.newHashSet(1, 2),
                Sets.newHashSet(),
                Sets.newHashSet(100L, 200L),
                Sets.newHashSet(1000L, 2000L),
                1
        )
    }

    def getTime(int day) {
        Calendar calendar = Calendar.getInstance()
        calendar.add(Calendar.DATE, day)
        return calendar
    }

    def getTime2(int day) {
        Calendar calendar = Calendar.getInstance()
        calendar.add(Calendar.DATE, day)
        return new Timestamp(calendar.getTimeInMillis())
    }
}
