package com.ctrip.car.market.activity.svc.service

import com.ctrip.car.market.activity.svc.bo.HolidayDto
import com.ctrip.car.market.activity.svc.cache.neww.ActivityCityCache
import com.ctrip.car.market.activity.svc.cache.neww.ActivityInfoCache
import com.ctrip.car.market.activity.svc.cache.neww.ActivityTempCache
import com.ctrip.car.market.activity.svc.config.BaseConfig
import com.ctrip.car.market.activity.svc.proxy.CdpServiceProxy
import com.ctrip.car.market.activity.svc.proxy.QueryQunarUserLabelProxy
import com.ctrip.car.market.activity.svc.util.ActivityListConditionUtil
import com.ctrip.car.market.common.entity.act.ActivityTempContent
import com.ctrip.car.market.common.entity.act.CustomContent
import com.ctrip.car.market.common.entity.act.CustomSupplierCondition
import com.ctrip.car.market.common.entity.act.SupplierCondition
import com.ctrip.car.market.job.common.entity.ActInfoDO
import com.ctrip.car.market.job.common.entity.ActTempInfoDO
import com.google.common.collect.Lists
import com.google.common.collect.Sets
import spock.lang.Specification

import java.lang.reflect.Method

class QueryActivityListNewServiceSpockTest extends Specification {

    def activityTempCache = Mock(ActivityTempCache)
    def activityInfoCache = Mock(ActivityInfoCache)
    def activityCityCache = Mock(ActivityCityCache)
    def activityListConditionUtil = Mock(ActivityListConditionUtil)
    def baseConfig = Mock(BaseConfig)
    def queryQunarUserLabelProxy = Mock(QueryQunarUserLabelProxy)

    def testInstance = new QueryActivityListNewService(
            activityTempCache: activityTempCache,
            activityInfoCache: activityInfoCache,
            activityCityCache: activityCityCache,
            activityListConditionUtil: activityListConditionUtil,
            baseConfig: baseConfig,
            queryQunarUserLabelProxy: queryQunarUserLabelProxy
    )

    def "test filterAct with empty template list"() {
        given: "获取私有方法"
        Method filterActMethod = testInstance.getClass().getDeclaredMethod("filterAct", List.class, HolidayDto.class, Set.class, Set.class)
        filterActMethod.setAccessible(true)

        when: "调用filterAct方法"
        List<Long> result = filterActMethod.invoke(testInstance, [], null, Sets.newHashSet(), Sets.newHashSet()) as List<Long>

        then: "返回空列表"
        result.isEmpty()
    }

    def "test filterAct with template having no activity ids"() {
        given: "获取私有方法"
        Method filterActMethod = testInstance.getClass().getDeclaredMethod("filterAct", List.class, HolidayDto.class, Set.class, Set.class)
        filterActMethod.setAccessible(true)

        and: "创建模板但没有对应的活动ID"
        def tempInfo = createActTempInfoDO(1L)
        activityInfoCache.queryActId(1L) >> []

        when: "调用filterAct方法"
        List<Long> result = filterActMethod.invoke(testInstance, [tempInfo], null, Sets.newHashSet(), Sets.newHashSet()) as List<Long>

        then: "返回空列表"
        result.isEmpty()
    }

    def "test filterAct without holiday and city filter"() {
        given: "获取私有方法"
        Method filterActMethod = testInstance.getClass().getDeclaredMethod("filterAct", List.class, HolidayDto.class, Set.class, Set.class)
        filterActMethod.setAccessible(true)

        and: "创建模板和活动ID"
        def tempInfo = createActTempInfoDO(1L)
        activityInfoCache.queryActId(1L) >> [100L, 200L]

        when: "调用filterAct方法"
        List<Long> result = filterActMethod.invoke(testInstance, [tempInfo], null, Sets.newHashSet(), Sets.newHashSet()) as List<Long>

        then: "返回所有活动ID"
        result.size() == 2
        result.contains(100L)
        result.contains(200L)
    }

    def "test filterAct with holiday filter - allow holiday"() {
        given: "获取私有方法"
        Method filterActMethod = testInstance.getClass().getDeclaredMethod("filterAct", List.class, HolidayDto.class, Set.class, Set.class)
        filterActMethod.setAccessible(true)

        and: "创建支持节假日的模板"
        def tempInfo = createActTempInfoDOWithHolidaySupport(1L, true, false)
        def holidayDto = new HolidayDto(holidayName: "春节")
        activityInfoCache.queryActId(1L) >> [100L, 200L]

        when: "调用filterAct方法"
        List<Long> result = filterActMethod.invoke(testInstance, [tempInfo], holidayDto, Sets.newHashSet(), Sets.newHashSet()) as List<Long>

        then: "返回支持节假日的活动"
        result.size() == 2
        result.contains(100L)
        result.contains(200L)
    }

    def "test filterAct with holiday filter - not allow holiday"() {
        given: "获取私有方法"
        Method filterActMethod = testInstance.getClass().getDeclaredMethod("filterAct", List.class, HolidayDto.class, Set.class, Set.class)
        filterActMethod.setAccessible(true)

        and: "创建不支持节假日的模板"
        def tempInfo = createActTempInfoDOWithHolidaySupport(1L, false, false)
        def holidayDto = new HolidayDto(holidayName: "春节")
        activityInfoCache.queryActId(1L) >> [100L, 200L]

        when: "调用filterAct方法"
        List<Long> result = filterActMethod.invoke(testInstance, [tempInfo], holidayDto, Sets.newHashSet(), Sets.newHashSet()) as List<Long>

        then: "返回空列表"
        result.isEmpty()
    }

    def "test filterAct with holiday filter - custom holiday limit"() {
        given: "获取私有方法"
        Method filterActMethod = testInstance.getClass().getDeclaredMethod("filterAct", List.class, HolidayDto.class, Set.class, Set.class)
        filterActMethod.setAccessible(true)

        and: "创建支持自定义节假日规则的模板"
        def tempInfo = createActTempInfoDOWithHolidaySupport(1L, false, true)
        def holidayDto = new HolidayDto(holidayName: "春节")
        def actInfo = createActInfoDOWithHolidaySupport(100L, true)

        activityInfoCache.queryActId(1L) >> [100L, 200L]
        activityInfoCache.queryById(100L) >> actInfo
        activityInfoCache.queryById(200L) >> createActInfoDOWithHolidaySupport(200L, false)

        when: "调用filterAct方法"
        List<Long> result = filterActMethod.invoke(testInstance, [tempInfo], holidayDto, Sets.newHashSet(), Sets.newHashSet()) as List<Long>

        then: "只返回支持节假日的活动"
        result.size() == 1
        result.contains(100L)
    }

    def "test filterAct with city filter enabled"() {
        given: "获取私有方法"
        Method filterActMethod = testInstance.getClass().getDeclaredMethod("filterAct", List.class, HolidayDto.class, Set.class, Set.class)
        filterActMethod.setAccessible(true)

        and: "创建模板和城市过滤条件"
        def tempInfo = createActTempInfoDO(1L)
        def citySet = Sets.newHashSet(1, 2)

        activityInfoCache.queryActId(1L) >> [100L, 200L, 300L]
        baseConfig.isListFilterCity() >> true
        activityCityCache.queryCityByActId(100L) >> [1, 3] // 匹配城市1
        activityCityCache.queryCityByActId(200L) >> [4, 5] // 不匹配
        activityCityCache.queryCityByActId(300L) >> [] // 空城市列表，应该通过

        when: "调用filterAct方法"
        List<Long> result = filterActMethod.invoke(testInstance, [tempInfo], null, citySet, Sets.newHashSet()) as List<Long>

        then: "返回匹配城市的活动"
        result.size() == 2
        result.contains(100L)
        result.contains(300L)
    }

    def "test filterAct with city filter disabled"() {
        given: "获取私有方法"
        Method filterActMethod = testInstance.getClass().getDeclaredMethod("filterAct", List.class, HolidayDto.class, Set.class, Set.class)
        filterActMethod.setAccessible(true)

        and: "创建模板，但禁用城市过滤"
        def tempInfo = createActTempInfoDO(1L)
        def citySet = Sets.newHashSet(1, 2)

        activityInfoCache.queryActId(1L) >> [100L, 200L]
        baseConfig.isListFilterCity() >> false

        when: "调用filterAct方法"
        List<Long> result = filterActMethod.invoke(testInstance, [tempInfo], null, citySet, Sets.newHashSet()) as List<Long>

        then: "返回所有活动，不进行城市过滤"
        result.size() == 2
        result.contains(100L)
        result.contains(200L)
    }

    def "test filterAct with multiple templates"() {
        given: "获取私有方法"
        Method filterActMethod = testInstance.getClass().getDeclaredMethod("filterAct", List.class, HolidayDto.class, Set.class, Set.class)
        filterActMethod.setAccessible(true)

        and: "创建多个模板"
        def tempInfo1 = createActTempInfoDO(1L)
        def tempInfo2 = createActTempInfoDO(2L)

        activityInfoCache.queryActId(1L) >> [100L, 200L]
        activityInfoCache.queryActId(2L) >> [300L, 400L]

        when: "调用filterAct方法"
        List<Long> result = filterActMethod.invoke(testInstance, [tempInfo1, tempInfo2], null, Sets.newHashSet(), Sets.newHashSet()) as List<Long>

        then: "返回所有模板的活动ID"
        result.size() == 4
        result.containsAll([100L, 200L, 300L, 400L])
    }

    def "test filterAct with complex scenario"() {
        given: "获取私有方法"
        Method filterActMethod = testInstance.getClass().getDeclaredMethod("filterAct", List.class, HolidayDto.class, Set.class, Set.class)
        filterActMethod.setAccessible(true)

        and: "创建复杂场景：节假日+城市过滤"
        def tempInfo = createActTempInfoDOWithHolidaySupport(1L, true, false)
        def holidayDto = new HolidayDto(holidayName: "春节")
        def citySet = Sets.newHashSet(1, 2)

        activityInfoCache.queryActId(1L) >> [100L, 200L, 300L]
        baseConfig.isListFilterCity() >> true
        activityCityCache.queryCityByActId(100L) >> [1] // 匹配城市
        activityCityCache.queryCityByActId(200L) >> [3] // 不匹配城市
        activityCityCache.queryCityByActId(300L) >> [2] // 匹配城市

        when: "调用filterAct方法"
        List<Long> result = filterActMethod.invoke(testInstance, [tempInfo], holidayDto, citySet, Sets.newHashSet()) as List<Long>

        then: "返回同时满足节假日和城市条件的活动"
        result.size() == 2
        result.contains(100L)
        result.contains(300L)
    }

    // 辅助方法：创建基本的ActTempInfoDO
    private ActTempInfoDO createActTempInfoDO(Long tmpId) {
        def tempInfo = new ActTempInfoDO()
        tempInfo.setTmpId(tmpId)

        def content = new ActivityTempContent()
        def supplierCondition = new SupplierCondition()
        supplierCondition.setAllowHoliday(true)
        supplierCondition.setAllowHolidayLimit(false)
        content.setSupplierCondition(supplierCondition)
        tempInfo.setContent(content)

        return tempInfo
    }

    // 辅助方法：创建支持节假日配置的ActTempInfoDO
    private ActTempInfoDO createActTempInfoDOWithHolidaySupport(Long tmpId, Boolean allowHoliday, Boolean allowHolidayLimit) {
        def tempInfo = new ActTempInfoDO()
        tempInfo.setTmpId(tmpId)

        def content = new ActivityTempContent()
        def supplierCondition = new SupplierCondition()
        supplierCondition.setAllowHoliday(allowHoliday)
        supplierCondition.setAllowHolidayLimit(allowHolidayLimit)
        content.setSupplierCondition(supplierCondition)
        tempInfo.setContent(content)

        return tempInfo
    }

    // 辅助方法：创建支持节假日配置的ActInfoDO
    private ActInfoDO createActInfoDOWithHolidaySupport(Long id, Boolean allowHoliday) {
        def actInfo = new ActInfoDO()
        actInfo.setId(id)

        def content = new CustomContent()
        def supplierCondition = new CustomSupplierCondition()
        supplierCondition.setAllowHoliday(allowHoliday)
        content.setSupplierCondition(supplierCondition)
        actInfo.setContent(content)

        return actInfo
    }
}
