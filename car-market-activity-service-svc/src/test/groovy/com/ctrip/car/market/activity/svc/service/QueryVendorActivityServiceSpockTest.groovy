package com.ctrip.car.market.activity.svc.service

import com.ctrip.car.market.activity.contract.QueryVendorActivityRequestType
import com.ctrip.car.market.activity.svc.cache.neww.ActivityInfoCache
import com.ctrip.car.market.job.common.entity.ActInfoDO
import spock.lang.Specification

class QueryVendorActivityServiceSpockTest extends Specification {

    private activityInfoCache = Mock(ActivityInfoCache)

    private testInstance = new QueryVendorActivityService(
            activityInfoCache: activityInfoCache
    )

    def test_doBusiness() {
        given:
        activityInfoCache.queryByVendorId(_ as Long) >> list

        expect:
        testInstance.doBusiness(request as QueryVendorActivityRequestType).getBaseResponse().getCode() == result

        where:
        request                                          | list                    || result
        new QueryVendorActivityRequestType()             | []                      || "100000"
        new QueryVendorActivityRequestType(vendorId: 1L) | [new ActInfoDO(id: 1L)] || "000000"
    }
}
