package com.ctrip.car.market.activity.svc.service.old

import com.ctrip.car.market.activity.contract.BaseRequest
import com.ctrip.car.market.activity.contract.CancelActivityRequestType
import com.ctrip.car.market.activity.contract.CancelActivityResponseType
import com.ctrip.car.market.activity.contract.UseActivityRequestType
import com.ctrip.car.market.activity.contract.UseActivityResponseType
import com.ctrip.car.market.activity.repository.entity.CpnUseactivity
import com.ctrip.car.market.activity.repository.entity.old.CalcActivityItem
import com.ctrip.car.market.activity.repository.entity.old.CpnActivitycalc
import com.ctrip.car.market.activity.repository.entity.old.CpnActivityconfig
import com.ctrip.car.market.activity.repository.entity.old.enums.CarActivityStatusEnum
import com.ctrip.car.market.activity.repository.entity.old.enums.UseActivityStatusEnum
import com.ctrip.car.market.activity.repository.service.old.OldActivityService
import com.ctrip.car.sdcommon.utils.LogUtils
import com.google.common.collect.Lists
import spock.lang.Specification

class OldActivityUseCancelServiceTest extends Specification {

    private final static Long newActivityId = 200000L;

    def log = Mock(LogUtils.class)

    def oldActivityService = Mock(OldActivityService.class)

    def testInstance = new OldActivityUseCancelService(log: log, oldActivityService: oldActivityService)

    def "test_checkUseActivityRequest"() {
        setup: ""

        and: ""

        expect: ""
        result == testInstance.checkUseActivityRequest(request as UseActivityRequestType, response as UseActivityResponseType)
        if (Objects.nonNull(response.getBaseResponse())) {
            message == response.getBaseResponse().getMessage()
        }

        where: ""
        request                                                                 | response                          || result     | message
        new UseActivityRequestType()                                            | new UseActivityResponseType()     || false      | "baseRequest is null"
        new UseActivityRequestType(baseRequest: new BaseRequest())              | new UseActivityResponseType()     || false      | "uid is null"
        new UseActivityRequestType(baseRequest: new BaseRequest(uid: "1"))      | new UseActivityResponseType()     || false      | "activityId is invalid"
        new UseActivityRequestType(
                baseRequest: new BaseRequest(uid: "1"),
                activityId: newActivityId - 1L
        )                                                                       | new UseActivityResponseType()     || false      | "orderId is invalid"
        new UseActivityRequestType(
                baseRequest: new BaseRequest(uid: "1"),
                activityId: newActivityId - 1L,
                orderId: 10086L
        )                                                                       | new UseActivityResponseType()     || false      | "activityAmount error"
        new UseActivityRequestType(
                baseRequest: new BaseRequest(uid: "1"),
                activityId: newActivityId - 1L,
                orderId: 10086L,
                activityAmount: BigDecimal.ONE
        )                                                                       | new UseActivityResponseType()     || false      | "orderOriginalAmount error"
        new UseActivityRequestType(
                baseRequest: new BaseRequest(uid: "1"),
                activityId: newActivityId - 1L,
                orderId: 10086L,
                activityAmount: BigDecimal.ONE,
                orderOriginalAmount: BigDecimal.TEN
        )                                                                       | new UseActivityResponseType()     || true      | null
    }

    // 使用活动失败：请求验证失败
    def "test_useActivity_request check fail"() {
        setup: ""
        def configId = newActivityId - 1L

        and: ""

        when: ""
        def result = testInstance.useActivity(new UseActivityRequestType())

        then: ""
        Objects.equals(result.getBaseResponse().getCode(), "100000")
        Objects.equals(result.getBaseResponse().getMessage(), "baseRequest is null")
    }

    // 使用活动失败：活动不存在
    def "test_useActivity_activity non exist"() {
        setup: ""
        def configId = newActivityId - 1L

        and: ""
        oldActivityService.queryActivityByConfigId(_ as Long) >> null

        when: ""
        def result = testInstance.useActivity(new UseActivityRequestType(
                baseRequest: new BaseRequest(uid: "1"),
                activityId: newActivityId - 1L,
                orderId: 10086L,
                activityAmount: BigDecimal.ONE,
                orderOriginalAmount: BigDecimal.TEN)
        )

        then: ""
        Objects.equals(result.getBaseResponse().getCode(), "100000")
        Objects.equals(result.getBaseResponse().getMessage(), "activity non-existent")
    }

    // 使用活动失败：活动已过期
    def "test_useActivity_activity expired"() {
        setup: ""
        def configId = newActivityId - 1L

        and: ""
        oldActivityService.queryActivityByConfigId(_ as Long) >> new CpnActivityconfig(configId: configId, status: CarActivityStatusEnum.OUT_OF_DATE.getCode())

        when: ""
        def result = testInstance.useActivity(new UseActivityRequestType(
                baseRequest: new BaseRequest(uid: "1"),
                activityId: newActivityId - 1L,
                orderId: 10086L,
                activityAmount: BigDecimal.ONE,
                orderOriginalAmount: BigDecimal.TEN)
        )

        then: ""
        Objects.equals(result.getBaseResponse().getCode(), "100000")
        Objects.equals(result.getBaseResponse().getMessage(), "activity expired")
    }

    // 使用活动失败：已经使用过
    def "test_useActivity_do not reuse"() {
        setup: ""
        def configId = newActivityId - 1L

        and: ""
        oldActivityService.queryActivityByConfigId(_ as Long) >> new CpnActivityconfig(configId: configId, status: CarActivityStatusEnum.GO_VALID.getCode())

        and: ""
        oldActivityService.getUseActivityList(_ as Long, _ as Long, _ as String) >> Lists.newArrayList(new CpnUseactivity(status: UseActivityStatusEnum.USED.getCode()))

        when: ""
        def result = testInstance.useActivity(new UseActivityRequestType(
                baseRequest: new BaseRequest(uid: "1"),
                activityId: newActivityId - 1L,
                orderId: 10086L,
                activityAmount: BigDecimal.ONE,
                orderOriginalAmount: BigDecimal.TEN)
        )

        then: ""
        Objects.equals(result.getBaseResponse().getCode(), "100000")
        Objects.equals(result.getBaseResponse().getMessage(), "do not reuse")
    }

    // 使用活动失败：库存数量不足
    def "test_useActivity_stock is not enough"() {
        setup: ""
        def configId = newActivityId - 1L

        and: ""
        oldActivityService.queryActivityByConfigId(_ as Long) >> new CpnActivityconfig(
                configId: configId,
                status: CarActivityStatusEnum.GO_VALID.getCode(),
                stockNum: 5L
        )

        and: ""
        oldActivityService.getUseActivityList(_ as Long, _ as Long, _ as String) >> Lists.newArrayList()

        and: ""
        oldActivityService.queryCalcByCalcKey(_ as String) >> new CpnActivitycalc(totalNum: 10L)

        when: ""
        def result = testInstance.useActivity(new UseActivityRequestType(
                baseRequest: new BaseRequest(uid: "1"),
                activityId: newActivityId - 1L,
                orderId: 10086L,
                activityAmount: BigDecimal.ONE,
                orderOriginalAmount: BigDecimal.TEN)
        )

        then: ""
        Objects.equals(result.getBaseResponse().getCode(), "100000")
        Objects.equals(result.getBaseResponse().getMessage(), "stock is not enough")
    }

    // 使用活动失败：库存金额不足
    def "test_useActivity_stock amount is not enough"() {
        setup: ""
        def configId = newActivityId - 1L

        and: ""
        oldActivityService.queryActivityByConfigId(_ as Long) >> new CpnActivityconfig(
                configId: configId,
                status: CarActivityStatusEnum.GO_VALID.getCode(),
                stockNum: 20L,
                stockAmount: BigDecimal.ONE
        )

        and: ""
        oldActivityService.getUseActivityList(_ as Long, _ as Long, _ as String) >> Lists.newArrayList()

        and: ""
        oldActivityService.queryCalcByCalcKey(_ as String) >> new CpnActivitycalc(totalNum: 10L, totalAmount: BigDecimal.TEN)

        when: ""
        def result = testInstance.useActivity(new UseActivityRequestType(
                baseRequest: new BaseRequest(uid: "1"),
                activityId: newActivityId - 1L,
                orderId: 10086L,
                activityAmount: BigDecimal.ONE,
                orderOriginalAmount: BigDecimal.TEN)
        )

        then: ""
        Objects.equals(result.getBaseResponse().getCode(), "100000")
        Objects.equals(result.getBaseResponse().getMessage(), "stock amount is not enough")
    }

    def "test_useActivity"() {
        setup: ""
        def configId = newActivityId - 1L

        and: ""
        oldActivityService.queryActivityByConfigId(_ as Long) >> new CpnActivityconfig(configId: configId, status: CarActivityStatusEnum.GO_VALID.getCode())

        and: ""
        oldActivityService.getUseActivityList(_ as Long, _ as Long, _ as String) >> Lists.newArrayList()

        and: ""
        oldActivityService.queryCalcByCalcKey(_ as String) >> new CpnActivitycalc()

        and: ""
        oldActivityService.insertUseActivity(_ as CpnUseactivity) >> 1

        and: ""
        oldActivityService.insertActivityCalc(_ as CpnActivitycalc) >> 1

        when: ""
        def result = testInstance.useActivity(new UseActivityRequestType(
                baseRequest: new BaseRequest(uid: "1"),
                activityId: newActivityId - 1L,
                orderId: 10086L,
                activityAmount: BigDecimal.ONE,
                orderOriginalAmount: BigDecimal.TEN)
        )

        then: ""
        Objects.equals(result.getBaseResponse().getCode(), "000000")
    }

    def "test_checkCancelActivityRequest"() {
        setup: ""

        and: ""

        expect: ""
        result == testInstance.checkCancelActivityRequest(request as CancelActivityRequestType, response as CancelActivityResponseType)
        if (Objects.nonNull(response.getBaseResponse())) {
            message == response.getBaseResponse().getMessage()
        }

        where: ""
        request                                                                 | response                          || result     | message
        new CancelActivityRequestType()                                         | new CancelActivityResponseType()  || false      | "baseRequest is null"
        new CancelActivityRequestType(baseRequest: new BaseRequest())           | new CancelActivityResponseType()  || false      | "uid is null"
        new CancelActivityRequestType(baseRequest: new BaseRequest(uid: "1"))   | new CancelActivityResponseType()  || false      | "activityId is invalid"
        new CancelActivityRequestType(
                baseRequest: new BaseRequest(uid: "1"),
                activityId: newActivityId - 1L
        )                                                                       | new CancelActivityResponseType()  || false      | "orderId is invalid"
        new CancelActivityRequestType(
                baseRequest: new BaseRequest(uid: "1"),
                activityId: newActivityId - 1L,
                orderId: 10086L
        )                                                                       | new CancelActivityResponseType()  || true      | null
    }

    // 取消活动失败：请求验证失败
    def "test_cancelActivity_request check fail"() {
        setup: ""
        def configId = newActivityId - 1L

        and: ""

        when: ""
        def result = testInstance.cancelActivity(new CancelActivityRequestType())

        then: ""
        Objects.equals(result.getBaseResponse().getCode(), "100000")
        Objects.equals(result.getBaseResponse().getMessage(), "baseRequest is null")
    }

    // 取消活动失败：没有使用记录
    def "test_cancelActivity_have no used record"() {
        setup: ""
        def configId = newActivityId - 1L

        and: ""
        oldActivityService.getUseActivityList(_ as Long, _ as Long, _ as String) >> Lists.newArrayList()

        when: ""
        def result = testInstance.cancelActivity(new CancelActivityRequestType(
                baseRequest: new BaseRequest(uid: "1"),
                activityId: newActivityId - 1L,
                orderId: 10086L
        ))

        then: ""
        Objects.equals(result.getBaseResponse().getCode(), "100000")
        Objects.equals(result.getBaseResponse().getMessage(), "have no used record")
    }

    // 取消活动失败：有使用记录，但活动已经被取消了
    def "test_cancelActivity_has already canceled"() {
        setup: ""
        def configId = newActivityId - 1L

        and: ""
        oldActivityService.getUseActivityList(_ as Long, _ as Long, _ as String) >> Lists.newArrayList(new CpnUseactivity(configId: configId, status: UseActivityStatusEnum.CANCELED.getCode()))

        when: ""
        def result = testInstance.cancelActivity(new CancelActivityRequestType(
                baseRequest: new BaseRequest(uid: "1"),
                activityId: newActivityId - 1L,
                orderId: 10086L
        ))

        then: ""
        Objects.equals(result.getBaseResponse().getCode(), "100000")
        Objects.equals(result.getBaseResponse().getMessage(), "has already canceled, do not cancel repeatedly")
    }

    def "test_cancelActivity"() {
        setup: ""
        def configId = newActivityId - 1L

        and: ""
        oldActivityService.getUseActivityList(_ as Long, _ as Long, _ as String) >> Lists.newArrayList(new CpnUseactivity(configId: configId, status: UseActivityStatusEnum.USED.getCode()))

        and: ""
        oldActivityService.updateUseActivity(_ as CpnUseactivity) >> 1

        and: ""
        oldActivityService.queryCalcByCalcKey(_ as String) >> null

        and: ""
        oldActivityService.queryActivityNumAndAmount(_ as Long) >> new CalcActivityItem(totalAmount: BigDecimal.TEN, totalNum: 10L)

        when: ""
        def result = testInstance.cancelActivity(new CancelActivityRequestType(
                baseRequest: new BaseRequest(uid: "1"),
                activityId: newActivityId - 1L,
                orderId: 10086L
        ))

        then: ""
        Objects.equals(result.getBaseResponse().getCode(), "000000")
    }

}
