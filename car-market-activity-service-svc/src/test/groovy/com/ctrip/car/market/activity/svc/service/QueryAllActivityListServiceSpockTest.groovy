package com.ctrip.car.market.activity.svc.service

import com.ctrip.car.market.activity.contract.QueryAllActivityListRequestType
import com.ctrip.car.market.activity.contract.QueryAllActivityListResponseType
import com.ctrip.car.market.activity.repository.entity.ActCtripactinfo
import com.ctrip.car.market.activity.repository.entity.ActCtriptempinfo
import com.ctrip.car.market.activity.repository.po.QueryActivityPara
import com.ctrip.car.market.activity.repository.service.ActivityService
import com.ctrip.car.market.activity.svc.bo.ActivityGroupConfig
import com.ctrip.car.market.activity.svc.cache.neww.ActivityCityCache
import com.ctrip.car.market.activity.svc.cache.neww.ActivityProductCache
import com.ctrip.car.market.activity.svc.cache.neww.ActivityVendorSkuCache
import com.ctrip.car.market.activity.svc.util.JsonUtil
import com.ctrip.car.market.common.entity.act.*
import com.ctrip.car.market.common.enums.ActivityStatus
import spock.lang.Specification
import spock.lang.Unroll

import java.sql.Timestamp

class QueryAllActivityListServiceSpockTest extends Specification {

    def service = Mock(ActivityService)
    def activityCityCache = Mock(ActivityCityCache)
    def activityProductCache = Mock(ActivityProductCache)
    def activityVendorSkuCache = Mock(ActivityVendorSkuCache)
    def activityGroupConfig = Mock(ActivityGroupConfig)

    private testInstance = new QueryAllActivityListService(
            service: service,
            activityCityCache: activityCityCache,
            activityProductCache: activityProductCache,
            activityVendorSkuCache: activityVendorSkuCache,
            activityGroupConfig: activityGroupConfig
    )

    // 测试doBusiness方法 - 空数据
    def "test doBusiness with empty data"() {
        given:
        def request = new QueryAllActivityListRequestType(pageNo: 1, pageSize: 10)

        when:
        def response = testInstance.doBusiness(request)

        then:
        1 * service.queryActivityByPage(_ as QueryActivityPara, 1, 10) >> []
        1 * service.queryActivityCount(_ as QueryActivityPara) >> 0

        response.total == 0
        response.activityList == null
    }

    // 测试doBusiness方法 - 请求参数校验失败
    @Unroll
    def "test doBusiness with invalid request parameters"() {
        when:
        def response = testInstance.doBusiness(request)

        then:
        0 * service._
        !response.baseResponse.success
        response.total == 0

        where:
        request << [
                new QueryAllActivityListRequestType(),
                new QueryAllActivityListRequestType(pageNo: 0, pageSize: 10),
                new QueryAllActivityListRequestType(pageNo: 1, pageSize: 0),
                new QueryAllActivityListRequestType(pageNo: 1, pageSize: 1001)
        ]
    }

    // 测试doBusiness方法 - 异常处理
    def "test doBusiness with exception"() {
        given:
        def request = new QueryAllActivityListRequestType(pageNo: 1, pageSize: 10)

        when:
        def response = testInstance.doBusiness(request)

        then:
        1 * service.queryActivityByPage(_ as QueryActivityPara, 1, 10) >> { throw new RuntimeException("Database error") }

        response.total == 0
    }

    @Unroll
    def "test checkRequest validation"() {
        given:
        def testMethod = testInstance.getClass().getDeclaredMethod("checkRequest", QueryAllActivityListRequestType.class, QueryAllActivityListResponseType.class)
        testMethod.setAccessible(true)

        expect:
        testMethod.invoke(testInstance, request as QueryAllActivityListRequestType, new QueryAllActivityListResponseType()) == result

        where:
        request                                                         || result
        new QueryAllActivityListRequestType()                           || false
        new QueryAllActivityListRequestType(pageNo: 0)                  || false
        new QueryAllActivityListRequestType(pageNo: 1)                  || false
        new QueryAllActivityListRequestType(pageNo: 1, pageSize: 0)     || false
        new QueryAllActivityListRequestType(pageNo: 1, pageSize: 1001)  || false
        new QueryAllActivityListRequestType(pageNo: 1, pageSize: 100)   || true
    }

    // 测试convert方法 - 完整转换
    def "test convert method with complete data"() {
        given:
        def tempInfo = createMockTemplate(1L)
        def actInfo = createMockActivity(1L, 1L)

        activityGroupConfig.findPriority(1) >> 5
        activityCityCache.queryCityByActId(1L) >> [1, 2, 3]
        activityProductCache.queryByActId(1L) >> [10L, 20L]
        activityVendorSkuCache.queryByActId(100L) >> [1L, 2L]

        when:
        def result = testInstance.convert(tempInfo, actInfo)

        then:
        result != null
        result.activityId == 1L
        result.activityType == 1
        result.groupId == 1L
        result.groupPriority == 5
        result.vendorId == 100L
        result.name == "Test Activity"
        result.status == 1
        result.deductionStrategy != null
        result.supplierCondition != null
        result.shareDetail != null
        result.userCondition != null
    }

    // 测试convert方法 - 空模板内容
    def "test convert method with null template content"() {
        given:
        def tempInfo = new ActCtriptempinfo(
                tmpId: 1L,
                templateType: 1,
                groupId: 1L,
                labelId: 1,
                name: "Test Activity",
                remark: "Test Remark",
                priority: 1,
                repetitionPeriod: "1,2,3",
                tempContent: null
        )
        def actInfo = createMockActivity(1L, 1L)

        activityGroupConfig.findPriority(1) >> 5

        when:
        def result = testInstance.convert(tempInfo, actInfo)

        then:
        result != null
        result.activityId == 1L
        result.name == "Test Activity"
        result.deductionStrategy == null
        result.supplierCondition == null
    }

    // 测试convert方法 - 自动报名活动
    def "test convert method with auto signup activity"() {
        given:
        def tempInfo = createMockTemplate(1L)
        def actInfo = createMockActivity(1L, 1L)
        actInfo.signUpMode = 1  // 自动报名

        activityGroupConfig.findPriority(1) >> 5
        activityCityCache.queryCityByActId(1L) >> []
        activityProductCache.queryByActId(1L) >> []
        activityVendorSkuCache.queryByActId(100L) >> [1L, 2L, 3L]

        when:
        def result = testInstance.convert(tempInfo, actInfo)

        then:
        result != null
        result.supplierCondition.skuIds == [1L, 2L, 3L]
    }

    def test_convert() {
        given:
        activityVendorSkuCache.queryByActId(_ as Long) >> [1L, 2L]
        activityGroupConfig.findPriority(_ as Integer) >> 1
        activityCityCache.queryCityByActId(_ as Long) >> []
        activityProductCache.queryByActId(_ as Long) >> []

        expect:
        testInstance.convert(temp as ActCtriptempinfo, act as ActCtripactinfo).getDeductionStrategy().getDeductionStrategyList().size() == result

        where:
        temp      | act        || result
        getTemp() | getAct_1() || 1
        getTemp() | getAct_2() || 2
    }

    // 辅助方法：创建模拟的Activity实体
    private ActCtripactinfo createMockActivity(Long id, Long tempId) {
        return new ActCtripactinfo(
                id: id,
                tempId: tempId,
                vendorId: 100L,
                vendorCouponCode: "COUPON123",
                startTime: new Timestamp(System.currentTimeMillis()),
                endTime: new Timestamp(System.currentTimeMillis() + 86400000),
                status: ActivityStatus.Reviewed.getStatus(),
                excludeCity: false,
                excludeReturnCity: false,
                excludeProduct: false,
                signUpMode: 0,
                customContent: JsonUtil.toString(new CustomContent(
                        deductionStrategy: new CustomDeductionStrategy(
                                deductionAmount: BigDecimal.TEN,
                                deductionStrategyList: [
                                        new CustomDeductionStrategyItem(startValue: 100, deductionAmount: BigDecimal.ONE)
                                ]
                        )
                ))
        )
    }

    // 辅助方法：创建模拟的Template实体
    private ActCtriptempinfo createMockTemplate(Long tmpId) {
        def tempContent = new ActivityTempContent()

        // 设置扣减策略
        tempContent.deductionStrategy = new DeductionStrategy(
                deductionType: 1,
                deductionAmount: BigDecimal.TEN,
                deductionAmountLimit: BigDecimal.valueOf(100),
                includeFees: ["A"],
                shareWithCoupon: false,
                limitPrice: BigDecimal.valueOf(500),
                startAmount: BigDecimal.valueOf(50),
                subsidyRangeMin: BigDecimal.ONE,
                subsidyRangeMax: BigDecimal.valueOf(200),
                comparisonTarget: [1],
                adjustType: 1,
                deductionStrategyList: [

                ]
        )

        // 设置供应商条件
        tempContent.supplierCondition = new SupplierCondition(
                cityIds: [1, 2, 3],
                excludeCity: false,
                standardProductIds: [10L, 20L],
                excludeStandardProduct: false,
                vehicleGroupIds: [1, 2],
                excludeVendorGroupId: false,
                payModes: [2],
                pickUpTimeRange: [new LimitDate(start: Calendar.getInstance(), end: Calendar.getInstance())],
                tenancyRange: [new LimitRange(floor: 1, upline: 30)],
                storeType: [1, 2],
                pType: [1]
        )

        // 设置分摊详情
        tempContent.shareDetail = new ShareDetail(
                costShare: 50,
                costType: 1,
                fixedAmount: BigDecimal.valueOf(20),
                percentage: BigDecimal.valueOf(10),
                shareType: 1
        )

        // 设置用户条件
        tempContent.userCondition = new UserCondition(
                channelIds: [1, 2, 3],
                excludeChannel: false,
                userAttributes: ["1", "2"],
                platform: [1, 2]
        )

        return new ActCtriptempinfo(
                tmpId: tmpId,
                templateType: 1,
                groupId: 1L,
                labelId: 1,
                name: "Test Activity",
                remark: "Test Remark",
                priority: 1,
                repetitionPeriod: "1,2,3",
                tempContent: JsonUtil.toString(tempContent)
        )
    }

    def getTemp() {
        ActCtriptempinfo temp = new ActCtriptempinfo()
        temp.setTemplateType(1)
        ActivityTempContent tempContent = new ActivityTempContent()
        tempContent.setDeductionStrategy(new DeductionStrategy())
        tempContent.getDeductionStrategy().setDeductionType(11)
        tempContent.getDeductionStrategy().setDeductionStrategyList([
                new DeductionStrategyItem(startValue: 1, deductionAmount: BigDecimal.ONE),
                new DeductionStrategyItem(startValue: 2, deductionAmount: BigDecimal.TEN),
        ])
        tempContent.setSupplierCondition(new SupplierCondition())
        tempContent.getSupplierCondition().setAllowHoliday(true)
        tempContent.getSupplierCondition().setCityIds([1])
        tempContent.getSupplierCondition().setCityLimit(true)
        tempContent.getSupplierCondition().setExcludeCity(true)
        tempContent.getSupplierCondition().setPayModes([2])
        tempContent.getSupplierCondition().setPickUpTimeLimitType(1)
        tempContent.getSupplierCondition().setStoreType([1])
        tempContent.getSupplierCondition().setPickUpTimeRange([new LimitDate(start: Calendar.getInstance(), end: Calendar.getInstance())])
        tempContent.getSupplierCondition().setTenancyRange([new LimitRange(floor: 0, upline: 10)])
        tempContent.setShareDetail(new ShareDetail(costType: 0, shareType: 0, costShare: 0, percentage: BigDecimal.TEN, fixedAmount: BigDecimal.TEN))
        tempContent.setUserCondition(new UserCondition(platform: [1, 2, 3], channelIds: [], userAttributes: []))
        temp.setTempContent(JsonUtil.toString(tempContent))
        return temp
    }

    def getAct_1() {
        return new ActCtripactinfo(startTime: new Timestamp(System.currentTimeMillis()), signUpMode: 1,
                endTime: new Timestamp(System.currentTimeMillis()),
                customContent: JsonUtil.toString(new CustomContent(deductionStrategy: new CustomDeductionStrategy(deductionAmount: BigDecimal.ONE, deductionStrategyList: [
                        new CustomDeductionStrategyItem(startValue: 1, deductionAmount: BigDecimal.ONE)
                ]))))
    }

    def getAct_2() {
        return new ActCtripactinfo(startTime: new Timestamp(System.currentTimeMillis()),
                endTime: new Timestamp(System.currentTimeMillis()),
                customContent: JsonUtil.toString(new CustomContent(deductionStrategy: new CustomDeductionStrategy(deductionAmount: BigDecimal.ONE, deductionStrategyList: [
                        new CustomDeductionStrategyItem(startValue: 1, deductionAmount: BigDecimal.ONE),
                        new CustomDeductionStrategyItem(startValue: 2, deductionAmount: BigDecimal.TEN)
                ]))))
    }
}
