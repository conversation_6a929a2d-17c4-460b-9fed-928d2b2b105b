package com.ctrip.car.market.activity.svc.service.old

import com.ctrip.car.market.activity.repository.entity.ActCtripactinfo
import com.ctrip.car.market.activity.repository.entity.old.CpnActivityconfig
import com.ctrip.car.market.activity.repository.service.ActivityService
import com.ctrip.car.market.activity.repository.service.old.OldActivityService
import com.ctrip.car.market.activity.svc.dto.old.ActivityInfoErrorInfoDTO
import com.ctrip.car.market.activity.svc.dto.old.AutoCloseActivityDTO
import com.ctrip.car.market.activity.svc.dto.old.AutoCloseActivityEmailDTO
import com.ctrip.car.market.activity.svc.util.CarMarketCacheHelper
import com.ctrip.car.market.activity.svc.util.JsonUtil
import com.ctrip.car.market.activity.svc.util.QConfigHelper
import com.ctrip.car.sdcommon.utils.LogUtils
import com.google.common.collect.Lists
import spock.lang.Specification

import java.sql.SQLException

class AutoCloseActivityServiceTest extends Specification {

    private final static Long newActivityId = 200000L;

    def log = Mock(LogUtils.class)

    def oldActivityService = Mock(OldActivityService.class)

    def activityService = Mock(ActivityService.class)

    def carMarketCacheHelper = Mock(CarMarketCacheHelper.class)

    def qConfigHelper = Mock(QConfigHelper.class)

    def testInstance = new AutoCloseActivityService(log: log, oldActivityService: oldActivityService,
            activityService: activityService, carMarketCacheHelper: carMarketCacheHelper, qConfigHelper: qConfigHelper
    )

    def setup() {
    }

    def "autoCloseActivityServiceV2_exeption"() {
        setup: ""
        def configId = newActivityId - 1L

        and: ""
        oldActivityService.queryActivityByConfigId(_ as Long) >> new SQLException()

        when: ""
        def request = new ActivityInfoErrorInfoDTO(configId: configId, uid: "0")
        testInstance.autoCloseActivityServiceV2(request)

        then: ""
        Objects.equals(request.getConfigId(), configId)
    }

    def "autoCloseActivityServiceV2_configId not exist"() {
        setup: ""
        def configId = newActivityId - 1L

        and: ""
        oldActivityService.queryActivityByConfigId(_ as Long) >> null

        when: ""
        def request = new ActivityInfoErrorInfoDTO(configId: configId, uid: "0")
        testInstance.autoCloseActivityServiceV2(request)

        then: ""
        Objects.equals(request.getConfigId(), configId)
    }

    def "autoCloseActivityServiceV2_configId closed"() {
        setup: ""
        def configId = newActivityId + 1L

        and: ""
        activityService.queryByPk(_ as Long) >> new ActCtripactinfo(status: 99)

        when: ""
        def request = new ActivityInfoErrorInfoDTO(configId: configId, uid: "0")
        testInstance.autoCloseActivityServiceV2(request)

        then: ""
        Objects.equals(request.getConfigId(), configId)
    }

    def "autoCloseActivityServiceV2_oldConfig_shortest route"() {
        setup: ""
        def configId = newActivityId - 1L

        and: ""
        oldActivityService.queryActivityByConfigId(_ as Long) >> new CpnActivityconfig(status: 1)

        and: ""
        qConfigHelper.getConfigMap() >> new HashMap<String, String>() {
            {
                this.put("AutoCloseTimeMillis", "10800000")
            }
        }

        and: ""
        carMarketCacheHelper.getString(_ as String) >> ""
        carMarketCacheHelper.setString(_ as String, _ as int, _ as String) >> true

        when: ""
        def request = new ActivityInfoErrorInfoDTO(configId: configId, uid: "0")
        testInstance.autoCloseActivityServiceV2(request)

        then: ""
        Objects.equals(request.getConfigId(), configId)
    }

    def "autoCloseActivityServiceV2_oldConfig_enter all branch"() {
        setup: ""
        def configId = newActivityId - 1L

        and: ""
        oldActivityService.queryActivityByConfigId(_ as Long) >> new CpnActivityconfig(status: 1)

        and: ""
        qConfigHelper.getConfigMap() >> new HashMap<String, String>() {
            {
                this.put("AutoCloseTimeMillis", "10800000")
                this.put("openAutoClose", "open")
            }
        }

        and: ""
        def redisActivityList = Lists.newArrayList(
                new AutoCloseActivityDTO(configId: configId, uid: "1", vendorId: "10000", createTime: System.currentTimeMillis() + 11000000L),
                new AutoCloseActivityDTO(configId: configId, uid: "2", vendorId: "10000", createTime: System.currentTimeMillis() + 11000000L),
                new AutoCloseActivityDTO(configId: configId, uid: "3", vendorId: "10000", createTime: System.currentTimeMillis() + 11000000L),
        )
        carMarketCacheHelper.getString(_ as String) >> JsonUtil.toString(redisActivityList)
        carMarketCacheHelper.setString(_ as String, _ as int, _ as String) >> true

        and: ""
        oldActivityService.updateActivityByEntity(_ as CpnActivityconfig) >> 1

        and: ""
        qConfigHelper.getAutoCloseActivityEmailDTOList() >> Lists.newArrayList(new AutoCloseActivityEmailDTO(vendorId: "0"))

        when: ""
        def request = new ActivityInfoErrorInfoDTO(configId: configId, uid: "0", vendorId: "10000")
        testInstance.autoCloseActivityServiceV2(request)

        then: ""
        Objects.equals(request.getConfigId(), configId)
    }

    def "autoCloseActivityServiceV2_newConfig_shortest route"() {
        setup: ""
        def configId = newActivityId + 1L

        and: ""
        activityService.queryByPk(_ as Long) >> new ActCtripactinfo(status: 1)

        and: ""
        qConfigHelper.getConfigMap() >> new HashMap<String, String>() {
            {
                this.put("AutoCloseTimeMillis", "10800000")
            }
        }

        and: ""
        carMarketCacheHelper.getString(_ as String) >> ""
        carMarketCacheHelper.setString(_ as String, _ as int, _ as String) >> true

        when: ""
        def request = new ActivityInfoErrorInfoDTO(configId: configId, uid: "0")
        testInstance.autoCloseActivityServiceV2(request)

        then: ""
        Objects.equals(request.getConfigId(), configId)
    }

    def "autoCloseActivityServiceV2_newConfig_enter all branch"() {
        setup: ""
        def configId = newActivityId + 1L

        and: ""
        activityService.queryByPk(_ as Long) >> new ActCtripactinfo(status: 1)

        and: ""
        qConfigHelper.getConfigMap() >> new HashMap<String, String>() {
            {
                this.put("AutoCloseTimeMillis", "10800000")
                this.put("openAutoClose", "open")
            }
        }

        and: ""
        def redisActivityList = Lists.newArrayList(
                new AutoCloseActivityDTO(configId: configId, vehicleId: "1", vendorId: "10000", createTime: System.currentTimeMillis() + 11000000L),
                new AutoCloseActivityDTO(configId: configId, vehicleId: "2", vendorId: "10000", createTime: System.currentTimeMillis() + 11000000L),
                new AutoCloseActivityDTO(configId: configId, vehicleId: "3", vendorId: "10000", createTime: System.currentTimeMillis() + 11000000L),
        )
        carMarketCacheHelper.getString(_ as String) >> JsonUtil.toString(redisActivityList)
        carMarketCacheHelper.setString(_ as String, _ as int, _ as String) >> true

        and: ""
        activityService.updateActivity(_ as ActCtripactinfo) >> 1

        and: ""
        qConfigHelper.getAutoCloseActivityEmailDTOList() >> Lists.newArrayList(new AutoCloseActivityEmailDTO(vendorId: "10000"))

        when: ""
        def request = new ActivityInfoErrorInfoDTO(configId: configId, uid: "0", vendorId: "10000")
        testInstance.autoCloseActivityServiceV2(request)

        then: ""
        Objects.equals(request.getConfigId(), configId)
    }

}
