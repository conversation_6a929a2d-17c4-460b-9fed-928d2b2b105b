package com.ctrip.car.market.activity.svc.service

import com.ctrip.car.market.activity.contract.QueryAllOsdActivityListRequestType
import com.ctrip.car.market.activity.contract.QueryAllOsdActivityListResponseType
import com.ctrip.car.market.activity.repository.entity.ActOsdactinfo
import com.ctrip.car.market.activity.repository.entity.ActOsdlabel
import com.ctrip.car.market.activity.repository.po.QueryActivityPara
import com.ctrip.car.market.activity.repository.service.ActivityService
import com.ctrip.car.market.common.entity.act.OsdCondition
import com.ctrip.car.sdcommon.utils.GsonUtil
import org.apache.commons.collections.CollectionUtils
import spock.lang.Specification

class QueryAllOsdActivityListServiceSpockTest extends Specification {

    def service = Mock(ActivityService)

    def testInstance = new QueryAllOsdActivityListService(
            service: service
    )

    def test_checkRequest() {
        expect:
        testInstance.checkRequest(request as QueryAllOsdActivityListRequestType, new QueryAllOsdActivityListResponseType()) == result

        where:
        request                                                            || result
        new QueryAllOsdActivityListRequestType()                           || false
        new QueryAllOsdActivityListRequestType(pageNo: 1)                  || false
        new QueryAllOsdActivityListRequestType(pageNo: 1, pageSize: 10000) || false
        new QueryAllOsdActivityListRequestType(pageNo: 1, pageSize: 10)    || true
    }

    def test_doBusiness() {
        given:
        def content = GsonUtil.toJson(new OsdCondition(priceIds: [1L]))
        service.queryOsdActivityByPage(_ as QueryActivityPara, _ as Integer, _ as Integer) >> [new ActOsdactinfo(id: 1L, conditions: content)]
        service.queryOsdActivityCount(_ as QueryActivityPara) >> 1
        service.queryOsdActivityLabel(_ as List<Long>) >> [new ActOsdlabel(activityId: 1L, labelId: 1L)]

        expect:
        CollectionUtils.isNotEmpty(testInstance.doBusiness(request as QueryAllOsdActivityListRequestType).getActivityList()) == result

        where:
        request                                                         || result
        new QueryAllOsdActivityListRequestType()                        || false
        new QueryAllOsdActivityListRequestType(pageNo: 1, pageSize: 10) || true
    }
}
