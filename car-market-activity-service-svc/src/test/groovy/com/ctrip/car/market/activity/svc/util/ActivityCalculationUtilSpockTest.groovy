package com.ctrip.car.market.activity.svc.util

import com.ctrip.car.market.activity.svc.bo.Activity
import com.ctrip.car.market.common.entity.act.ActivityTempContent
import com.ctrip.car.market.common.entity.act.CustomContent
import com.ctrip.car.market.common.entity.act.CustomDeductionStrategy
import com.ctrip.car.market.common.entity.act.CustomDeductionStrategyItem
import com.ctrip.car.market.common.entity.act.DeductionStrategy
import com.ctrip.car.market.common.entity.act.DeductionStrategyItem
import com.ctrip.car.market.job.common.entity.ActInfoDO
import com.ctrip.car.market.job.common.entity.ActTempInfoDO
import spock.lang.Specification

class ActivityCalculationUtilSpockTest extends Specification {

    private testInstance = new ActivityCalculationUtil()

    def test_getDeductionStrategy() {
        def testMethod = testInstance.getClass().getDeclaredMethod("getDeductionStrategy", Activity.class)
        testMethod.setAccessible(true)

        expect:
        ((DeductionStrategy) testMethod.invoke(testInstance, activity as Activity)).getDeductionStrategyList().size() == result

        where:
        activity                                                                                                                      | result
        new Activity(new ActInfoDO(
                customContent: JsonUtil.toString(new CustomContent(deductionStrategy:
                        new CustomDeductionStrategy(deductionAmount: BigDecimal.ONE, deductionStrategyList: [
                                new CustomDeductionStrategyItem(deductionAmount: BigDecimal.ONE, startValue: 1)])))),
                new ActTempInfoDO(content: new ActivityTempContent(deductionStrategy: new DeductionStrategy(deductionStrategyList: [
                        new DeductionStrategyItem(deductionAmount: BigDecimal.ONE, startValue: 1)
                ]))), 0)                                                                                                              | 1

        new Activity(new ActInfoDO(
                customContent: JsonUtil.toString(new CustomContent(deductionStrategy:
                        new CustomDeductionStrategy(deductionAmount: BigDecimal.ONE, deductionStrategyList: [
                                new CustomDeductionStrategyItem(deductionAmount: BigDecimal.ONE, startValue: 1),
                                new CustomDeductionStrategyItem(deductionAmount: BigDecimal.TEN, startValue: 2)])))),
                new ActTempInfoDO(content: new ActivityTempContent(deductionStrategy: new DeductionStrategy(deductionStrategyList: [
                        new DeductionStrategyItem(deductionAmount: BigDecimal.ONE, startValue: 1)
                ]))), 0)                                                                                                              | 2
    }
}
