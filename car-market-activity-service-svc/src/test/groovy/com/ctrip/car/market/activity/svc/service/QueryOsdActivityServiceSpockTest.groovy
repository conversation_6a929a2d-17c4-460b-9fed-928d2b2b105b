package com.ctrip.car.market.activity.svc.service

import com.ctrip.car.market.activity.contract.BaseRequest
import com.ctrip.car.market.activity.contract.QueryOsdActivityRequestType
import com.ctrip.car.market.activity.contract.dto.LabelItem
import com.ctrip.car.market.activity.repository.entity.ActOsdactinfo
import com.ctrip.car.market.activity.svc.bo.OsdActivity
import com.ctrip.car.market.activity.svc.bo.TmsLanguageDto
import com.ctrip.car.market.activity.svc.cache.LabelCache
import com.ctrip.car.market.activity.svc.cache.OsdActivityCache
import com.ctrip.car.market.activity.svc.cache.OsdActivityLabelCache
import com.ctrip.car.market.activity.svc.config.BaseConfig
import com.ctrip.car.market.activity.svc.proxy.CarCommodityServiceProxy
import com.ctrip.car.market.activity.svc.proxy.TranslateServiceProxy
import com.ctrip.car.market.common.entity.act.LimitDate
import com.ctrip.car.market.common.entity.act.OsdCondition
import com.ctrip.car.market.job.common.entity.CpnLabelDO
import com.ctrip.car.osd.translate.dto.TranslateResponseInfo
import com.ctrip.car.osd.translate.dto.TranslateResultInfo
import com.google.common.collect.Sets
import org.junit.platform.commons.util.CollectionUtils
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification

class QueryOsdActivityServiceSpockTest extends Specification {


    private osdActivityCache = Mock(OsdActivityCache)

    private osdActivityLabelCache = Mock(OsdActivityLabelCache)

    private baseConfig = Mock(BaseConfig)

    private labelCache = Mock(LabelCache)

    private testInstance = new QueryOsdActivityService(
            osdActivityCache: osdActivityCache,
            osdActivityLabelCache: osdActivityLabelCache,
            baseConfig: baseConfig,
            labelCache: labelCache
    )


    def test_getTranslate() {
        def testMethod = testInstance.getClass().getDeclaredMethod("getTranslate", List.class, Long.class, TmsLanguageDto.class)
        testMethod.setAccessible(true)

        expect:
        Objects.isNull(testMethod.invoke(testInstance, translateList as List, 1L, language as TmsLanguageDto)) == result

        where:
        translateList                                                                                                                             | language                                   || result
        [new TranslateResponseInfo(standardKey: "carmarketingdb|cpn_label|Name|Code|1", results: [new TranslateResultInfo("EN", "test")])]        | null                                       || true

        [new TranslateResponseInfo(standardKey: "carmarketingdb|cpn_label|Name|Code|1", results: [new TranslateResultInfo("EN", "test")]),
         new TranslateResponseInfo(standardKey: "carmarketingdb|cpn_label|Description|Code|1", results: [new TranslateResultInfo("EN", "test")])] | new TmsLanguageDto(tmsLanguageValue: "EN") || false

        [new TranslateResponseInfo(standardKey: "carmarketingdb|cpn_label|Name|Code|1", results: [new TranslateResultInfo("EN", "test")])]        | new TmsLanguageDto(tmsLanguageValue: "EN") || true
    }

    def test_getLabel() {
        def testMethod = testInstance.getClass().getDeclaredMethod("getLabel", String.class, List.class)
        testMethod.setAccessible(true)

        given:
        labelCache.getLabel(_ as List<Long>) >> labels
        baseConfig.getTmsSupportLanguages() >> tms

        def tResult = [new TranslateResponseInfo(standardKey: "carmarketingdb|cpn_label|Name|Code|1", results: [new TranslateResultInfo("EN", "test")]),
                       new TranslateResponseInfo(standardKey: "carmarketingdb|cpn_label|Description|Code|1", results: [new TranslateResultInfo("EN", "test")])]
        try (MockedStatic<TranslateServiceProxy> translateServiceProxyMockedStatic = Mockito.mockStatic(TranslateServiceProxy.class)) {
            translateServiceProxyMockedStatic.when(() -> TranslateServiceProxy.getTranslate(Mockito.anyLong(), Mockito.anyString())).thenReturn(tResult)
        }

        expect:
        ((List<LabelItem>) testMethod.invoke(testInstance, locale as String, codes as List)).size() == result

        where:
        locale  | codes | labels                                                        | tms                                                               || result
        "zh_cn" | []    | [new CpnLabelDO(code: 1L, name: "test", description: "test")] | [new TmsLanguageDto(baselocale: "en_us", tmsLanguageValue: "EN")] || 0
        "zh_cn" | [1L]  | []                                                            | [new TmsLanguageDto(baselocale: "en_us", tmsLanguageValue: "EN")] || 0
        "zh_cn" | [1L]  | [new CpnLabelDO(code: 1L, name: "test", description: "test")] | [new TmsLanguageDto(baselocale: "en_us", tmsLanguageValue: "EN")] || 1
        "en_us" | [1L]  | [new CpnLabelDO(code: 1L, name: "test", description: "test")] | [new TmsLanguageDto(baselocale: "en_us", tmsLanguageValue: "EN")] || 1
        "en_us" | [1L]  | [new CpnLabelDO(code: 1L, name: "test", description: "test")] | [new TmsLanguageDto(baselocale: "ge_ge", tmsLanguageValue: "GE")] || 1
    }

    def test_doBusiness() {
        given:
        osdActivityCache.getVendorAct(_ as Long) >> actList
        try (MockedStatic<CarCommodityServiceProxy> carCommodityServiceProxyMockedStatic = Mockito.mockStatic(CarCommodityServiceProxy.class)) {
            carCommodityServiceProxyMockedStatic.when(() -> CarCommodityServiceProxy.queryArea(Mockito.anyInt(), Mockito.anyString())).thenReturn(Sets.newHashSet(1L, 2L))
        }

        expect:
        org.apache.commons.collections.CollectionUtils.isNotEmpty(testInstance.doBusiness(request as QueryOsdActivityRequestType).getActivityList()) == result

        where:
        actList                                                       | request                                                                                                                                                                           || result
        []                                                            | new QueryOsdActivityRequestType()                                                                                                                                                 || false
        []                                                            | new QueryOsdActivityRequestType(baseRequest: new BaseRequest())                                                                                                                   || false
        []                                                            | new QueryOsdActivityRequestType(baseRequest: new BaseRequest(), cityId: 1L)                                                                                                       || false
        []                                                            | new QueryOsdActivityRequestType(baseRequest: new BaseRequest(), cityId: 1L, pickUpTime: getDate(1), returnTime: getDate(3))                                                       || false
        []                                                            | new QueryOsdActivityRequestType(baseRequest: new BaseRequest(), cityId: 1L, pickUpTime: getDate(1), returnTime: getDate(3), vendorId: 1L)                                         || false
        []                                                            | new QueryOsdActivityRequestType(baseRequest: new BaseRequest(), cityId: 1L, pickUpTime: getDate(1), returnTime: getDate(3), vendorId: 1L, standardProductId: 1L)                  || false
        [new OsdActivity(condition: new OsdCondition(activityTimeRange: [
                new LimitDate(start: getDate(-10), end: getDate(10))
        ]), act: new ActOsdactinfo(type: 1))]                         | new QueryOsdActivityRequestType(baseRequest: new BaseRequest(requestId: "test"), cityId: 1L, pickUpTime: getDate(1), returnTime: getDate(3), vendorId: 1L, standardProductId: 1L) || true
    }

    def getDate(int day) {
        Calendar calendar = Calendar.getInstance()
        calendar.add(Calendar.DATE, day)
        return calendar;
    }
}
