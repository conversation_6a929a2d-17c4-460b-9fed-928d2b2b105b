package com.ctrip.car.market.activity.svc.service

import com.ctrip.car.market.activity.contract.ActivityDetailRequestType
import com.ctrip.car.market.activity.contract.ActivityDetailResponseType
import com.ctrip.car.market.activity.contract.dto.ActivityDto
import com.ctrip.car.market.activity.repository.entity.ActCtripactinfo
import com.ctrip.car.market.activity.repository.entity.ActCtriptempinfo
import com.ctrip.car.market.activity.repository.entity.CpnLabel
import com.ctrip.car.market.activity.repository.service.ActivityService
import spock.lang.Specification
import spock.lang.Unroll

class ActivityDetailServiceSpockTest extends Specification {

    def service = Mock(ActivityService)
    def queryAllActivityListService = Mock(QueryAllActivityListService)

    private testInstance = new ActivityDetailService(
            service: service,
            queryAllActivityListService: queryAllActivityListService
    )

    // 测试doBusiness方法 - 单个活动ID正常流程
    def "test doBusiness with single activity id"() {
        given:
        def request = new ActivityDetailRequestType(activityId: 1L)
        def actInfo = createMockActivity(1L, 1L)
        def tempInfo = createMockTemplate(1L)
        def activityDto = createMockActivityDto()
        def label = createMockLabel("Test Label")

        when:
        def response = testInstance.doBusiness(request)

        then:
        1 * service.queryByPk(1L) >> actInfo
        1 * service.queryByTempId(1L) >> tempInfo
        1 * queryAllActivityListService.convert(tempInfo, actInfo) >> activityDto
        1 * service.queryLabel(1) >> label

        response.baseResponse.message == "success"
        response.activityDetail != null
        response.activityDetail.remark == "Test Label"
        response.activityList == null
    }

    // 测试doBusiness方法 - 同时提供单个和多个活动ID
    def "test doBusiness with both single and multiple activity ids"() {
        given:
        def request = new ActivityDetailRequestType(
                activityId: 1L,
                activityIdList: [2L, 3L]
        )
        def actInfo1 = createMockActivity(1L, 1L)
        def actInfo2 = createMockActivity(2L, 2L)
        def actInfo3 = createMockActivity(3L, 3L)
        def tempInfo1 = createMockTemplate(1L)
        def tempInfo2 = createMockTemplate(2L)
        def tempInfo3 = createMockTemplate(3L)
        def activityDto = createMockActivityDto()

        when:
        def response = testInstance.doBusiness(request)

        then:
        1 * service.queryByPk(1L) >> actInfo1
        1 * service.queryByPk(2L) >> actInfo2
        1 * service.queryByPk(3L) >> actInfo3
        1 * service.queryByTempId(1L) >> tempInfo1
        1 * service.queryByTempId(2L) >> tempInfo2
        1 * service.queryByTempId(3L) >> tempInfo3
        3 * queryAllActivityListService.convert(_, _) >> activityDto
        3 * service.queryLabel(_) >> null

        response.baseResponse.message == "success"
        response.activityDetail != null
        response.activityList != null
        response.activityList.size() == 2
    }

    // 测试doBusiness方法 - 参数校验失败
    @Unroll
    def "test doBusiness with invalid parameters"() {
        when:
        def response = testInstance.doBusiness(request)

        then:
        0 * service._
        0 * queryAllActivityListService._

        !response.baseResponse.success
        response.baseResponse.message == "activity id is null"
        response.activityDetail == null
        response.activityList == null

        where:
        request << [
                new ActivityDetailRequestType(),
                new ActivityDetailRequestType(activityId: 0L),
                new ActivityDetailRequestType(activityId: -1L),
                new ActivityDetailRequestType(activityIdList: [])
        ]
    }

    // 测试singleActivity方法 - 正常流程
    def "test singleActivity with valid data"() {
        given:
        def actInfo = createMockActivity(1L, 1L)
        def tempInfo = createMockTemplate(1L)
        def activityDto = createMockActivityDto()
        def label = createMockLabel("Test Description")

        when:
        def result = testInstance.singleActivity(1L)

        then:
        1 * service.queryByPk(1L) >> actInfo
        1 * service.queryByTempId(1L) >> tempInfo
        1 * queryAllActivityListService.convert(tempInfo, actInfo) >> activityDto
        1 * service.queryLabel(1) >> label

        result != null
        result.remark == "Test Description"
    }

    // 测试singleActivity方法 - 活动不存在
    def "test singleActivity with non-existent activity"() {
        when:
        def result = testInstance.singleActivity(999L)

        then:
        1 * service.queryByPk(999L) >> null
        0 * service.queryByTempId(_)
        0 * queryAllActivityListService.convert(_, _)
        0 * service.queryLabel(_)

        result == null
    }

    // 测试singleActivity方法 - 模板不存在
    def "test singleActivity with non-existent template"() {
        given:
        def actInfo = createMockActivity(1L, 999L)

        when:
        def result = testInstance.singleActivity(1L)

        then:
        1 * service.queryByPk(1L) >> actInfo
        1 * service.queryByTempId(999L) >> null
        0 * queryAllActivityListService.convert(_, _)
        0 * service.queryLabel(_)

        result == null
    }

    // 测试singleActivity方法 - 异常处理
    def "test singleActivity with exception"() {
        when:
        def result = testInstance.singleActivity(1L)

        then:
        1 * service.queryByPk(1L) >> { throw new RuntimeException("Database error") }

        result == null
    }

    // 测试singleActivity方法 - 无标签情况
    def "test singleActivity without label"() {
        given:
        def actInfo = createMockActivity(1L, 1L)
        def tempInfo = createMockTemplate(1L)
        def activityDto = createMockActivityDto()
        activityDto.remark = "Original Remark"

        when:
        def result = testInstance.singleActivity(1L)

        then:
        1 * service.queryByPk(1L) >> actInfo
        1 * service.queryByTempId(1L) >> tempInfo
        1 * queryAllActivityListService.convert(tempInfo, actInfo) >> activityDto
        1 * service.queryLabel(1) >> null

        result != null
        result.remark == "Original Remark"  // 保持原有的remark
    }

    // 测试multipleActivity方法 - 正常流程
    def "test multipleActivity with valid data"() {
        given:
        def actInfo1 = createMockActivity(1L, 1L)
        def actInfo2 = createMockActivity(2L, 2L)
        def tempInfo1 = createMockTemplate(1L)
        def tempInfo2 = createMockTemplate(2L)
        def activityDto1 = createMockActivityDto()
        def activityDto2 = createMockActivityDto()

        when:
        def result = testInstance.multipleActivity([1L, 2L])

        then:
        1 * service.queryByPk(1L) >> actInfo1
        1 * service.queryByPk(2L) >> actInfo2
        1 * service.queryByTempId(1L) >> tempInfo1
        1 * service.queryByTempId(2L) >> tempInfo2
        1 * queryAllActivityListService.convert(tempInfo1, actInfo1) >> activityDto1
        1 * queryAllActivityListService.convert(tempInfo2, actInfo2) >> activityDto2
        2 * service.queryLabel(_) >> null

        result != null
        result.size() == 2
    }

    // 测试multipleActivity方法 - 部分活动存在
    def "test multipleActivity with partial existing activities"() {
        given:
        def actInfo1 = createMockActivity(1L, 1L)
        def tempInfo1 = createMockTemplate(1L)
        def activityDto1 = createMockActivityDto()

        when:
        def result = testInstance.multipleActivity([1L, 999L])

        then:
        1 * service.queryByPk(1L) >> actInfo1
        1 * service.queryByPk(999L) >> null
        1 * service.queryByTempId(1L) >> tempInfo1
        1 * queryAllActivityListService.convert(tempInfo1, actInfo1) >> activityDto1
        1 * service.queryLabel(_) >> null

        result != null
        result.size() == 1
    }

    // 测试multipleActivity方法 - 所有活动都不存在
    def "test multipleActivity with no existing activities"() {
        when:
        def result = testInstance.multipleActivity([998L, 999L])

        then:
        1 * service.queryByPk(998L) >> null
        1 * service.queryByPk(999L) >> null

        result != null
        result.size() == 0
    }

    // 测试multipleActivity方法 - 空列表
    def "test multipleActivity with empty list"() {
        when:
        def result = testInstance.multipleActivity([])

        then:
        0 * service._

        result != null
        result.size() == 0
    }

    // 辅助方法：创建模拟的Activity实体
    private ActCtripactinfo createMockActivity(Long id, Long tempId) {
        return new ActCtripactinfo(
                id: id,
                tempId: tempId,
                vendorId: 100L,
                vendorCouponCode: "COUPON123"
        )
    }

    // 辅助方法：创建模拟的Template实体
    private ActCtriptempinfo createMockTemplate(Long tmpId) {
        return new ActCtriptempinfo(
                tmpId: tmpId,
                templateType: 1,
                groupId: 1L,
                labelId: 1,
                name: "Test Activity",
                remark: "Test Remark",
                priority: 1
        )
    }

    // 辅助方法：创建模拟的ActivityDto
    private ActivityDto createMockActivityDto() {
        def dto = new ActivityDto()
        dto.activityId = 1L
        dto.name = "Test Activity"
        dto.remark = "Test Remark"
        return dto
    }

    // 辅助方法：创建模拟的Label
    private CpnLabel createMockLabel(String description) {
        def label = new CpnLabel()
        label.code = 1L
        label.name = "Test Label"
        label.description = description
        return label
    }
}
