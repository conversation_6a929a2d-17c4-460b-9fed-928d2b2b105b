package com.ctrip.car.market.activity.svc.service

import com.ctrip.car.market.activity.contract.BaseRequest
import com.ctrip.car.market.activity.contract.UseActivityRequestType
import com.ctrip.car.market.activity.contract.UseActivityResponseType
import com.ctrip.car.market.activity.repository.entity.ActCtripactinfo
import com.ctrip.car.market.activity.repository.entity.CpnUseactivity
import com.ctrip.car.market.activity.repository.service.ActivityService
import spock.lang.Specification

import java.sql.Timestamp

class UseActivityServiceSpockTest extends Specification {

    private activityService = Mock(ActivityService)

    private testInstance = new UseActivityService(
            activityService: activityService
    )

    def test_checkRequest() {
        def testMethod = testInstance.getClass().getDeclaredMethod("checkRequest", UseActivityRequestType.class, UseActivityResponseType.class)
        testMethod.setAccessible(true)

        expect:
        ((boolean) testMethod.invoke(testInstance, request as UseActivityRequestType, new UseActivityResponseType())) == result

        where:
        request                                                                                                                                                                 || result
        new UseActivityRequestType()                                                                                                                                            || false
        new UseActivityRequestType(baseRequest: new BaseRequest())                                                                                                              || false
        new UseActivityRequestType(baseRequest: new BaseRequest(uid: "test"))                                                                                                   || false
        new UseActivityRequestType(baseRequest: new BaseRequest(uid: "test"), activityId: 1L)                                                                                   || false
        new UseActivityRequestType(baseRequest: new BaseRequest(uid: "test"), activityId: 1L, orderId: 1L)                                                                      || false
        new UseActivityRequestType(baseRequest: new BaseRequest(uid: "test"), activityId: 1L, orderId: 1L, activityAmount: BigDecimal.TEN)                                      || false
        new UseActivityRequestType(baseRequest: new BaseRequest(uid: "test"), activityId: 1L, orderId: 1L, activityAmount: BigDecimal.TEN, orderOriginalAmount: BigDecimal.TEN) || true
    }

    def test_doBusiness() {
        given:
        activityService.queryByPk(_ as Long) >> new ActCtripactinfo(status: 1, startTime: new Timestamp(System.currentTimeMillis() - 100000), endTime: new Timestamp(System.currentTimeMillis() + 100000))
        activityService.queryUseActivity(_ as Long, _ as Long, _ as String) >> []
        activityService.insertUseActivity(_ as CpnUseactivity) >> 1

        expect:
        testInstance.doBusiness(request as UseActivityRequestType).getBaseResponse().getMessage() == result

        where:
        request                                                                                                                                                                 || result
        new UseActivityRequestType(baseRequest: new BaseRequest(uid: "test"), activityId: 1L, orderId: 1L, activityAmount: BigDecimal.TEN, orderOriginalAmount: BigDecimal.TEN) || "success"
    }
}
