package com.ctrip.car.market.activity.svc.service

import com.ctrip.car.market.activity.contract.BaseRequest
import com.ctrip.car.market.activity.contract.CancelActivityRequestType
import com.ctrip.car.market.activity.contract.CancelActivityResponseType
import com.ctrip.car.market.activity.repository.entity.CpnUseactivity
import com.ctrip.car.market.activity.repository.service.ActivityService
import spock.lang.Specification

class CancelActivityServiceSpockTest extends Specification {

    private activityService = Mock(ActivityService)

    private testInstance = new CancelActivityService(
            activityService: activityService
    )


    def test_checkRequest() {
        def testMethod = testInstance.getClass().getDeclaredMethod("checkRequest", CancelActivityRequestType.class, CancelActivityResponseType.class)
        testMethod.setAccessible(true)

        expect:
        ((boolean) testMethod.invoke(testInstance, request as CancelActivityRequestType, new CancelActivityResponseType())) == result

        where:
        request                                                                                               || result
        new CancelActivityRequestType()                                                                       || false
        new CancelActivityRequestType(baseRequest: new BaseRequest())                                         || false
        new CancelActivityRequestType(baseRequest: new BaseRequest(uid: "test"))                              || false
        new CancelActivityRequestType(baseRequest: new BaseRequest(uid: "test"), activityId: 1L)              || false
        new CancelActivityRequestType(baseRequest: new BaseRequest(uid: "test"), activityId: 1L, orderId: 1L) || true
    }

    def test_doBusiness() {
        given:
        activityService.queryUseActivity(_ as Long, _ as Long, _ as String) >> useList
        activityService.updateUseActivity(_ as CpnUseactivity) >> 1

        expect:
        testInstance.doBusiness(request as CancelActivityRequestType).getBaseResponse().getMessage() == result

        where:
        request                                                                                               | useList                || result
        new CancelActivityRequestType(baseRequest: new BaseRequest(uid: "test"), activityId: 1L, orderId: 1L) | []                     || "no use record"
        new CancelActivityRequestType(baseRequest: new BaseRequest(uid: "test"), activityId: 1L, orderId: 1L) | [new CpnUseactivity()] || "success"
    }
}
