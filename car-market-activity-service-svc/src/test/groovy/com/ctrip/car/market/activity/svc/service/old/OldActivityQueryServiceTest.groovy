package com.ctrip.car.market.activity.svc.service.old

import com.ctrip.car.market.activity.contract.ActivityDetailRequestType
import com.ctrip.car.market.activity.contract.ActivityDetailResponseType
import com.ctrip.car.market.activity.repository.entity.old.CpnActivityTemplate
import com.ctrip.car.market.activity.repository.entity.old.CpnActivityconfig
import com.ctrip.car.market.activity.repository.entity.old.content.CpnActivityTemplateRegisterForm
import com.ctrip.car.market.activity.repository.service.old.OldActivityService
import com.ctrip.car.market.activity.svc.util.JsonUtil
import com.ctrip.car.sdcommon.utils.LogUtils
import spock.lang.Specification

class OldActivityQueryServiceTest extends Specification {

    private final static Long newActivityId = 200000L;

    def log = Mock(LogUtils.class)

    def oldActivityService = Mock(OldActivityService)

    def testInstance = new OldActivityQueryService(log: log, oldActivityService: oldActivityService)

    def setup() {

    }

    def "test_checkRequest"() {
        setup: ""

        and: ""

        when: ""
        def request1 = new ActivityDetailRequestType()
        def response1 = new ActivityDetailResponseType()
        def result1 = testInstance.checkRequest(request1, response1)

        def request2 = new ActivityDetailRequestType(activityId: -1L)
        def response2 = new ActivityDetailResponseType()
        def result2 = testInstance.checkRequest(request2, response2)

        def request3 = new ActivityDetailRequestType(activityId: newActivityId + 1L)
        def response3 = new ActivityDetailResponseType()
        def result3 = testInstance.checkRequest(request3, response3)

        def request4 = new ActivityDetailRequestType(activityId: newActivityId - 1L)
        def response4 = new ActivityDetailResponseType()
        def result4 = testInstance.checkRequest(request4, response4)

        then: ""
        Objects.equals(false, result1)
        Objects.equals("activityId is null", response1.getBaseResponse().getMessage())

        Objects.equals(false, result2)
        Objects.equals("activityId is invalid", response2.getBaseResponse().getMessage())

        Objects.equals(false, result3)
        Objects.equals("activityId is invalid", response3.getBaseResponse().getMessage())

        Objects.equals(true, result4)
    }

    def "test_getActivityDetail_config is null"() {
        setup: ""
        def configId = newActivityId - 1L

        and: ""
        oldActivityService.queryActivityByConfigId(_ as Long) >> null

        when: ""
        def request = new ActivityDetailRequestType(activityId: configId)
        def result = testInstance.getActivityDetail(request)

        then: ""
        Objects.equals(result.getBaseResponse().getCode(), "100000")
        Objects.equals(result.getBaseResponse().getMessage(), "configId/activityId non-exist")
    }

    def "test_getActivityDetail_shortest route"() {
        setup: ""
        def configId = newActivityId - 1L

        and: ""
        oldActivityService.queryActivityByConfigId(_ as Long) >> new CpnActivityconfig(configId: configId, templateId: 1L)

        and: ""
        oldActivityService.queryActivityTemp(_ as Long) >> new CpnActivityTemplate(templateId: 1L)

        when: ""
        def request = new ActivityDetailRequestType(activityId: configId)
        def result = testInstance.getActivityDetail(request)

        then: ""
        Objects.nonNull(result.getActivityDetail())
        Objects.equals(configId, result.getActivityDetail().getActivityId())
    }

    def "test_getActivityDetail_enter all branch"() {
        setup: ""
        def configId = newActivityId - 1L

        and: ""
        oldActivityService.queryActivityByConfigId(_ as Long) >> new CpnActivityconfig(
                configId: configId,
                templateId: 1L,
                labelId: 1L,
                vendorIds: "100",
                activityTime: "2000-01-01 00:00:00,2000-12-31 23:23:59",
                status: 1,
                exCludeFees: "A",
                cityIds: "1,2,3",
                pickTime: "2000-01-01,2000-12-31",
                rentDay: "1,10"
        )

        and: ""
        def templateConfig = new CpnActivityTemplateRegisterForm(shareDetailDTO: new CpnActivityTemplateRegisterForm.ShareDetailDTO(costShare: 10))
        oldActivityService.queryActivityTemp(_ as Long) >> new CpnActivityTemplate(
                templateId: 1L,
                registerFormConfig: JsonUtil.toString(templateConfig)
        )

        when: ""
        def request = new ActivityDetailRequestType(activityId: configId)
        def result = testInstance.getActivityDetail(request)

        then: ""
        Objects.nonNull(result.getActivityDetail())

        Objects.equals(configId, result.getActivityDetail().getActivityId())

        Objects.equals(100L, result.getActivityDetail().getVendorId())

        def activityStartTime = Calendar.getInstance()
        activityStartTime.set(2000, 1, 1, 0, 0, 0)
        Objects.equals(activityStartTime <=> result.getActivityDetail().getActivityStartTime(), 1)

        def pickStartTime = Calendar.getInstance()
        pickStartTime.set(2000, 1, 1)
        Objects.equals(pickStartTime <=> result.getActivityDetail().getActivityStartTime(), 1)

        Objects.equals(10, result.getActivityDetail().getShareDetail().getCostShare())
    }

}
