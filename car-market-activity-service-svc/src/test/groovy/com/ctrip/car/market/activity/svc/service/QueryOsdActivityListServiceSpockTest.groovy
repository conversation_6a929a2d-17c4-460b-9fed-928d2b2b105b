package com.ctrip.car.market.activity.svc.service

import com.ctrip.car.market.activity.contract.BaseRequest
import com.ctrip.car.market.activity.contract.QueryOsdActivityListRequestType
import com.ctrip.car.market.activity.contract.QueryOsdActivityListResponseType
import com.ctrip.car.market.activity.repository.entity.ActOsdactinfo
import com.ctrip.car.market.activity.svc.bo.OsdActivity
import com.ctrip.car.market.activity.svc.cache.OsdActivityCache
import com.ctrip.car.market.activity.svc.proxy.CarCommodityServiceProxy
import com.ctrip.car.market.common.entity.act.LimitDate
import com.ctrip.car.market.common.entity.act.OsdCondition
import com.google.common.collect.Sets
import org.apache.commons.collections.CollectionUtils
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification

class QueryOsdActivityListServiceSpockTest extends Specification {

    def osdActivityCache = Mock(OsdActivityCache)

    def testInstance = new QueryOsdActivityListService(
            osdActivityCache: osdActivityCache
    )

    def test_checkRequest() {
        expect:
        testInstance.checkRequest(request as QueryOsdActivityListRequestType, new QueryOsdActivityListResponseType()) == result

        where:
        request                                                                                                                         || result
        new QueryOsdActivityListRequestType()                                                                                           || false
        new QueryOsdActivityListRequestType(baseRequest: new BaseRequest())                                                             || false
        new QueryOsdActivityListRequestType(baseRequest: new BaseRequest(), cityId: 1L)                                                 || false
        new QueryOsdActivityListRequestType(baseRequest: new BaseRequest(), cityId: 1L, pickUpTime: getTime(1), returnTime: getTime(3)) || true
    }

    def getTime(int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, day)
        return calendar
    }

    def test_getAreaAct() {
        given:
        osdActivityCache.getAll() >> actList

        expect:
        CollectionUtils.isNotEmpty(testInstance.getAreaAct(citySet as Set<Long>)) == result

        where:
        actList                                                                             | citySet             || result
        []                                                                                  | Sets.newHashSet()   || false
        [new OsdActivity()]                                                                 | Sets.newHashSet()   || false
        [new OsdActivity(condition: new OsdCondition())]                                    | Sets.newHashSet()   || true
        [new OsdActivity(condition: new OsdCondition(excludeAreaId: true, areaIds: [1L]))]  | Sets.newHashSet()   || true
        [new OsdActivity(condition: new OsdCondition(excludeAreaId: false, areaIds: [1L]))] | Sets.newHashSet(1L) || true
    }

    def test_doBusiness() {
        given:
        try (MockedStatic<CarCommodityServiceProxy> mockedStatic = Mockito.mockStatic(CarCommodityServiceProxy.class)) {
            mockedStatic.when(() -> CarCommodityServiceProxy.queryArea(Mockito.anyInt(), Mockito.anyString())).thenReturn(Sets.newHashSet(1L))
        }
        osdActivityCache.getAll() >> [new OsdActivity(condition: new OsdCondition(excludeAreaId: false, areaIds: []
                , activityTimeRange: [new LimitDate(start: getTime(-10), end: getTime(10))]),act: new ActOsdactinfo(type: 1))]

        expect:
        CollectionUtils.isNotEmpty(testInstance.doBusiness(request as QueryOsdActivityListRequestType).getActivityList()) == result

        where:
        request                                                                                                                         || result
        new QueryOsdActivityListRequestType(baseRequest: new BaseRequest(), cityId: 1L, pickUpTime: getTime(1), returnTime: getTime(3)) || true

    }
}
