package com.ctrip.car.market.activity.svc.service

import com.ctrip.car.market.activity.contract.BaseRequest
import com.ctrip.car.market.activity.contract.QueryFlightActivityRequestType
import com.ctrip.car.market.activity.svc.bo.FlightActivityLabelItem
import com.ctrip.car.market.activity.svc.cache.neww.ActivityInfoCache
import com.ctrip.car.market.activity.svc.cache.neww.ActivityTempCache
import com.ctrip.car.market.activity.svc.config.BaseConfig
import com.ctrip.car.market.common.entity.act.ActivityTempContent
import com.ctrip.car.market.common.entity.act.DeductionStrategy
import com.ctrip.car.market.common.entity.act.SupplierCondition
import com.ctrip.car.market.common.entity.act.UserCondition
import com.ctrip.car.market.job.common.entity.ActInfoDO
import com.ctrip.car.market.job.common.entity.ActTempInfoDO
import spock.lang.Specification

import java.sql.Timestamp

class QueryFlightActivityServiceSpockTest extends Specification {

    def activityTempCache = Mock(ActivityTempCache)

    def activityInfoCache = Mock(ActivityInfoCache)

    def baseConfig = Mock(BaseConfig)

    def testInstance = new QueryFlightActivityService(
            activityTempCache: activityTempCache,
            activityInfoCache: activityInfoCache,
            baseConfig: baseConfig
    )

    def test_convert() {
        given:
        activityInfoCache.queryActId(_ as Long) >> [1L]
        activityInfoCache.queryById(_ as Long) >> new ActInfoDO(tempId: 1L, vendorId: 1L)

        expect:
        testInstance.convert(list as List<ActTempInfoDO>).size() == result

        where:
        list                                                                                     || result
        []                                                                                       || 0
        [new ActTempInfoDO(content: new ActivityTempContent(deductionStrategy: new DeductionStrategy()),
                labelId: 1L, tmpId: 1L)]                                                         || 1
    }

    def test_filter() {
        expect:
        testInstance.filter(cityId as Integer, channelId as Integer, list as List<ActTempInfoDO>).size() == result

        where:
        cityId | channelId | list                                                                                                         || result
        2      | 17522     | [new ActTempInfoDO(activityStartTime: getTime(-10), activityEndTime: getTime(-5))]                           || 0

        2      | 17522     | [new ActTempInfoDO(activityStartTime: getTime(-10), activityEndTime: getTime(10),
                content: new ActivityTempContent(supplierCondition: new SupplierCondition(cityIds: [2], excludeCity: true)))]             || 0

        2      | 17522     | [new ActTempInfoDO(activityStartTime: getTime(-10), activityEndTime: getTime(10),
                content: new ActivityTempContent(supplierCondition: new SupplierCondition(cityIds: [1], excludeCity: false)))]            || 0

        2      | 17522     | [new ActTempInfoDO(activityStartTime: getTime(-10), activityEndTime: getTime(10),
                content: new ActivityTempContent(userCondition: new UserCondition(channelIds: [17522], excludeChannel: true)))]           || 0

        2      | 17522     | [new ActTempInfoDO(activityStartTime: getTime(-10), activityEndTime: getTime(10),
                content: new ActivityTempContent(userCondition: new UserCondition(channelIds: [1], excludeChannel: false)))]              || 0

        2      | 17522     | [new ActTempInfoDO(activityStartTime: getTime(-10), activityEndTime: getTime(10),
                content: new ActivityTempContent(deductionStrategy: new DeductionStrategy(allowCustomization: true)))]                    || 0

        2      | 17522     | [new ActTempInfoDO(activityStartTime: getTime(-10), activityEndTime: getTime(10),
                content: new ActivityTempContent(deductionStrategy: new DeductionStrategy(allowCustomization: false, deductionType: 2)))] || 0

        2      | 17522     | [new ActTempInfoDO(activityStartTime: getTime(-10), activityEndTime: getTime(10),
                content: new ActivityTempContent(deductionStrategy: new DeductionStrategy(allowCustomization: false, deductionType: 1)))] || 1
    }

    def test_doBusiness() {
        given:
        activityTempCache.getAll() >> [new ActTempInfoDO(activityStartTime: getTime(-10), activityEndTime: getTime(10), tmpId: 1L, labelId: 1L,
                content: new ActivityTempContent(deductionStrategy: new DeductionStrategy(allowCustomization: false, deductionType: 1)))]

        baseConfig.getFlightActivityLabel() >> config
        activityInfoCache.queryActId(_ as Long) >> [1L]
        activityInfoCache.queryById(_ as Long) >> new ActInfoDO(tempId: 1L, vendorId: 1L)

        expect:
        testInstance.doBusiness(request as QueryFlightActivityRequestType).getBaseResponse().getMessage() == result

        where:
        config                                     | request                                                                                                    || result
        []                                         | new QueryFlightActivityRequestType()                                                                       || "parameter cannot be empty"
        []                                         | new QueryFlightActivityRequestType(baseRequest: new BaseRequest(channelId: 17522), cityId: 2)              || "flight config is null"
        [new FlightActivityLabelItem(labelId: 1L)] | new QueryFlightActivityRequestType(baseRequest: new BaseRequest(channelId: 17522), cityId: 2, labelId: 1L) || "success"
    }

    def getTime(int day) {
        Calendar calendar = Calendar.getInstance()
        calendar.add(Calendar.DATE, day)
        return new Timestamp(calendar.getTimeInMillis())
    }
}
