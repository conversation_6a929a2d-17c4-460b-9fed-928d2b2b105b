package com.ctrip.car.market.activity.svc.cache.neww;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheBean;
import com.alicp.jetcache.anno.IncUpdateConsumerByRedis;
import com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst;
import com.ctrip.car.market.job.common.consts.CacheName;
import com.ctrip.car.market.job.common.entity.ActTempInfoDO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@CreateCacheBean
public class ActivityTempCache {

    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.IsdActivityTempAllCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.ACT_TEMP_ALL_KEY)
    public Cache<String, List<Long>> cacheAll;


    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.IsdActivityTempCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.ACT_TEMP_KEY)
    public Cache<Long, ActTempInfoDO> cache;

    public List<ActTempInfoDO> getAll() {
        return Optional.ofNullable(cacheAll.get("ALL")).orElse(Lists.newArrayList()).stream().map(l -> cache.get(l)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public ActTempInfoDO queryByTmpId(Long tempId) {
        return cache.get(tempId);
    }
}
