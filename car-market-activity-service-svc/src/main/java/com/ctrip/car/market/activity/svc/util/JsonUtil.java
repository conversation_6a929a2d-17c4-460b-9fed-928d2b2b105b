package com.ctrip.car.market.activity.svc.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.common.base.Strings;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class JsonUtil {

    private static final ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, Boolean.TRUE);
        mapper.configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, Boolean.TRUE);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, Boolean.FALSE);
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, Boolean.FALSE);
    }

    public static <T> List<T> toList(String jsonStr, TypeReference<List<T>> valueTypeRef) {
        if (Strings.isNullOrEmpty(jsonStr)) {
            return null;
        }
        try {
            return mapper.readValue(jsonStr, valueTypeRef);
        } catch (Exception e) {
            return null;
        }
    }

    public static <T> T toObject(String jsonStr, Class<T> t) {
        try {
            if (StringUtils.isBlank(jsonStr)) {
                return null;
            }
            return mapper.readValue(jsonStr, t);
        } catch (Exception e) {
            return null;
        }
    }

    public static <T> String toString(T t) {
        try {
            return mapper.writeValueAsString(t);
        } catch (Exception e) {
            return null;
        }
    }
}
