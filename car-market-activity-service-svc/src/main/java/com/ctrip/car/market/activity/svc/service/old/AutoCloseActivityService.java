package com.ctrip.car.market.activity.svc.service.old;

import com.ctrip.car.market.activity.core.service.BaseService;
import com.ctrip.car.market.activity.repository.service.ActivityService;
import com.ctrip.car.market.activity.repository.service.old.OldActivityService;
import com.ctrip.car.market.activity.svc.enums.ActivityValidationErrorMsgEnum;
import com.ctrip.car.market.activity.svc.util.CarMarketCacheHelper;
import com.ctrip.car.market.activity.svc.util.EmailUtil;
import com.ctrip.car.market.activity.svc.util.EmailUtil.*;
import com.ctrip.car.market.activity.svc.util.QConfigHelper;
import com.ctrip.car.market.activity.repository.entity.ActCtripactinfo;
import com.ctrip.car.market.activity.repository.entity.old.CpnActivityconfig;
import com.ctrip.car.market.activity.svc.dto.old.ActivityInfoErrorInfoDTO;
import com.ctrip.car.market.activity.svc.dto.old.AutoCloseActivityDTO;
import com.ctrip.car.market.activity.svc.dto.old.AutoCloseActivityEmailDTO;
import com.ctrip.car.market.activity.svc.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.ctrip.car.market.activity.svc.enums.ActivityValidationErrorMsgEnum.*;

@Service
public class AutoCloseActivityService extends BaseService {

    private final static Long newActivityId = 200000L;

    private final static List<ActivityValidationErrorMsgEnum> notWithinActivityTimeErrorMsgEnumList = Arrays.asList(FILTER_ACTIVITY_TIME, FILTER_EXCLUDE_DATE, FILTER_REPETITION_PERIOD);

    protected String AUTO_CLOSE_REDIS_KEY = "AutoClose_configId_%s";

    @Autowired
    protected OldActivityService oldActivityService;

    @Autowired
    protected ActivityService activityService;

    @Autowired
    protected CarMarketCacheHelper carMarketCacheHelper;

    @Autowired
    protected QConfigHelper qConfigHelper;

    public void autoCloseActivityServiceV2(ActivityInfoErrorInfoDTO activityInfoErrorInfoDTO) {
        Long configId = activityInfoErrorInfoDTO.getConfigId();
        getHandlerContext().writeTagMap("configId", configId + "");
        try {
            CpnActivityconfig oldConfig = null;
            ActCtripactinfo newConfig = null;
            boolean isNewActivity = configId >= newActivityId;
            if (isNewActivity) {
                newConfig = activityService.queryByPk(configId);
            } else {
                oldConfig = oldActivityService.queryActivityByConfigId(configId);
            }
            // 1. 判断活动是否存在
            if ((Objects.isNull(oldConfig) || Objects.isNull(oldConfig.getStatus())) &&
                    (Objects.isNull(newConfig) || Objects.isNull(newConfig.getStatus()))) {
                log.warn("AutoCloseActivityService.autoCloseActivityServiceV2", "configId or configStatus not exist", getHandlerContext().getTagMap());
                return;
            }
            // 2. 判断活动是否已经被关闭
            if ((Objects.nonNull(oldConfig) && oldConfig.getStatus() > 1) || (Objects.nonNull(newConfig) && !Objects.equals(newConfig.getStatus(), 1))) {
                log.warn("AutoCloseActivityService.autoCloseActivityServiceV2", "configId closed", getHandlerContext().getTagMap());
                return;
            }
            if (Objects.nonNull(newConfig) && notWithinActivityTimeErrorInfo(activityInfoErrorInfoDTO.getErrorInfo())) {
                log.warn("AutoCloseActivityService.autoCloseActivityServiceV2", "not within activity time", getHandlerContext().getTagMap());
                return;
            }
            log.info("AutoCloseActivityService.autoCloseActivityServiceV2Start", JsonUtil.toString(activityInfoErrorInfoDTO), getHandlerContext().getTagMap());
            long timeMillis = Long.parseLong(qConfigHelper.getConfigMap().getOrDefault("AutoCloseTimeMillis", "10800000"));
            String key = String.format(AUTO_CLOSE_REDIS_KEY, configId);
            AutoCloseActivityDTO autoCloseActivityDTO = new AutoCloseActivityDTO();
            autoCloseActivityDTO.setUid(activityInfoErrorInfoDTO.getUid());
            autoCloseActivityDTO.setConfigId(configId);
            autoCloseActivityDTO.setVendorId(activityInfoErrorInfoDTO.getVendorId());
            autoCloseActivityDTO.setCreateTime(System.currentTimeMillis());
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.HOUR_OF_DAY, 3);
            autoCloseActivityDTO.setInValidTime(calendar.getTimeInMillis());
            autoCloseActivityDTO.setRequestId(activityInfoErrorInfoDTO.getRequestId());
            autoCloseActivityDTO.setCityName(activityInfoErrorInfoDTO.getPickUpCityName());
            autoCloseActivityDTO.setCityId(activityInfoErrorInfoDTO.getPickUpCityId());
            autoCloseActivityDTO.setReason(activityInfoErrorInfoDTO.getErrorInfo());
            autoCloseActivityDTO.setVehicleId(activityInfoErrorInfoDTO.getVehicleId());
            autoCloseActivityDTO.setVehicleName(activityInfoErrorInfoDTO.getVehicleName());
            autoCloseActivityDTO.setVendorName(activityInfoErrorInfoDTO.getVendorName());
            autoCloseActivityDTO.setPickVendorStoreId(activityInfoErrorInfoDTO.getPickVendorStoreId());
            autoCloseActivityDTO.setPickVendorStoreName(activityInfoErrorInfoDTO.getPickVendorStoreName());
            autoCloseActivityDTO.setReturnVendorStoreId(activityInfoErrorInfoDTO.getReturnVendorStoreId());
            autoCloseActivityDTO.setReturnVendorStoreName(activityInfoErrorInfoDTO.getReturnVendorStoreName());
            String content = carMarketCacheHelper.getString(key);
            log.info("AutoCloseActivityService.cacheHelper.content", content, getHandlerContext().getTagMap());
            List<AutoCloseActivityDTO> result = new ArrayList<>();
            // 3. 下线活动（不用马上下线，先存到 Redis 里，当 Redis 里的数据超过一定数量再下线）
            if (StringUtils.isNotEmpty(content)) {
                result = JsonUtil.toList(content, new TypeReference<List<AutoCloseActivityDTO>>() {
                });
                if (CollectionUtils.isNotEmpty(result)) {
                    result.add(autoCloseActivityDTO);
                    List<AutoCloseActivityDTO> tmpResult = result.stream().filter(x -> x.getCreateTime() >= (System.currentTimeMillis() - timeMillis)).collect(Collectors.toList());
                    log.info("AutoCloseActivityService.tmpResult", JsonUtil.toString(tmpResult), getHandlerContext().getTagMap());
                    Optional<AutoCloseActivityDTO> minAutoCloseActivityDto = result.stream().min(Comparator.comparing(AutoCloseActivityDTO::getCreateTime));
                    if (minAutoCloseActivityDto.isPresent()) {
                        if (tmpResult.size() >= 3) {
                            // 3.1 超过三小时

                            // 3.2 超过 3 个不同 uid 或车型，使用活动失败，则下线活动
                            if (result.stream().map(AutoCloseActivityDTO::getUid).distinct().collect(Collectors.toList()).size() >= 3 ||
                                    result.stream().map(AutoCloseActivityDTO::getVehicleId).distinct().collect(Collectors.toList()).size() >= 3) {
                                // 3.3 下线
                                if (qConfigHelper.getConfigMap().getOrDefault("openAutoClose", "close").equalsIgnoreCase("open")) {
                                    if (isNewActivity) {
                                        newConfig.setStatus(0);
                                        newConfig.setModifyUser("autoClose");
                                        activityService.updateActivity(newConfig);
                                    } else {
                                        oldConfig.setStatus(99);
                                        oldConfig.setDatachangeLasttime(new Timestamp(System.currentTimeMillis()));
                                        oldConfig.setUpdateMan("autoClose");
                                        oldActivityService.updateActivityByEntity(oldConfig);
                                    }
                                    sendCloseEmail(result);
                                    String key2 = "8873_CacheHelperV2" + configId;
                                    carMarketCacheHelper.del(key2);
                                }
                                //refreshAllCache(); //原来用来刷新本地缓存的
                                carMarketCacheHelper.del(key);
                            } else {
                                result = result.stream().filter(x -> x.getCreateTime() >= (System.currentTimeMillis() - timeMillis)).collect(Collectors.toList());
                                log.warn("AutoCloseActivityService.autoCloseActivityServiceV2", "not than three uid, clear out time limit time, key: " + key, getHandlerContext().getTagMap());
                                carMarketCacheHelper.setString(key, 60 * 60 * 5, JsonUtil.toString(result));

                            }
                            if (result.size() >= 50) {
                                // 超过 50 个没有被清空，肯定有问题
                                log.warn("AutoCloseActivityService.autoCloseActivityServiceV2", "auto close size more than 50", getHandlerContext().getTagMap());
                                sendEmail("autoCloseActivityServiceV2,auto close size more than 50 key：" + key, "<EMAIL>", "<EMAIL>");
                                carMarketCacheHelper.setString(key, 60 * 60 * 5, JsonUtil.toString(result));
                            }
                        } else {
                            log.warn("AutoCloseActivityService.autoCloseActivityServiceV2", "not arrive 10800000 ms", getHandlerContext().getTagMap());
                            carMarketCacheHelper.setString(key, 60 * 60 * 5, JsonUtil.toString(result));
                        }
                    }
                }
            } else {
                result.add(autoCloseActivityDTO);
                carMarketCacheHelper.setString(key, 60 * 60 * 5, JsonUtil.toString(result));
            }
        } catch (Exception e) {
            log.warn("AutoCloseActivityService.autoCloseActivityServiceV2", e, getHandlerContext().getTagMap());
        }
    }

    /**
     * 判断是否为不在活动时间范围内的 错误信息
     */
    private boolean notWithinActivityTimeErrorInfo(String errorInfo) {
        return notWithinActivityTimeErrorMsgEnumList.stream().map(ActivityValidationErrorMsgEnum::getMsg).anyMatch(msg -> msg.equalsIgnoreCase(errorInfo));
    }

    protected void sendCloseEmail(List<AutoCloseActivityDTO> result) {
        log.info("AutoCloseActivityService.sendCloseEmail", JsonUtil.toString(result), getHandlerContext().getTagMap());
        List<AutoCloseActivityEmailDTO> emailDTOList = qConfigHelper.getAutoCloseActivityEmailDTOList();
        if (CollectionUtils.isNotEmpty(emailDTOList)) {
            Map<String, List<AutoCloseActivityDTO>> map = result.stream().collect(Collectors.groupingBy(AutoCloseActivityDTO::getVendorId));
            map.forEach((k, v) -> {
                AutoCloseActivityEmailDTO emailDTO = emailDTOList.stream().filter(e -> k.equals(e.getVendorId())).findFirst().orElse(null);
                if (Objects.isNull(emailDTO)) {
                    emailDTO = emailDTOList.stream().filter(e -> "0".equals(e.getVendorId())).findFirst().orElse(null);
                }
                if (Objects.nonNull(emailDTO)) {
                    String content = v.stream()
                            .map(x -> {
                                StringBuilder builder = new StringBuilder();
                                builder.append("<table border='1'>");
                                builder.append("<tr><td>ConfigId:</td><td>" + x.getConfigId() + "</td>  </tr>");
                                builder.append("<tr><td>Uid:</td> <td>" + x.getUid() + "</td>  </tr>");
                                builder.append("<tr><td>City:</td><td>" + x.getCityId() + "[" + x.getCityName() + "]</td>  </tr>");
                                builder.append("<tr><td>Reason:</td><td>" + x.getReason() + "</td>  </tr>");
                                builder.append("<tr><td>VendorId:</td><td>" + x.getVendorId() + ";" + x.getVendorName() + "</td>  </tr>");
                                builder.append("<tr><td>RequestId:</td><td>" + x.getRequestId() + "</td>  </tr>");
                                builder.append("<tr><td>VehicleId:</td><td>" + x.getVehicleId() + ";" + x.getVehicleName() + "</td>  </tr>");
                                builder.append("<tr><td>Store:</td><td>" + x.getPickVendorStoreId() + ";" + x.getPickVendorStoreName() + "</td>  </tr>");
                                builder.append("</table>");
                                builder.append("</br><br/>");
                                return builder.toString();
                            })
                            .collect(Collectors.joining("</br><br/>"));
                    List<String> tripEmailList = emailDTO.getTripEmailList();
                    List<String> tripCCEmailList = emailDTO.getTripCCEmailList();
                    tripEmailList.addAll(emailDTO.getVendorEmailList());
                    tripCCEmailList.addAll(emailDTO.getVendorCCEmailList());
                    String receivers = tripEmailList.stream().collect(Collectors.joining(";"));
                    String ccReceivers = tripCCEmailList.stream().collect(Collectors.joining(";"));
                    sendEmail(content, receivers, ccReceivers);
                } else {
                    log.warn("AutoCloseActivityService.autoCloseActivityServiceV2", "email config is not exit", getHandlerContext().getTagMap());
                }
            });
        }
    }

    protected void sendEmail(String content, String recipient, String cc) {
        EmailRequestDTO email = new EmailRequestDTO();
        email.setSubject("activity close");
        email.setCc(cc);
        email.setBodyContent(content);
        email.setRecipientName("activitySystem");
        email.setRecipient(recipient);
        EmailUtil.sendEmail(email);
    }

    //protected void refreshAllCache() {
    //    try {
    //        oldActivityCache.refreshCache();
    //        Cat.logEvent(this.getClass().getSimpleName(), "cache refresh success");
    //    } catch (Exception e) {
    //        log.error("refreshAllCache", e);
    //        Cat.logEvent(this.getClass().getSimpleName(), "cache refresh fail");
    //    }
    //}

}
