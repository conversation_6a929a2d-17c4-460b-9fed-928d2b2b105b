package com.ctrip.car.market.activity.svc.proxy;

import com.ctrip.car.market.activity.svc.util.JsonUtil;
import com.ctrip.car.sdcommon.utils.LogUtils;
import com.ctrip.di.cdp.CdpSoaServiceClient;
import com.ctrip.di.cdp.UserHitInRequestType;
import com.ctrip.di.cdp.UserHitInResponseType;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

public class CdpServiceProxy {

    private static final CdpSoaServiceClient cdpSoaServiceClient = CdpSoaServiceClient.getInstance();

    private static final LogUtils<CdpServiceProxy> log = new LogUtils<>();

    public static Set<String> userHitIn(String uid, List<String> secrets) {
        if (StringUtils.isBlank(uid)) {
            return Sets.newHashSet();
        }
        try {
            UserHitInRequestType requestType = new UserHitInRequestType();
            requestType.setUid(uid);
            requestType.setSecrets(secrets);
            UserHitInResponseType responseType = cdpSoaServiceClient.userHitIn(requestType);
            Map<String, String> tag = Maps.newHashMap();
            tag.put("uid", uid);
            log.info("userHitIn", uid + "|" + String.join(",", secrets) + "|" + JsonUtil.toString(responseType), tag);
            if (responseType.getSecretResultMap() == null || responseType.getSecretResultMap().isEmpty()) {
                return Sets.newHashSet();
            }
            Set<String> result = Sets.newHashSet();
            for (Map.Entry<String, Integer> kv : responseType.getSecretResultMap().entrySet()) {
                if (Objects.equals(kv.getValue(), 1)) {
                    result.add(kv.getKey());
                }
            }
            return result;
        } catch (Exception e) {
            log.error("userHitIn", e);
            return Sets.newHashSet();
        }
    }
}
