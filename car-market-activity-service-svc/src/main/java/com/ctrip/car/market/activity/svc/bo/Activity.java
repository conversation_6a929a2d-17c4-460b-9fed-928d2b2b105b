package com.ctrip.car.market.activity.svc.bo;

import com.ctrip.car.market.job.common.entity.ActInfoDO;
import com.ctrip.car.market.job.common.entity.ActTempInfoDO;

import java.math.BigDecimal;
import java.util.Set;

public class Activity {

    private ActInfoDO act;

    private ActTempInfoDO temp;

    private Set<Integer> city;

    private Set<Integer> returnCity;

    private Set<Long> product;

    private Set<Long> sku;

    private BigDecimal activityAmount;

    private BigDecimal realActivityAmount;

    private Integer vendorCheckCodeIndex;

    private Integer groupPriority;

    public Activity(ActInfoDO act, ActTempInfoDO temp, Integer groupPriority) {
        this.act = act;
        this.temp = temp;
        this.groupPriority = groupPriority;
    }

    public Activity(ActInfoDO act, ActTempInfoDO temp, Set<Integer> city, Set<Integer> returnCity, Set<Long> product, Set<Long> sku, Integer groupPriority) {
        this.act = act;
        this.temp = temp;
        this.city = city;
        this.returnCity = returnCity;
        this.product = product;
        this.sku = sku;
        this.groupPriority = groupPriority;
    }

    public ActInfoDO getAct() {
        return act;
    }

    public ActTempInfoDO getTemp() {
        return temp;
    }

    public Set<Integer> getCity() {
        return city;
    }

    public Set<Integer> getReturnCity() {
        return returnCity;
    }

    public Set<Long> getProduct() {
        return product;
    }

    public BigDecimal getActivityAmount() {
        return activityAmount;
    }

    public void setActivityAmount(BigDecimal activityAmount) {
        this.activityAmount = activityAmount;
    }

    public BigDecimal getRealActivityAmount() {
        return realActivityAmount;
    }

    public void setRealActivityAmount(BigDecimal realActivityAmount) {
        this.realActivityAmount = realActivityAmount;
    }

    public Integer getVendorCheckCodeIndex() {
        return vendorCheckCodeIndex;
    }

    public void setVendorCheckCodeIndex(Integer vendorCheckCodeIndex) {
        this.vendorCheckCodeIndex = vendorCheckCodeIndex;
    }

    public Set<Long> getSku() {
        return sku;
    }

    public Integer getGroupPriority() {
        return groupPriority;
    }
}
