package com.ctrip.car.market.activity.svc.service;

import com.ctrip.car.market.activity.contract.BaseRequest;
import com.ctrip.car.market.activity.contract.QueryFlightActivityRequestType;
import com.ctrip.car.market.activity.contract.QueryFlightActivityResponseType;
import com.ctrip.car.market.activity.contract.dto.FlightActivityItem;
import com.ctrip.car.market.activity.core.annotation.SOALog;
import com.ctrip.car.market.activity.core.service.BaseService;
import com.ctrip.car.market.activity.core.util.ResponseUtil;
import com.ctrip.car.market.activity.svc.bo.FlightActivityLabelItem;
import com.ctrip.car.market.activity.svc.cache.neww.ActivityInfoCache;
import com.ctrip.car.market.activity.svc.cache.neww.ActivityTempCache;
import com.ctrip.car.market.activity.svc.config.BaseConfig;
import com.ctrip.car.market.common.enums.DeductionType;
import com.ctrip.car.market.job.common.entity.ActInfoDO;
import com.ctrip.car.market.job.common.entity.ActTempInfoDO;
import com.ctrip.flight.intl.common.metric.Metrics;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class QueryFlightActivityService extends BaseService {

    @Resource
    private ActivityTempCache activityTempCache;

    @Resource
    private ActivityInfoCache activityInfoCache;

    @Resource
    private BaseConfig baseConfig;

    private final static String logTitle = "QueryFlightActivity";

    @SOALog(logTitle = logTitle)
    public QueryFlightActivityResponseType doBusiness(QueryFlightActivityRequestType request) {
        QueryFlightActivityResponseType response = new QueryFlightActivityResponseType();
        response.setActivityList(Lists.newArrayList());
        try {
            if (request.getCityId() == null || request.getBaseRequest() == null || request.getBaseRequest().getChannelId() == null) {
                response.setBaseResponse(ResponseUtil.fail("parameter cannot be empty"));
                return response;
            }
            List<ActTempInfoDO> tempList = activityTempCache.getAll();
            if (CollectionUtils.isNotEmpty(baseConfig.getFlightActivityLabel())) {
                Set<Integer> labelSet = baseConfig.getFlightActivityLabel().stream().map(FlightActivityLabelItem::getLabelId).collect(Collectors.toSet());
                tempList = tempList.stream().filter(l -> labelSet.contains(l.getLabelId())).collect(Collectors.toList());
            } else {
                response.setBaseResponse(ResponseUtil.fail("flight config is null"));
                return response;
            }
            if (request.getLabelId() != null) {
                tempList = tempList.stream().filter(l -> Objects.equals(l.getLabelId(), request.getLabelId().intValue())).collect(Collectors.toList());
            }
            //活动时间、城市、渠道过滤
            tempList = filter(request.getCityId(), request.getBaseRequest().getChannelId(), tempList);
            response.setActivityList(convert(tempList));
            response.setBaseResponse(ResponseUtil.success());
            return response;
        } catch (Exception e) {
            log.error(logTitle, e);
            response.setBaseResponse(ResponseUtil.systemError());
            return response;
        } finally {
            Integer channelId = Optional.ofNullable(request.getBaseRequest()).orElse(new BaseRequest()).getChannelId();
            Metrics.withTag("channelId", Optional.ofNullable(channelId).orElse(0).toString())
                    .withTag("cityId", Optional.ofNullable(request.getCityId()).orElse(0).toString())
                    .withTag("result", CollectionUtils.isNotEmpty(response.getActivityList()) ? "1" : "0")
                    .recordOne("queryFlightActivity");
        }
    }

    public List<FlightActivityItem> convert(List<ActTempInfoDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(l -> {
            FlightActivityItem item = new FlightActivityItem();
            item.setActivityDesc(l.getRemark());
            item.setActivityName(l.getName());
            item.setLabelId(l.getLabelId().longValue());
            item.setDeductionType(l.getContent().getDeductionStrategy().getDeductionType());
            item.setDeductionAmount(l.getContent().getDeductionStrategy().getDeductionAmount());
            //活动id
            List<Long> actIdList = activityInfoCache.queryActId(l.getTmpId());
            if (CollectionUtils.isNotEmpty(actIdList)) {
                List<ActInfoDO> actList = actIdList.stream().map(li -> activityInfoCache.queryById(li)).filter(Objects::nonNull).collect(Collectors.toList());
                item.setVendorList(actList.stream().map(ActInfoDO::getVendorId).distinct().collect(Collectors.toList()));
            } else {
                item.setVendorList(Lists.newArrayList());
            }
            return item;
        }).filter(l -> CollectionUtils.isNotEmpty(l.getVendorList())).collect(Collectors.toList());
    }

    public List<ActTempInfoDO> filter(Integer cityId, Integer channelId, List<ActTempInfoDO> list) {
        Calendar curr = Calendar.getInstance();
        return list.stream().filter(l -> {
            //活动时间
            if (l.getActivityStartTime().getTime() > curr.getTimeInMillis() || l.getActivityEndTime().getTime() < curr.getTimeInMillis()) {
                return false;
            }
            //城市
            if (l.getContent().getSupplierCondition() != null && CollectionUtils.isNotEmpty(l.getContent().getSupplierCondition().getCityIds())) {
                //排除
                if (Objects.equals(l.getContent().getSupplierCondition().getExcludeCity(), true) && l.getContent().getSupplierCondition().getCityIds().stream().anyMatch(li -> Objects.equals(li, cityId))) {
                    return false;
                }
                //包含
                if (!Objects.equals(l.getContent().getSupplierCondition().getExcludeCity(), true) && l.getContent().getSupplierCondition().getCityIds().stream().noneMatch(li -> Objects.equals(li, cityId))) {
                    return false;
                }
            }
            //渠道
            if (l.getContent().getUserCondition() != null && CollectionUtils.isNotEmpty(l.getContent().getUserCondition().getChannelIds())) {
                //排除
                if (Objects.equals(l.getContent().getUserCondition().getExcludeChannel(), true) && l.getContent().getUserCondition().getChannelIds().stream().anyMatch(li -> Objects.equals(li, channelId))) {
                    return false;
                }
                //包含
                if (!Objects.equals(l.getContent().getUserCondition().getExcludeChannel(), true) && l.getContent().getUserCondition().getChannelIds().stream().noneMatch(li -> Objects.equals(li, channelId))) {
                    return false;
                }
            }
            //不允许供应商自定义
            if (Objects.equals(l.getContent().getDeductionStrategy().getAllowCustomization(), true)) {
                return false;
            }
            //优惠方式只需要折扣
            if (!DeductionType.Discount.equal(l.getContent().getDeductionStrategy().getDeductionType())) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());
    }
}
