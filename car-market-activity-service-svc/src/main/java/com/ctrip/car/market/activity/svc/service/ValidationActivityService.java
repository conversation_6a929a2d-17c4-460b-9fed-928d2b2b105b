package com.ctrip.car.market.activity.svc.service;

import com.ctrip.car.market.activity.contract.ValidationActivityRequestType;
import com.ctrip.car.market.activity.contract.ValidationActivityResponseType;
import com.ctrip.car.market.activity.contract.dto.ActivityPriceItem;
import com.ctrip.car.market.activity.core.annotation.SOALog;
import com.ctrip.car.market.activity.core.service.BaseService;
import com.ctrip.car.market.activity.core.util.ResponseUtil;
import com.ctrip.car.market.activity.svc.bo.Activity;
import com.ctrip.car.market.activity.svc.bo.ActivityCondition;
import com.ctrip.car.market.activity.svc.bo.ActivityGroupConfig;
import com.ctrip.car.market.activity.svc.bo.HolidayDto;
import com.ctrip.car.market.activity.svc.cache.neww.*;
import com.ctrip.car.market.activity.svc.config.BaseConfig;
import com.ctrip.car.market.activity.svc.util.*;
import com.ctrip.car.market.job.common.entity.ActInfoDO;
import com.ctrip.car.market.job.common.entity.ActTempInfoDO;
import com.ctrip.flight.intl.common.metric.Metrics;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class ValidationActivityService extends BaseService {

    private static final ILog LOG = LogManager.getLogger(ValidationActivityService.class);

    @Resource
    private ActivityInfoCache activityInfoCache;

    @Resource
    private ActivityTempCache activityTempCache;

    @Resource
    private ActivityCityCache activityCityCache;

    @Resource
    private ActivityProductCache activityProductCache;

    @Resource
    private ActivityVendorSkuCache activityVendorSkuCache;

    @Resource
    private BaseConfig baseConfig;

    @Resource
    private FilterSecretUtils filterSecretUtils;

    @Resource
    private ActivityGroupConfig activityGroupConfig;

    private final static String logTitle = "ValidationActivity";

    @SOALog(logTitle = logTitle)
    public ValidationActivityResponseType doBusiness(ValidationActivityRequestType request) {
        ValidationActivityResponseType response = new ValidationActivityResponseType();
        if (!this.checkRequest(request, response)) {
            return response;
        }
        try {
            List<Long> activityIds = CommonUtil.getActivityIdList(request.getActivityId(), request.getActivityIdList());
            if (CollectionUtils.isEmpty(activityIds)) {
                response.setBaseResponse(ResponseUtil.fail("activity id is null"));
                return response;
            }
            List<Activity> actList = getAct(activityIds);
            if (CollectionUtils.isEmpty(actList)) {
                response.setBaseResponse(ResponseUtil.fail("activity non-existent"));
                log.warn(logTitle, "activity or temp non-existent," + request.getActivityId());
                return response;
            }
            HolidayDto holidayDto = CommonUtil.getHoliday(request.getProductCondition().getPickUpTime(), request.getProductCondition().getReturnTime(), baseConfig.getHolidayList());
            Calendar current = Calendar.getInstance();
            Integer tenancy = CommonUtil.getTenancy(request.getProductCondition().getPickUpTime(), request.getProductCondition().getReturnTime());
            boolean isScatteredHour = CommonUtil.isScatteredHour(request.getProductCondition().getPickUpTime(), request.getProductCondition().getReturnTime());
            Integer sourceFrom = CommonUtil.getSourceFrom(request.getBaseRequest().getSourceFrom(), baseConfig.getSourceFromMapping());
            //分销渠道活动配置
            Set<Long> channelActivityConfig = baseConfig.getChannelActivity(request.getBaseRequest().getChannelId());
            Map<Long, String> activityId2ErrorMsgMap = new HashMap<>();
            //提前预定期
            long advanceTime = CommonUtil.getAdvanceTime(request.getProductCondition().getPickUpTime());
            List<Activity> result = actList.stream().filter(ActivityConditionUtil.filterVendor(request.getProductCondition().getVendorId())
                    .and(ActivityConditionUtil.filterActivityTime(current, activityId2ErrorMsgMap))
                    .and(ActivityConditionUtil.filterExcludeData(current, activityId2ErrorMsgMap))
                    .and(ActivityConditionUtil.filterRepetitionPeriod(current, activityId2ErrorMsgMap))
                    .and(ActivityConditionUtil.filterHoliday(holidayDto))
                    .and(ActivityConditionUtil.filterCity(request.getProductCondition().getCityIds()))
                    .and(ActivityConditionUtil.filterTenancy(tenancy, isScatteredHour))
                    .and(ActivityConditionUtil.filterPickupTime(request.getProductCondition().getPickUpTime(), request.getProductCondition().getReturnTime()))
                    .and(ActivityConditionUtil.filterTakeTimeType(request.getProductCondition().getPickUpTime()))
                    .and(ActivityConditionUtil.filterSourceFrom(sourceFrom))
                    .and(ActivityConditionUtil.filterChannel(request.getBaseRequest().getChannelId(), request.getBaseRequest().getDistributionChannelId(), baseConfig.getDistributionChannel(), channelActivityConfig))
                    .and(ActivityConditionUtil.filterPayMode(request.getProductCondition().getPayMode()))
                    .and(ActivityConditionUtil.filterVehicleGroup(request.getProductCondition().getVehicleGroupId(), request.getProductCondition().getVirtualVehicleGroupId()))
                    .and(ActivityConditionUtil.filterStoreType(request.getProductCondition().getStoreType()))
                    .and(ActivityConditionUtil.filterProduct(request.getProductCondition().getStandardProductId()))
                    .and(ActivityConditionUtil.filterSku(request.getProductCondition().getSkuId()))
                    .and(ActivityConditionUtil.filterPrefer(request.getProductCondition().getPrefer()))
                    .and(ActivityConditionUtil.filterActivityGroup(request.getActivityIdList(), false, true))
                    .and(ActivityConditionUtil.filterSubsidyRange(request.getProductPrice().getAdjustAmount(), request.getProductPrice().getRentAmount()))
                    .and(ActivityConditionUtil.filterAdvanceTime(advanceTime))).collect(Collectors.toList());

            LOG.info(logTitle, "activityId2ErrorMsgMap::" + JsonUtil.toString(activityId2ErrorMsgMap));
            if (CollectionUtils.isEmpty(result) && MapUtils.isNotEmpty(activityId2ErrorMsgMap)) {
                String errorMsg = activityId2ErrorMsgMap.values().stream().findFirst().orElse("activity verification failed!");
                response.setBaseResponse(ResponseUtil.fail(errorMsg));
                return response;
            }

            //用户属性过滤
            result = filterSecretUtils.filterSecret(request.getBaseRequest().getUid(), result, request.getBaseRequest().getSourceFrom());
            if (CollectionUtils.isEmpty(result)) {
                response.setBaseResponse(ResponseUtil.fail("activity verification failed"));
                return response;
            }
            //计算优惠金额
            List<Activity> activityList = ActivityCalculationUtil.calculation(result, request.getProductPrice(), new ActivityCondition(tenancy, request.getProductCondition().getPickUpTime(), null, false));
            //任意活动校验不通过，则返回失败
            if (CollectionUtils.isEmpty(activityList) || activityList.size() != activityIds.size()) {
                response.setBaseResponse(ResponseUtil.fail("verification of preferential amount failed"));
                return response;
            }
            response.setActivityList(ConvertUtil.activityConvert(activityList));
            response.setActivityInfo(ConvertUtil.activityConvert(activityList.getFirst()));
            response.setShareDetail(ConvertUtil.shareConvert(activityList.getFirst()));
            response.setBaseResponse(ResponseUtil.success());
            activityAmountMonitor(request, activityList);
            return response;
        } catch (Exception e) {
            response.setBaseResponse(ResponseUtil.systemError());
            return response;
        } finally {
            getHandlerContext().writeTagMap("result", Objects.equals(response.getBaseResponse().getCode(), "000000") ? "1" : "0");
            getHandlerContext().writeTagMap("vendorId", Optional.ofNullable(request.getProductCondition().getVendorId()).orElse(0L).toString());
            Metrics.withTag("sourceFrom", StringUtils.isEmpty(request.getBaseRequest().getSourceFrom()) ? "0" : request.getBaseRequest().getSourceFrom())
                    .withTag("channel", Objects.isNull(request.getBaseRequest().getChannelId()) ? "0" : request.getBaseRequest().getChannelId().toString())
                    .withTag("vendor", request.getProductCondition().getVendorId().toString())
                    .withTag("result", Objects.equals(response.getBaseResponse().getCode(), "000000") ? "1" : "0")
                    .recordOne("validationActivity");
        }
    }

    private List<Activity> getAct(List<Long> activityIds) {
        List<Activity> result = Lists.newArrayList();
        for (Long activityId : activityIds) {
            ActInfoDO act = activityInfoCache.queryById(activityId);
            if (Objects.isNull(act)) {
                return Lists.newArrayList();
            }
            ActTempInfoDO temp = activityTempCache.queryByTmpId(act.getTempId());
            if (Objects.isNull(temp)) {
                return Lists.newArrayList();
            }
            List<Integer> city = activityCityCache.queryCityByActId(act.getId());
            List<Long> product = activityProductCache.queryByActId(act.getId());
            List<Long> sku = activityVendorSkuCache.queryByActId(act.getVendorId());
            Set<Integer> citySet = CollectionUtils.isEmpty(city) ? Sets.newHashSet() : Sets.newHashSet(city);
            Set<Integer> returnCitySet = Sets.newHashSet();
            Set<Long> productSet = CollectionUtils.isEmpty(product) ? Sets.newHashSet() : Sets.newHashSet(product);
            Set<Long> skuSet = CollectionUtils.isEmpty(sku) ? Sets.newHashSet() : Sets.newHashSet(sku);
            Integer groupPriority = activityGroupConfig.findPriority(temp.getGroupId());
            result.add(new Activity(act, temp, citySet, returnCitySet, productSet, skuSet, groupPriority));
        }
        return result;
    }

    private boolean checkRequest(ValidationActivityRequestType request, ValidationActivityResponseType response) {
        if (Objects.isNull(request.getBaseRequest())) {
            response.setBaseResponse(ResponseUtil.fail("base request is null"));
            return false;
        }
        if (Optional.ofNullable(request.getActivityId()).orElse(0L) <= 0 && CollectionUtils.isEmpty(request.getActivityIdList())) {
            response.setBaseResponse(ResponseUtil.fail("activity id is null"));
            return false;
        }
        String productConditionCheck = VerificationUtil.checkProductCondition(request.getProductCondition(), 1);
        if (StringUtils.isNotBlank(productConditionCheck)) {
            response.setBaseResponse(ResponseUtil.fail(productConditionCheck));
            return false;
        }
        String productPriceCheck = VerificationUtil.checkProductPrice(request.getProductPrice());
        if (StringUtils.isNotBlank(productPriceCheck)) {
            response.setBaseResponse(ResponseUtil.fail(productPriceCheck));
            return false;
        }
        return true;
    }

    public void activityAmountMonitor(ValidationActivityRequestType request, List<Activity> activityList) {
        try {
            for (Activity activity : activityList) {
                if (Optional.ofNullable(request.getProductPrice().getOrderAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) <= 0) {
                    return;
                }
                if (Optional.ofNullable(activity.getActivityAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) <= 0) {
                    return;
                }
                BigDecimal discount = activity.getActivityAmount().divide(request.getProductPrice().getOrderAmount(), 1, RoundingMode.DOWN).multiply(BigDecimal.TEN);
                boolean threshold = discount.compareTo(baseConfig.getActivityAmountLimit()) >= 0;
                if (threshold) {
                    Map<String, String> tag = Maps.newHashMap();
                    tag.put("templateId", activity.getTemp().getTmpId().toString());
                    tag.put("activityId", activity.getAct().getId().toString());
                    LOG.warn("activityAmountMonitor", "templateId:" + activity.getTemp().getTmpId() + "\n" + "activityId:" + activity.getAct().getId() + "\n" + discount, tag);
                }
                Metrics.withTag("templateId", threshold ? activity.getTemp().getTmpId().toString() : "0")
                        .withTag("discount", discount.toString())
                        .recordOne("activityAmountMonitor");
            }
        } catch (Exception e) {
            LOG.warn("activityAmountMonitor", e);
        }
    }
}
