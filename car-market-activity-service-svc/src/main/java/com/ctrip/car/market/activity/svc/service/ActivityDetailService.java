package com.ctrip.car.market.activity.svc.service;

import com.ctrip.car.market.activity.contract.ActivityDetailRequestType;
import com.ctrip.car.market.activity.contract.ActivityDetailResponseType;
import com.ctrip.car.market.activity.contract.dto.ActivityDto;
import com.ctrip.car.market.activity.core.annotation.SOALog;
import com.ctrip.car.market.activity.core.service.BaseService;
import com.ctrip.car.market.activity.core.util.ResponseUtil;
import com.ctrip.car.market.activity.repository.entity.ActCtripactinfo;
import com.ctrip.car.market.activity.repository.entity.ActCtriptempinfo;
import com.ctrip.car.market.activity.repository.entity.CpnLabel;
import com.ctrip.car.market.activity.repository.service.ActivityService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class ActivityDetailService extends BaseService {

    @Resource
    private ActivityService service;

    @Resource
    private QueryAllActivityListService queryAllActivityListService;

    @SOALog(logTitle = "ActivityDetail")
    public ActivityDetailResponseType doBusiness(ActivityDetailRequestType request) {
        ActivityDetailResponseType response = new ActivityDetailResponseType();
        if (Optional.ofNullable(request.getActivityId()).orElse(0L) <= 0 && CollectionUtils.isEmpty(request.getActivityIdList())) {
            response.setBaseResponse(ResponseUtil.fail("activity id is null"));
            return response;
        }
        //单个活动
        if (Optional.ofNullable(request.getActivityId()).orElse(0L) > 0) {
            response.setActivityDetail(singleActivity(request.getActivityId()));
        }
        //多个活动
        if (CollectionUtils.isNotEmpty(request.getActivityIdList())) {
            response.setActivityList(multipleActivity(request.getActivityIdList()));
        }
        response.setBaseResponse(ResponseUtil.success());
        return response;
    }

    public ActivityDto singleActivity(Long activityId) {
        try {
            ActCtripactinfo actInfo = service.queryByPk(activityId);
            ActCtriptempinfo tempInfo = Objects.nonNull(actInfo) ? service.queryByTempId(actInfo.getTempId()) : null;
            if (Objects.isNull(actInfo) || Objects.isNull(tempInfo)) {
                log.warn("singleActivity", "act is null");
                return null;
            }
            ActivityDto dto = queryAllActivityListService.convert(tempInfo, actInfo);
            CpnLabel label = service.queryLabel(tempInfo.getLabelId());
            if (Objects.nonNull(label)) {
                dto.setRemark(label.getDescription());
                dto.setLabelName(label.getName());
            }
            return dto;
        } catch (Exception e) {
            log.error("singleActivity", e);
            return null;
        }
    }

    public List<ActivityDto> multipleActivity(List<Long> activityIds) {
        return activityIds.stream().map(this::singleActivity).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
