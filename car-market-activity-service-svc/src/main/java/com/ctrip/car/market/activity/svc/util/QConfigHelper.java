package com.ctrip.car.market.activity.svc.util;

import com.ctrip.car.market.activity.svc.dto.old.AutoCloseActivityEmailDTO;
import org.springframework.stereotype.Service;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.List;
import java.util.Map;

@Service
public class QConfigHelper {

    @QConfig("config.properties")
    private Map<String, String> configMap;

    @QConfig("autoCloseActivityEmailConfig.json")
    private List<AutoCloseActivityEmailDTO> autoCloseActivityEmailDTOList;

    public Map<String, String> getConfigMap() {
        return configMap;
    }

    public List<AutoCloseActivityEmailDTO> getAutoCloseActivityEmailDTOList() {
        return autoCloseActivityEmailDTOList;
    }

}
