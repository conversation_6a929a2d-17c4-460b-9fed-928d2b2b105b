package com.ctrip.car.market.activity.svc.service;

import com.ctrip.car.market.activity.contract.QueryAllActivityListRequestType;
import com.ctrip.car.market.activity.contract.QueryAllActivityListResponseType;
import com.ctrip.car.market.activity.contract.dto.*;
import com.ctrip.car.market.activity.core.annotation.SOALog;
import com.ctrip.car.market.activity.core.service.BaseService;
import com.ctrip.car.market.activity.core.util.ResponseUtil;
import com.ctrip.car.market.activity.repository.entity.ActCtripactinfo;
import com.ctrip.car.market.activity.repository.entity.ActCtriptempinfo;
import com.ctrip.car.market.activity.repository.po.QueryActivityPara;
import com.ctrip.car.market.activity.repository.service.ActivityService;
import com.ctrip.car.market.activity.svc.bo.ActivityGroupConfig;
import com.ctrip.car.market.activity.svc.cache.neww.ActivityCityCache;
import com.ctrip.car.market.activity.svc.cache.neww.ActivityProductCache;
import com.ctrip.car.market.activity.svc.cache.neww.ActivityVendorSkuCache;
import com.ctrip.car.market.activity.svc.util.DateUtil;
import com.ctrip.car.market.activity.svc.util.JsonUtil;
import com.ctrip.car.market.common.entity.act.ActivityTempContent;
import com.ctrip.car.market.common.entity.act.CustomContent;
import com.ctrip.car.market.common.enums.ActivityStatus;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class QueryAllActivityListService extends BaseService {

    @Autowired
    private ActivityService service;

    @Autowired
    private ActivityCityCache activityCityCache;

    @Autowired
    private ActivityProductCache activityProductCache;

    @Autowired
    private ActivityVendorSkuCache activityVendorSkuCache;

    @Autowired
    private ActivityGroupConfig activityGroupConfig;

    @SOALog(logTitle = "QueryAllActivityList")
    public QueryAllActivityListResponseType doBusiness(QueryAllActivityListRequestType request) {
        QueryAllActivityListResponseType response = new QueryAllActivityListResponseType();
        response.setTotal(0);
        try {
            if (!this.checkRequest(request, response)) {
                return response;
            }
            response.setBaseResponse(ResponseUtil.success());
            QueryActivityPara queryPara = new QueryActivityPara();
            queryPara.setActivityIds(request.getActivityIdList());
            queryPara.setDataChangeLastTime(request.getDataChangeLastTime());
            List<ActCtripactinfo> activityList = service.queryActivityByPage(queryPara, request.getPageNo(), request.getPageSize());
            response.setTotal(service.queryActivityCount(queryPara));
            if (CollectionUtils.isNotEmpty(activityList)) {
                List<ActCtriptempinfo> tempList = service.queryActivityTemplate(activityList.stream().map(ActCtripactinfo::getTempId).distinct().collect(Collectors.toList()));
                Map<Long, ActCtriptempinfo> tempMap = tempList.stream().collect(Collectors.toMap(ActCtriptempinfo::getTmpId, l -> l));
                response.setTotal(service.queryActivityCount(queryPara));
                response.setActivityList(activityList.stream().map(l -> {
                    ActCtriptempinfo tempInfo = tempMap.get(l.getTempId());
                    if (Objects.isNull(tempInfo)) {
                        return null;
                    }
                    return this.convert(tempInfo, l);
                }).filter(Objects::nonNull).collect(Collectors.toList()));
            }
        } catch (Exception e) {
            response.setBaseResponse(ResponseUtil.systemError());
        }
        return response;
    }

    public ActivityDto convert(ActCtriptempinfo tempInfo, ActCtripactinfo actInfo) {
        ActivityDto item = new ActivityDto();
        item.setActivityId(actInfo.getId());
        item.setActivityType(Objects.equals(tempInfo.getTemplateType(), 2) ? 2 : 1);
        item.setGroupId(tempInfo.getGroupId());
        item.setGroupPriority(activityGroupConfig.findPriority(tempInfo.getGroupId()));
        item.setVendorId(actInfo.getVendorId());
        item.setVendorCouponCode(actInfo.getVendorCouponCode());
        item.setLabelId(Objects.nonNull(tempInfo.getLabelId()) ? tempInfo.getLabelId().longValue() : 0);
        item.setName(tempInfo.getName());
        item.setRemark(tempInfo.getRemark());
        item.setPriority(tempInfo.getPriority());
        item.setRepetitionPeriod(StringUtils.isNotBlank(tempInfo.getRepetitionPeriod()) ? Lists.newArrayList(tempInfo.getRepetitionPeriod().split("[,]")).stream().map(Integer::valueOf).collect(Collectors.toList()) : null);
        item.setActivityStartTime(DateUtil.toCalendar(actInfo.getStartTime()));
        item.setActivityEndTime(DateUtil.toCalendar(actInfo.getEndTime()));
        item.setStatus(Objects.equals(actInfo.getStatus(), ActivityStatus.Reviewed.getStatus()) ? 1 : 0);
        ActivityTempContent tempContent = JsonUtil.toObject(tempInfo.getTempContent(), ActivityTempContent.class);
        if (Objects.isNull(tempContent)) {
            return item;
        }
        item.setPayOffType(Optional.of(tempContent).map(ActivityTempContent::getPayOffType).orElse(2));
        if (Objects.nonNull(tempContent.getDeductionStrategy())) {
            item.setDeductionStrategy(new DeductionStrategy());
            item.getDeductionStrategy().setDeductionAmount(tempContent.getDeductionStrategy().getDeductionAmount());
            item.getDeductionStrategy().setDeductionType(tempContent.getDeductionStrategy().getDeductionType());
            item.getDeductionStrategy().setDeductionAmountLimit(tempContent.getDeductionStrategy().getDeductionAmountLimit());
            item.getDeductionStrategy().setIncludeFees(tempContent.getDeductionStrategy().getIncludeFees());
            item.getDeductionStrategy().setShareWithCoupon(tempContent.getDeductionStrategy().getShareWithCoupon());
            item.getDeductionStrategy().setLimitPrice(tempContent.getDeductionStrategy().getLimitPrice());
            item.getDeductionStrategy().setStartAmount(tempContent.getDeductionStrategy().getStartAmount());
            item.getDeductionStrategy().setSubsidyRangeMin(tempContent.getDeductionStrategy().getSubsidyRangeMin());
            item.getDeductionStrategy().setSubsidyRangeMax(tempContent.getDeductionStrategy().getSubsidyRangeMax());
            item.getDeductionStrategy().setComparisonTarget(tempContent.getDeductionStrategy().getComparisonTarget());
            item.getDeductionStrategy().setAdjustType(tempContent.getDeductionStrategy().getAdjustType());
            item.getDeductionStrategy().setDeductionStrategyList(CollectionUtils.isNotEmpty(tempContent.getDeductionStrategy().getDeductionStrategyList()) ? tempContent.getDeductionStrategy().getDeductionStrategyList().stream().map(l -> {
                DeductionStrategyItem listItem = new DeductionStrategyItem();
                listItem.setStartValue(l.getStartValue());
                listItem.setDeductionAmount(l.getDeductionAmount());
                return listItem;
            }).collect(Collectors.toList()) : Lists.newArrayList());

            if (StringUtils.isNotBlank(actInfo.getCustomContent())) {
                CustomContent customContent = JsonUtil.toObject(actInfo.getCustomContent(), CustomContent.class);
                if (Objects.nonNull(customContent) && Objects.nonNull(customContent.getDeductionStrategy()) && Objects.nonNull(customContent.getDeductionStrategy().getDeductionAmount())) {
                    item.getDeductionStrategy().setDeductionAmount(customContent.getDeductionStrategy().getDeductionAmount());
                }
                if (Objects.nonNull(customContent) && Objects.nonNull(customContent.getDeductionStrategy()) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(customContent.getDeductionStrategy().getDeductionStrategyList())) {
                    item.getDeductionStrategy().setDeductionStrategyList(customContent.getDeductionStrategy().getDeductionStrategyList().stream().map(l -> {
                        DeductionStrategyItem listItem = new DeductionStrategyItem();
                        listItem.setStartValue(l.getStartValue());
                        listItem.setDeductionAmount(l.getDeductionAmount());
                        return listItem;
                    }).collect(Collectors.toList()));
                }
            }
            //优惠计算base
            item.getDeductionStrategy().setDiscountBase(tempInfo.getDiscountBase());
        }
        if (Objects.nonNull(tempContent.getSupplierCondition())) {
            item.setSupplierCondition(new SupplierCondition());
            List<Integer> cityList = activityCityCache.queryCityByActId(actInfo.getId());
            List<Long> productList = activityProductCache.queryByActId(actInfo.getId());
            item.getSupplierCondition().setCityIds(CollectionUtils.isNotEmpty(cityList) ? cityList : null);
            item.getSupplierCondition().setExcludeCity(Objects.equals(actInfo.getExcludeCity(), true));
            item.getSupplierCondition().setExcludeReturnCity(Objects.equals(actInfo.getExcludeReturnCity(), true));
            item.getSupplierCondition().setTempCityIds(CollectionUtils.isNotEmpty(tempContent.getSupplierCondition().getCityIds()) ? tempContent.getSupplierCondition().getCityIds() : null);
            item.getSupplierCondition().setExcludeTempCity(Objects.equals(tempContent.getSupplierCondition().getExcludeCity(), true));
            item.getSupplierCondition().setStandardProductIds(CollectionUtils.isNotEmpty(productList) ? productList : null);
            item.getSupplierCondition().setExcludeStandardProduct(Objects.equals(actInfo.getExcludeProduct(), true));
            item.getSupplierCondition().setVehicleGroupIds(tempContent.getSupplierCondition().getVehicleGroupIds());
            item.getSupplierCondition().setExcludeVehicleGroup(Objects.equals(tempContent.getSupplierCondition().getExcludeVendorGroupId(), true));
            item.getSupplierCondition().setPayMode(CollectionUtils.isNotEmpty(tempContent.getSupplierCondition().getPayModes()) ? tempContent.getSupplierCondition().getPayModes().get(0) : 2);
            item.getSupplierCondition().setPickUpTimeRange(CollectionUtils.isNotEmpty(tempContent.getSupplierCondition().getPickUpTimeRange()) ?
                    tempContent.getSupplierCondition().getPickUpTimeRange().stream().map(l -> {
                        LimitDate range = new LimitDate();
                        range.setStart(l.getStart());
                        range.setEnd(l.getEnd());
                        return range;
                    }).collect(Collectors.toList()) : null);
            item.getSupplierCondition().setTenancyRange(CollectionUtils.isNotEmpty(tempContent.getSupplierCondition().getTenancyRange()) ?
                    tempContent.getSupplierCondition().getTenancyRange().stream().map(l -> {
                        LimitRange range = new LimitRange();
                        range.setFloor(l.getFloor());
                        range.setUplimit(l.getUpline());
                        return range;
                    }).collect(Collectors.toList()) : null);
            item.getSupplierCondition().setStoreType(CollectionUtils.isNotEmpty(tempContent.getSupplierCondition().getStoreType()) ? tempContent.getSupplierCondition().getStoreType() : Lists.newArrayList());
            //自动报名活动，返回sku
            if (Objects.equals(actInfo.getSignUpMode(), 1)) {
                List<Long> skuList = activityVendorSkuCache.queryByActId(actInfo.getVendorId());
                //自动报名活动必须有sku，异常情况默认0
                item.getSupplierCondition().setSkuIds(CollectionUtils.isNotEmpty(skuList) ? skuList : Lists.newArrayList(0L));
            }
            //供应商资源类型，兼容存量数据未配置时默认返回全部
            item.getSupplierCondition().setPrefer(CollectionUtils.isNotEmpty(tempContent.getSupplierCondition().getPrefer()) ? tempContent.getSupplierCondition().getPrefer() : Lists.newArrayList(0, 1));
            //标准产品优先使用供应商报名的，未选择时使用模版配置
            if (CollectionUtils.isEmpty(item.getSupplierCondition().getStandardProductIds()) && CollectionUtils.isNotEmpty(tempContent.getSupplierCondition().getStandardProductIds())) {
                item.getSupplierCondition().setStandardProductIds(tempContent.getSupplierCondition().getStandardProductIds());
                item.getSupplierCondition().setExcludeStandardProduct(Objects.equals(tempContent.getSupplierCondition().getExcludeStandardProduct(), true));
            }
            //活动模版支持的服务商
            item.getSupplierCondition().setVendorIds(CollectionUtils.isNotEmpty(tempContent.getSupplierCondition().getVendorIds()) ? tempContent.getSupplierCondition().getVendorIds().stream().map(Long::valueOf).collect(Collectors.toList()) : null);
            item.getSupplierCondition().setExcludeVendor(Objects.equals(tempContent.getSupplierCondition().getExcludeVendor(), true));
        }
        if (Objects.nonNull(tempContent.getShareDetail())) {
            item.setShareDetail(new ShareDetail());
            item.getShareDetail().setCostShare(tempContent.getShareDetail().getCostShare());
            item.getShareDetail().setShareAmount(Objects.equals(tempContent.getShareDetail().getCostType(), 1) ? tempContent.getShareDetail().getFixedAmount() : tempContent.getShareDetail().getPercentage());
            item.getShareDetail().setCostType(tempContent.getShareDetail().getCostType());
            item.getShareDetail().setShareType(tempContent.getShareDetail().getShareType());
        }
        if (Objects.nonNull(tempContent.getUserCondition())) {
            item.setUserCondition(new UserCondition());
            item.getUserCondition().setChannelIds(tempContent.getUserCondition().getChannelIds());
            item.getUserCondition().setExcludeChannel(tempContent.getUserCondition().getExcludeChannel());
            item.getUserCondition().setUserAttributes(tempContent.getUserCondition().getUserAttributes());
            item.getUserCondition().setSourceFrom(tempContent.getUserCondition().getPlatform());
        }

        return item;
    }

    private boolean checkRequest(QueryAllActivityListRequestType request, QueryAllActivityListResponseType response) {
        if (Objects.isNull(request.getPageNo()) || request.getPageNo() <= 0) {
            response.setBaseResponse(ResponseUtil.fail("page no error"));
            return false;
        }
        if (Objects.isNull(request.getPageSize()) || request.getPageSize() <= 0) {
            response.setBaseResponse(ResponseUtil.fail("page size error"));
            return false;
        }
        if (request.getPageSize() > 1000) {
            response.setBaseResponse(ResponseUtil.fail("page size not more than 1000"));
            return false;
        }
        return true;
    }
}
