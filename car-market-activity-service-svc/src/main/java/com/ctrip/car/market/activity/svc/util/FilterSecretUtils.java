package com.ctrip.car.market.activity.svc.util;

import com.ctrip.car.market.activity.svc.bo.Activity;
import com.ctrip.car.market.activity.svc.bo.UserGroupDTO;
import com.ctrip.car.market.activity.svc.config.BaseConfig;
import com.ctrip.car.market.activity.svc.proxy.CdpServiceProxy;
import com.ctrip.car.market.activity.svc.proxy.QueryQunarUserLabelProxy;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class FilterSecretUtils {
    @Autowired
    private BaseConfig baseConfig;

    @Autowired
    private QueryQunarUserLabelProxy queryQunarUserLabelProxy;

    public List<Activity> filterSecret(String uid, List<Activity> activityList, String sourceFrom) {
        if (CollectionUtils.isEmpty(activityList)) {
            return Lists.newArrayList();
        }
        List<String> secrets = activityList.stream().filter(l -> Objects.nonNull(l.getTemp().getContent()) && Objects.nonNull(l.getTemp().getContent().getUserCondition())
                        && CollectionUtils.isNotEmpty(l.getTemp().getContent().getUserCondition().getUserAttributes()))
                .flatMap(l -> l.getTemp().getContent().getUserCondition().getUserAttributes().stream()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(secrets)) {
            return activityList;
        }
        Set<String> qunarSets = baseConfig.getQunarUserTags().stream().map(UserGroupDTO::getSecret).collect(Collectors.toSet());
        if (baseConfig.getQunarSourceFromList().contains(sourceFrom)) {
            List<String> qunarList = secrets.stream().filter(qunarSets::contains).toList();
            Map<String, String> qunarUserTagMap = queryQunarUserLabelProxy.queryQunarUid(uid, qunarList, false);
            if (qunarUserTagMap != null && !qunarUserTagMap.isEmpty()) {
                // 遍历qunarUserTagMap，收集map中value = 1 的 key
                Set<String> secretSet = qunarUserTagMap.entrySet().stream()
                        .filter(entry -> Objects.equals(entry.getValue(), "1"))
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toSet());
                return activityList.stream().filter(ActivityConditionUtil.filterSecret(secretSet, true, qunarSets)).collect(Collectors.toList());
            } else {
                // 如果qunarUserTagMap为空，说明没有匹配到任何标签
                return activityList.stream().filter(ActivityConditionUtil.filterSecret(new HashSet<>(), true, qunarSets)).collect(Collectors.toList());
            }
        } else {
            List<String> list = secrets.stream().filter(x -> !qunarSets.contains(x)).toList();
            Set<String> secretSet = CdpServiceProxy.userHitIn(uid, list);
            return activityList.stream().filter(ActivityConditionUtil.filterSecret(secretSet, false, qunarSets)).collect(Collectors.toList());
        }
    }
}
