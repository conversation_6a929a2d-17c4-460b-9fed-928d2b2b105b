package com.ctrip.car.market.activity.svc.service.old;

import com.ctrip.car.market.activity.contract.*;
import com.ctrip.car.market.activity.core.annotation.SOALog;
import com.ctrip.car.market.activity.core.service.BaseService;
import com.ctrip.car.market.activity.core.util.ResponseUtil;
import com.ctrip.car.market.activity.repository.entity.CpnUseactivity;
import com.ctrip.car.market.activity.repository.entity.old.CpnActivitycalc;
import com.ctrip.car.market.activity.repository.entity.old.CpnActivityconfig;
import com.ctrip.car.market.activity.repository.entity.old.CalcActivityItem;
import com.ctrip.car.market.activity.repository.entity.old.content.UseActivityItem;
import com.ctrip.car.market.activity.repository.entity.old.enums.CalcActivityTypeEnum;
import com.ctrip.car.market.activity.repository.entity.old.enums.CarActivityStatusEnum;
import com.ctrip.car.market.activity.repository.entity.old.enums.UseActivityStatusEnum;
import com.ctrip.car.market.activity.repository.service.old.OldActivityService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

@Service
public class OldActivityUseCancelService extends BaseService {

    private final static Long newActivityId = 200000L;

    @Autowired
    protected OldActivityService oldActivityService;

    @SOALog(logTitle = "OldUseActivity")
    public UseActivityResponseType useActivity(UseActivityRequestType request) {
        UseActivityResponseType response = new UseActivityResponseType();
        if (!checkUseActivityRequest(request, response)) {
            return response;
        }
        Long configId = request.getActivityId();
        getHandlerContext().writeTagMap("configId", configId + "");
        try {
            // 验证活动是否存在
            CpnActivityconfig config = oldActivityService.queryActivityByConfigId(configId);
            if (Objects.isNull(config)) {
                response.setBaseResponse(ResponseUtil.fail("activity non-existent"));
                return response;
            }
            // 验证活动是否已过期
            if (!Objects.equals(config.getStatus(), CarActivityStatusEnum.GO_VALID.getCode())) {
                response.setBaseResponse(ResponseUtil.fail("activity expired"));
                return response;
            }
            // 验证活动是否已使用过
            List<CpnUseactivity> usedList = getUseActivityListUsed(configId, request.getOrderId(), request.getBaseRequest().getUid());
            if (CollectionUtils.isNotEmpty(usedList)) {
                response.setBaseResponse(ResponseUtil.fail("do not reuse"));
                return response;
            }
            // 验证库存
            CpnActivitycalc calc = queryCalcByAll(config.getConfigId());
            if (Objects.nonNull(calc)) {
                if (Objects.nonNull(config.getStockNum()) && config.getStockNum() > 0 &&
                        Objects.nonNull(calc.getTotalNum()) && config.getStockNum() <= calc.getTotalNum()) {
                    response.setBaseResponse(ResponseUtil.fail("stock is not enough"));
                    return response;
                }
                if (Objects.nonNull(config.getStockAmount()) && config.getStockAmount().compareTo(new BigDecimal(0)) > 0 &&
                        Objects.nonNull(calc.getTotalAmount()) && calc.getTotalAmount().add(request.getActivityAmount()).compareTo(config.getStockAmount()) > 0) {
                    response.setBaseResponse(ResponseUtil.fail("stock amount is not enough"));
                    return response;
                }
            }
            // 使用活动
            UseActivityItem useItem = new UseActivityItem();
            useItem.setConfigId(configId);
            useItem.setActivityAmount(request.getActivityAmount());
            useItem.setOrderOriginalAmount(request.getOrderOriginalAmount());
            useItem.setOrderID(request.getOrderId());
            useItem.setUid(request.getBaseRequest().getUid());
            //useItem.setIntlCode(requestType.getUserCondition().intlCode);
            //useItem.setTel(requestType.getUserCondition().get);
            //useItem.setIdCardNo(requestType.getUserCondition().idCardNo);
            //useItem.setSn(requestType.getUserCondition().sn);
            //useItem.setClientIP(requestType.getBaseRequest().getIp());
            useItem.setCommissionType(config.getCommissionType());
            useItem.setSettlementKey(config.getSettlementKey());
            useItem.setVendorActivityCode(config.getVendorCode());
            useItem.setDiscountType(config.getDiscountType());
            useItem.setSettlementType(config.getSettlementType());
            Boolean useResult = useActivity(useItem);
            //扣除库存
            if (Boolean.TRUE.equals(useResult)) {
                response.setBaseResponse(ResponseUtil.success());
            } else {
                response.setBaseResponse(ResponseUtil.fail("use activity fail"));
            }
        } catch (Exception e) {
            response.setBaseResponse(ResponseUtil.systemError());
        }
        return response;
    }

    protected boolean checkUseActivityRequest(UseActivityRequestType request, UseActivityResponseType response) {
        if (Objects.isNull(request.getBaseRequest())) {
            response.setBaseResponse(ResponseUtil.fail("baseRequest is null"));
            return false;
        }
        if (StringUtils.isBlank(request.getBaseRequest().getUid())) {
            response.setBaseResponse(ResponseUtil.fail("uid is null"));
            return false;
        }
        if (Objects.isNull(request.getActivityId()) || request.getActivityId() <= 0L || request.getActivityId() >= newActivityId) {
            response.setBaseResponse(ResponseUtil.fail("activityId is invalid"));
            return false;
        }
        if (Objects.isNull(request.getOrderId()) || request.getOrderId() <= 0L) {
            response.setBaseResponse(ResponseUtil.fail("orderId is invalid"));
            return false;
        }
        if (Objects.isNull(request.getActivityAmount()) || request.getActivityAmount().compareTo(BigDecimal.ZERO) <= 0) {
            response.setBaseResponse(ResponseUtil.fail("activityAmount error"));
            return false;
        }
        if (Objects.isNull(request.getOrderOriginalAmount()) || request.getOrderOriginalAmount().compareTo(request.getActivityAmount()) < 0) {
            response.setBaseResponse(ResponseUtil.fail("orderOriginalAmount error"));
            return false;
        }
        return true;
    }

    protected List<CpnUseactivity> getUseActivityListUsed(Long configId, Long orderId, String userId) throws SQLException {
        List<CpnUseactivity> list = new ArrayList<>();
        List<CpnUseactivity> all = oldActivityService.getUseActivityList(configId, orderId, userId);
        if (CollectionUtils.isNotEmpty(all)) {
            for (CpnUseactivity used : all) {
                if (Objects.equals(used.getStatus(), UseActivityStatusEnum.USED.getCode())) { //找出已使用的活动
                    list.add(used);
                }
            }
        }
        return list;
    }

    protected Boolean useActivity(UseActivityItem item) {
        // 插入一条活动使用记录
        Boolean insertResult = insertUseActivityFromActivityItem(item);
        if (Boolean.TRUE.equals(insertResult)) {
            // 查出历史的库存
            CpnActivitycalc calc = queryCalcByAll(item.getConfigId());
            if (Objects.nonNull(calc)) {
                calc.setTotalAmount(Objects.nonNull(calc.getTotalAmount()) ? calc.getTotalAmount().add(item.getActivityAmount()) : BigDecimal.ZERO);
                calc.setTotalNum(Objects.nonNull(calc.getTotalNum()) ? calc.getTotalNum() + 1L : 0L);
                // 修改库存
                String calcKey = generateCalcKey(item.getConfigId(), CalcActivityTypeEnum.TOTAL, null);
                useOrCancel(calcKey, calc.getTotalAmount(), calc.getTotalNum());
                return true;
            }
        } else {
            return false;
        }
        return false;
    }

    protected Boolean insertUseActivityFromActivityItem(UseActivityItem item) {
        if (Objects.nonNull(item)) {
            try {
                CpnUseactivity use = new CpnUseactivity();
                use.setStatus(UseActivityStatusEnum.USED.getCode()); //0已使用 1取消使用
                use.setOrderAmount(item.getOrderOriginalAmount());
                use.setActivityAmount(item.getActivityAmount());
                use.setOrderID(item.getOrderID());
                //use.setPayOffType();
                use.setUID(item.getUid());
                use.setConfigId(item.getConfigId());
                //use.setActivityId(item.getActivityAmount());
                use.setIntlCode(item.getIntlCode());
                use.setTel(item.getTel());
                use.setIDCardNo(item.getIdCardNo());
                use.setClientIP(item.getClientIP());
                use.setSN(item.getSn());
                Timestamp current = new Timestamp(System.currentTimeMillis());
                use.setDatachangeCreatetime(current);
                use.setDatachangeCreatetime(current);
                //为了计算使用
                String key = (Objects.equals(item.getSettlementType(), 0) ? "0" : (StringUtils.isBlank(item.getSettlementKey()) ? "1" : item.getSettlementKey()));
                use.setSettlementKey(key);
                if (Objects.equals(item.getDiscountType(), 0)) {
                    use.setVendorActivityCode(item.getVendorActivityCode());
                } else {
                    use.setVendorActivityCode("");
                }
                use.setCommissionType(item.getCommissionType());
                oldActivityService.insertUseActivity(use);
                return true;
            } catch (SQLException e) {
                log.error("OldActivityUseCancelService.insertUseActivityFromActivityItem", e, getHandlerContext().getTagMap());
                return false;
            }
        }
        return false;
    }

    protected CpnActivitycalc queryCalcByAll(Long configId) {
        CpnActivitycalc calc = null;
        String calcKey = generateCalcKey(configId, CalcActivityTypeEnum.TOTAL, null);
        try {
            calc = oldActivityService.queryCalcByCalcKey(calcKey); //查询库存
            if (Objects.isNull(calc)) {
                calc = autoAddCalc(oldActivityService.queryActivityNumAndAmount(configId), calcKey); //如果历史上没有库存信息，则新增一条
            }
        } catch (Exception e) {
            log.error("OldActivityUseCancelService.queryCalcByAll", e, getHandlerContext().getTagMap());
        }
        return calc;
    }

    protected CpnActivitycalc autoAddCalc(CalcActivityItem item, String key) {
        CpnActivitycalc calc = null;
        try {
            CpnActivitycalc calcInsert = oldActivityService.queryCalcByCalcKey(key);
            if (Objects.nonNull(calcInsert)) {
                return calcInsert;
            }
            Timestamp current = new Timestamp(System.currentTimeMillis());
            if (Objects.nonNull(item)) {
                if (Objects.isNull(item.getTotalAmount())) {
                    item.setTotalAmount(new BigDecimal(0));
                }
                calc = new CpnActivitycalc();
                calc.setTotalAmount(item.getTotalAmount());
                calc.setTotalNum(item.getTotalNum());
            } else {
                calc = new CpnActivitycalc();
                calc.setTotalAmount(new BigDecimal(0));
                calc.setTotalNum(0L);
            }
            calc.setDatachangeLasttime(current);
            calc.setDatachangeCreatetime(current);
            calc.setCalcKey(key);
            oldActivityService.insertActivityCalc(calc);
        } catch (Exception e) {
            log.error("OldActivityUseCancelService.autoAddCalc", e, getHandlerContext().getTagMap());
            return null;
        }
        return calc;
    }

    @SOALog(logTitle = "OldCancelActivity")
    public CancelActivityResponseType cancelActivity(CancelActivityRequestType request) {
        CancelActivityResponseType response = new CancelActivityResponseType();
        if (!checkCancelActivityRequest(request, response)) {
            return response;
        }
        Long configId = request.getActivityId();
        getHandlerContext().writeTagMap("configId", configId + "");
        try {
            // 验证是否存在使用记录
            List<CpnUseactivity> activities = oldActivityService.getUseActivityList(configId, request.getOrderId(), request.getBaseRequest().getUid());
            if (CollectionUtils.isEmpty(activities)) {
                response.setBaseResponse(ResponseUtil.fail("have no used record"));
                return response;
            }
            // 验证是否已经被取消了（存在使用记录，但没有状态为 USED 的记录，说明活动已经被取消了）
            List<CpnUseactivity> usedActivities = getUseActivityListUsed(configId, request.getOrderId(), request.getBaseRequest().getUid());
            if (CollectionUtils.isEmpty(usedActivities)) {
                response.setBaseResponse(ResponseUtil.fail("has already canceled, do not cancel repeatedly"));
                return response;
            }
            // 取消活动
            Boolean cancelResult = cancelActivity(usedActivities);
            if (Boolean.TRUE.equals(cancelResult)) {
                response.setBaseResponse(ResponseUtil.success());
            } else {
                response.setBaseResponse(ResponseUtil.fail("cancel fail"));
            }
        } catch (Exception e) {
            response.setBaseResponse(ResponseUtil.systemError());
        }
        return response;
    }

    protected boolean checkCancelActivityRequest(CancelActivityRequestType request, CancelActivityResponseType response) {
        if (Objects.isNull(request.getBaseRequest())) {
            response.setBaseResponse(ResponseUtil.fail("baseRequest is null"));
            return false;
        }
        if (StringUtils.isBlank(request.getBaseRequest().getUid())) {
            response.setBaseResponse(ResponseUtil.fail("uid is null"));
            return false;
        }
        if (Objects.isNull(request.getActivityId()) || request.getActivityId() <= 0 || request.getActivityId() >= newActivityId) {
            response.setBaseResponse(ResponseUtil.fail("activityId is invalid"));
            return false;
        }
        if (Objects.isNull(request.getOrderId()) || request.getOrderId() <= 0) {
            response.setBaseResponse(ResponseUtil.fail("orderId is invalid"));
            return false;
        }
        return true;
    }

    protected Boolean cancelActivity(List<CpnUseactivity> activityList) {
        if (CollectionUtils.isEmpty(activityList)) {
            return true;
        }
        for (CpnUseactivity useActivity : activityList) {
            try {
                // 修改表 Cpn_UseActivity
                useActivity.setStatus(UseActivityStatusEnum.CANCELED.getCode()); //0已使用 1取消使用
                Timestamp current = new Timestamp(System.currentTimeMillis());
                useActivity.setDatachangeLasttime(current);
                oldActivityService.updateUseActivity(useActivity);
                // 修改库存
                CpnActivitycalc calc = queryCalcByAll(useActivity.getConfigId());
                if (Objects.nonNull(calc)) {
                    if (Objects.nonNull(calc.getTotalAmount())) {
                        BigDecimal subRes = calc.getTotalAmount().subtract(Optional.ofNullable(useActivity.getActivityAmount()).orElse(BigDecimal.ZERO));
                        if (subRes.compareTo(BigDecimal.ZERO) >= 0) {
                            calc.setTotalAmount(subRes);
                        } else {
                            calc.setTotalAmount(BigDecimal.ZERO);
                        }
                    }
                    if (Objects.nonNull(calc.getTotalNum()) && (calc.getTotalNum() - 1L > 0L)) {
                        calc.setTotalNum(calc.getTotalNum() - 1L);
                    } else {
                        calc.setTotalNum(0L);
                    }
                    // 写库 CpnActivityCalc
                    String calcKey = generateCalcKey(useActivity.getConfigId(), CalcActivityTypeEnum.TOTAL, null);
                    useOrCancel(calcKey, calc.getTotalAmount(), calc.getTotalNum());
                    return true;
                }
            } catch (SQLException e) {
                log.error("OldActivityUseCancelService.cancelActivity", e, getHandlerContext().getTagMap());
            }
        }
        return false;
    }

    protected Boolean useOrCancel(String Key, BigDecimal totalAmount, Long toNum) {
        try {
            CpnActivitycalc calc = oldActivityService.queryCalcByCalcKey(Key);
            Timestamp current = new Timestamp(System.currentTimeMillis());
            if (Objects.isNull(calc)) {
                calc = new CpnActivitycalc();
                calc.setTotalAmount(totalAmount);
                calc.setTotalNum(toNum);
                calc.setCalcKey(Key);
                calc.setDatachangeCreatetime(current);
                calc.setDatachangeLasttime(current);
                oldActivityService.insertActivityCalc(calc);
            } else {
                calc.setDatachangeLasttime(current);
                calc.setTotalAmount(totalAmount);
                calc.setTotalNum(toNum);
                oldActivityService.updateActivityCalc(calc);
            }
            return true;
        } catch (SQLException e) {
            log.error("OldActivityUseCancelService.useOrCancel", e, getHandlerContext().getTagMap());
        }
        return false;
    }

    protected static String generateCalcKey(long configId, CalcActivityTypeEnum status, String value) {
        if (StringUtils.isBlank(value)) {
            return configId + "_" + status.getCode();
        } else {
            return configId + "_" + status.getCode() + "_" + value;
        }
    }

}
