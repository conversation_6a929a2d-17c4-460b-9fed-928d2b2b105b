package com.ctrip.car.market.activity.svc.util;

import com.ctrip.car.market.activity.contract.dto.ProductCondition;
import com.ctrip.car.market.activity.contract.dto.ProductPrice;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Objects;

public class VerificationUtil {

    /**
     * 校验取还车时间
     */
    public static boolean checkPickupReturnTime(Calendar pTime, Calendar rTime) {
        if (Objects.isNull(pTime) || Objects.isNull(rTime)) {
            return false;
        }
        if (pTime.getTimeInMillis() < System.currentTimeMillis()) {
            return false;
        }
        return pTime.compareTo(rTime) < 0;
    }

    /**
     * 校验产品条件
     *
     * @param type 0:列表页 1:填写页、验证活动
     */
    public static String checkProductCondition(ProductCondition condition, int type) {
        if (Objects.isNull(condition)) {
            return "product condition is null";
        }
        if (CollectionUtils.isEmpty(condition.getCityIds())) {
            return "city id is null";
        }
        if (!checkPickupReturnTime(condition.getPickUpTime(), condition.getReturnTime())) {
            return "pickup time or return time error";
        }
        if (type == 0) {
            return null;
        }
        if (Objects.isNull(condition.getVendorId()) || condition.getVendorId() <= 0) {
            return "vendor id error";
        }
        if (Objects.isNull(condition.getStoreId()) || condition.getStoreId() <= 0) {
            return "store id error";
        }
        if (Objects.isNull(condition.getStandardProductId()) || condition.getStandardProductId() <= 0) {
            return "standard product id error";
        }
        if (Objects.isNull(condition.getPayMode()) || condition.getPayMode() <= 0) {
            return "pay mode error";
        }
        if (Objects.isNull(condition.getVehicleGroupId())) {
            return "vehicle group id is null";
        }
        return null;
    }

    /**
     * 校验价格
     */
    public static String checkProductPrice(ProductPrice price) {
        if (Objects.isNull(price)) {
            return "product price is null";
        }
        if (Objects.isNull(price.getRentAmount()) || price.getRentAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return "rent amount error";
        }
        if (Objects.isNull(price.getFirstRentAmount())) {
            price.setFirstRentAmount(BigDecimal.ZERO);
        }
        if (Objects.isNull(price.getOrderAmount())) {
            price.setOrderAmount(BigDecimal.ZERO);
        }
        return null;
    }

}
