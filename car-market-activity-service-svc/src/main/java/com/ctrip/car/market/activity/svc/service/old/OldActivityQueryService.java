package com.ctrip.car.market.activity.svc.service.old;

import com.ctrip.car.market.activity.contract.ActivityDetailRequestType;
import com.ctrip.car.market.activity.contract.ActivityDetailResponseType;
import com.ctrip.car.market.activity.contract.dto.*;
import com.ctrip.car.market.activity.core.annotation.SOALog;
import com.ctrip.car.market.activity.core.service.BaseService;
import com.ctrip.car.market.activity.core.util.ResponseUtil;
import com.ctrip.car.market.activity.repository.entity.old.CpnActivityTemplate;
import com.ctrip.car.market.activity.repository.entity.old.content.CpnActivityTemplateRegisterForm;
import com.ctrip.car.market.activity.repository.entity.old.CpnActivityconfig;
import com.ctrip.car.market.activity.repository.service.old.OldActivityService;
import com.ctrip.car.market.common.util.JsonUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class OldActivityQueryService extends BaseService {

    private final static Long newActivityId = 200000L;

    @Resource
    private OldActivityService oldActivityService;

    protected String[] formats = new String[]{"yyyy-MM-dd", "yyyy/MM/dd HH:mm", "yyyy-MM-dd HH:mm:ss"};

    @SOALog(logTitle = "OldActivityDetail")
    public ActivityDetailResponseType getActivityDetail(ActivityDetailRequestType request) {
        ActivityDetailResponseType response = new ActivityDetailResponseType();
        if (!checkRequest(request, response)) {
            return response;
        }
        Long configId = request.getActivityId();
        getHandlerContext().writeTagMap("configId", configId + "");
        // 1. 查询活动 config
        CpnActivityconfig config = oldActivityService.queryActivityByConfigId(configId);
        if (Objects.isNull(config)) {
            response.setBaseResponse(ResponseUtil.fail("configId/activityId non-exist"));
            return response;
        }
        // 2. 查询 template
        Long templateId = config.getTemplateId();
        CpnActivityTemplate template = null;
        if (Objects.nonNull(templateId) && templateId > 0L) {
            getHandlerContext().writeTagMap("templateId", templateId + "");
            template = oldActivityService.queryActivityTemp(templateId);
        }
        // 3. 返回活动数据
        ActivityDto activityDto = new ActivityDto();
        response.setActivityDetail(activityDto);
        // 活动id
        activityDto.setActivityId(config.getConfigId());
        // 活动类型
        activityDto.setActivityType(1);
        // 活动名称
        activityDto.setName(config.getActivityName());
        // 标签id
        activityDto.setLabelId(Objects.nonNull(config.getLabelId()) ? config.getLabelId().longValue() : 0L);
        // 服务商id TODO 旧活动有多个 vendorId
        List<Long> vendorIdList = parseLong(config.getVendorIds());
        if (CollectionUtils.size(vendorIdList) > 0) {
            activityDto.setVendorId(parseLong(config.getVendorIds()).get(0));
        }
        // 供应商活动code
        activityDto.setVendorCouponCode(config.getVendorCode());
        // 活动时间
        List<Calendar> ActivityTimes = parseActivityTime(config.getActivityTime());
        if (CollectionUtils.isNotEmpty(ActivityTimes)) {
            activityDto.setActivityStartTime(ActivityTimes.get(0));
            if (CollectionUtils.size(ActivityTimes) >= 2) {
                activityDto.setActivityEndTime(ActivityTimes.get(1));
            }
        }
        // 重复周期
        //activityDto.setRepetitionPeriod();
        // 活动说明
        activityDto.setRemark(config.getActivityDes());
        // 优先级
        activityDto.setPriority(config.getSortIndex());
        // 是否有效
        activityDto.setStatus(Objects.equals(config.getStatus(), 1) ? 1 : 0); //是否有效 1有效 0无效
        // 优惠折扣规则
        DeductionStrategy deductionStrategy = new DeductionStrategy();
        deductionStrategy.setDeductionType(config.getPriceDiscountType()); //优惠折扣方式 0.固定金额 1.折扣 7.首日租金
        deductionStrategy.setDeductionAmount(config.getPriceDiscountValue()); //优惠金额
        //deductionStrategy.setStartAmount();
        //deductionStrategy.setDeductionAmountLimit();
        deductionStrategy.setIncludeFees(parseListString(config.getExCludeFees())); //可用费用项
        deductionStrategy.setShareWithCoupon(Objects.equals(config.getIsShareCoupon(), 1)); //是否可与优惠券同享
        deductionStrategy.setLimitPrice(Objects.equals(config.getIsMaxFirstRent(), 1)); //是否限价
        //deductionStrategy.setDeductionStrategyList(); //优惠阶梯
        activityDto.setDeductionStrategy(deductionStrategy);
        // 供应商参与条件
        SupplierCondition supplierCondition = new SupplierCondition();
        supplierCondition.setPayMode(config.getPayMode()); //支付方式 1.现付 2.预付
        supplierCondition.setCityIds(parseInteger(config.getCityIds()));
        supplierCondition.setExcludeCity(Objects.equals(config.getReverseCity(), 1));
        //supplierCondition.setReturnCityIds();
        //supplierCondition.setExcludeReturnCity();
        //supplierCondition.setTempCityIds();
        //supplierCondition.setExcludeTempCity();
        supplierCondition.setStoreIds(parseLong(config.getVendorStorecCodes()));
        supplierCondition.setStandardProductIds(parseLong(config.getVehicleIds()));
        supplierCondition.setExcludeStandardProduct(Objects.equals(config.getReverseVehicle(), 1));
        supplierCondition.setPickUpTimeRange(parsePickReturnTime(config.getPickTime())); // 取车时间范围
        supplierCondition.setTenancyRange(parseTenancyRange(config.getRentDay())); //租期范围
        supplierCondition.setVehicleGroupIds(parseInteger(config.getVehicleGroupIds())); //车型组
        supplierCondition.setExcludeVehicleGroup(Objects.equals(config.getReverseVehicleGroup(), 1));
        //supplierCondition.setStoreType(); //门店类型
        activityDto.setSupplierCondition(supplierCondition);
        // 用户参与条件
        UserCondition userCondition = new UserCondition();
        userCondition.setSourceFrom(Lists.newArrayList(0)); //平台 0携程 1去哪儿 2分销
        userCondition.setChannelIds(parseInteger(config.getDisChannelIds())); //渠道
        userCondition.setExcludeChannel(Objects.equals(config.getReverseDisChannel(), 1)); //是否排除渠道
        //userCondition.setUserAttributes(); //用户属性
        activityDto.setUserCondition(userCondition);
        // 活动类型 1返现 2立减
        activityDto.setPayOffType(config.getPayOffType());
        // 分摊方式
        if (Objects.nonNull(template)) {
            if (StringUtils.isNotBlank(template.getRegisterFormConfig())) {
                CpnActivityTemplateRegisterForm form = JsonUtil.meetCtripOldProjectEnum.readValue(template.getRegisterFormConfig(), CpnActivityTemplateRegisterForm.class);
                if (Objects.nonNull(form) && Objects.nonNull(form.getShareDetailDTO())) {
                    CpnActivityTemplateRegisterForm.ShareDetailDTO shareDetailDTO = form.getShareDetailDTO();
                    ShareDetail shareDetail = new ShareDetail();
                    shareDetail.setCostShare(shareDetailDTO.getCostShare()); //成本分摊方 0-供应商 1-携程
                    shareDetail.setCostType(shareDetailDTO.getCostType()); //成本类型 0-活动优惠金额
                    shareDetail.setShareType(shareDetailDTO.getShareType()); //分摊方式 0-百分比 1-金额
                    if (Objects.equals(shareDetailDTO.getShareType(), 0)) {
                        shareDetail.setShareAmount(shareDetailDTO.getPercentage()); //百分比
                    } else if (Objects.equals(shareDetailDTO.getShareType(), 1)) {
                        shareDetail.setShareAmount(shareDetailDTO.getFixedAmount()); //固定金额
                    }
                    shareDetail.setRealAmount(shareDetailDTO.getRealAmount()); //真实分摊金额
                    activityDto.setShareDetail(shareDetail);
                }
            }
        }
        response.setBaseResponse(ResponseUtil.success());
        return response;
    }

    protected boolean checkRequest(ActivityDetailRequestType request, ActivityDetailResponseType response) {
        if (Objects.isNull(request.getActivityId())) {
            response.setBaseResponse(ResponseUtil.fail("activityId is null"));
            return false;
        }
        if ((request.getActivityId() < 0L) || (request.getActivityId() >= newActivityId)) {
            response.setBaseResponse(ResponseUtil.fail("activityId is invalid"));
            return false;
        }
        return true;
    }

    protected List<Calendar> parseActivityTime(String timeString) {
        return Optional.ofNullable(timeString)
                .filter(StringUtils::isNotBlank)
                .map(times -> {
                    List<Calendar> calendars = new ArrayList<>();
                    try {
                        String[] ss = times.split(",");
                        SimpleDateFormat dateFormat = new SimpleDateFormat(formats[2]);
                        Calendar startCalendar = Calendar.getInstance();
                        startCalendar.setTime(dateFormat.parse(ss[0]));
                        calendars.add(startCalendar);
                        Calendar endCalendar = Calendar.getInstance();
                        endCalendar.setTime(dateFormat.parse(ss[1]));
                        calendars.add(endCalendar);
                    } catch (Exception e) {
                        log.error("OldActivityQueryService.parseActivityTime", e, getHandlerContext().getTagMap());
                    }
                    return calendars;
                })
                .orElse(new ArrayList<>());
    }

    protected List<LimitDate> parsePickReturnTime(String timeString) {
        return Optional.ofNullable(timeString)
                .filter(StringUtils::isNotBlank)
                .map(times -> {
                    List<LimitDate> list = new ArrayList<>();
                    try {
                        String[] ss = times.split(",");
                        if (ss.length >= 1) {
                            LimitDate limitDate = new LimitDate();
                            SimpleDateFormat dateFormat = new SimpleDateFormat(formats[0]);
                            Calendar startCalendar = Calendar.getInstance();
                            startCalendar.setTime(dateFormat.parse(ss[0]));
                            limitDate.setStart(startCalendar);
                            if (ss.length >= 2) {
                                Calendar endCalendar = Calendar.getInstance();
                                endCalendar.setTime(dateFormat.parse(ss[1]));
                                limitDate.setEnd(endCalendar);
                            }
                        }
                    } catch (Exception e) {
                        log.error("OldActivityQueryService.parsePickReturnTime", e, getHandlerContext().getTagMap());
                    }
                    return list;
                })
                .orElse(new ArrayList<>());
    }

    protected List<LimitRange> parseTenancyRange(String rentDay) {
        return Optional.ofNullable(rentDay)
                .filter(StringUtils::isNotBlank)
                .map(days -> {
                    List<LimitRange> list = new ArrayList<>();
                    List<Integer> dayList = parseInteger(days);
                    if (CollectionUtils.size(dayList) >= 1) {
                        LimitRange limitRange = new LimitRange();
                        limitRange.setFloor(dayList.get(0));
                        if (CollectionUtils.size(dayList) >= 2) {
                            limitRange.setUplimit(dayList.get(1));
                        }
                        list.add(limitRange);
                    }
                    return list;
                })
                .orElse(new ArrayList<>());
    }

    protected List<String> parseListString(String s) {
        if (StringUtils.isBlank(s)) {
            return new ArrayList<>();
        }
        return Arrays.stream(s.split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    protected List<Integer> parseInteger(String s) {
        if (StringUtils.isBlank(s)) {
            return new ArrayList<>();
        }
        return Arrays.stream(s.split(",")).filter(StringUtils::isNotBlank).filter(NumberUtils::isDigits).map(Integer::parseInt).collect(Collectors.toList());
    }

    protected List<Long> parseLong(String s) {
        if (StringUtils.isBlank(s)) {
            return new ArrayList<>();
        }
        return Arrays.stream(s.split(",")).filter(StringUtils::isNotBlank).filter(NumberUtils::isDigits).map(Long::parseLong).collect(Collectors.toList());
    }

}
