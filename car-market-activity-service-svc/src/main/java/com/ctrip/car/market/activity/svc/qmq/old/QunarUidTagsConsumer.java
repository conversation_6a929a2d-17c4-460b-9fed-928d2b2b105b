package com.ctrip.car.market.activity.svc.qmq.old;


import com.ctrip.car.market.activity.core.service.BaseService;
import com.ctrip.car.market.activity.svc.bo.UserGroupDTO;
import com.ctrip.car.market.activity.svc.config.BaseConfig;
import com.ctrip.car.market.activity.svc.proxy.QueryQunarUserLabelProxy;
import com.ctrip.car.market.activity.svc.util.JsonUtils;
import com.ctrip.car.sd.ctqrestfulshopping.dto.QueryProductsRequestType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.List;
import java.util.Set;

/**
 * 消费首页预加载信息，拿到Q端用户的uid，查询用户标签提前缓存
 */
@Component
public class QunarUidTagsConsumer extends BaseService {

    @Autowired
    private QueryQunarUserLabelProxy queryQunarUserLabelProxy;

    @Autowired
    private BaseConfig baseConfig;

    @QmqConsumer(prefix = "car.sd.notification.server.resource.track", consumerGroup = "100041788")
    public void listen(Message message) {
        String uid = message.getStringProperty("uid");
        String request = message.getStringProperty("request");
        if (StringUtils.isBlank(uid) || StringUtils.isBlank(request)) {
            return;
        }
        try {
            QueryProductsRequestType requestType = JsonUtils.toObject(request, QueryProductsRequestType.class);
            if (requestType == null || requestType.getBaseRequest() == null) {
                return;
            }
            String sourceFrom = requestType.getBaseRequest().getSourceFrom();
            Set<String> qunarSourceFromList = baseConfig.getQunarSourceFromList();
            if (qunarSourceFromList.isEmpty() || !qunarSourceFromList.contains(sourceFrom)) {
                return;
            }
            List<UserGroupDTO> qunarUserTags = baseConfig.getQunarUserTags();
            if (CollectionUtils.isNotEmpty(qunarUserTags)) {
                List<String> list = qunarUserTags.stream().map(UserGroupDTO::getSecret).toList();
                queryQunarUserLabelProxy.queryQunarUid(uid, list, true);
            }
        } catch (Exception e) {
            log.error("Error processing QunarUidTagsConsumer for uid: " + uid, e);
        }
    }
}
