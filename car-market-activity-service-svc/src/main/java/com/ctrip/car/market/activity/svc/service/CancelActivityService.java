package com.ctrip.car.market.activity.svc.service;

import com.ctrip.car.market.activity.contract.CancelActivityRequestType;
import com.ctrip.car.market.activity.contract.CancelActivityResponseType;
import com.ctrip.car.market.activity.core.annotation.SOALog;
import com.ctrip.car.market.activity.core.service.BaseService;
import com.ctrip.car.market.activity.core.util.ResponseUtil;
import com.ctrip.car.market.activity.repository.entity.CpnUseactivity;
import com.ctrip.car.market.activity.repository.service.ActivityService;
import com.ctrip.flight.intl.common.metric.Metrics;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Component
public class CancelActivityService extends BaseService {

    @Resource
    private ActivityService activityService;

    @SOALog(logTitle = "CancelActivity")
    public CancelActivityResponseType doBusiness(CancelActivityRequestType request) {
        CancelActivityResponseType response = new CancelActivityResponseType();
        if (!checkRequest(request, response)) {
            return response;
        }
        try {
            List<CpnUseactivity> useList = activityService.queryUseActivity(request.getOrderId(), request.getActivityId(), request.getBaseRequest().getUid());
            if (CollectionUtils.isEmpty(useList)) {
                response.setBaseResponse(ResponseUtil.fail("no use record"));
                return response;
            }
            useList.forEach(l -> l.setStatus(1));
            for (CpnUseactivity item : useList) {
                activityService.updateUseActivity(item);
            }
            response.setBaseResponse(ResponseUtil.success());
            return response;
        } catch (Exception e) {
            log.error("CancelActivity", e);
            response.setBaseResponse(ResponseUtil.systemError());
            return response;
        } finally {
            Metrics.withTag("result", Objects.equals(response.getBaseResponse().getCode(), "000000") ? "1" : "0").recordOne("cancelActivity");
        }
    }

    private boolean checkRequest(CancelActivityRequestType request, CancelActivityResponseType response) {
        if (Objects.isNull(request.getBaseRequest())) {
            response.setBaseResponse(ResponseUtil.fail("base request is null"));
            return false;
        }
        if (StringUtils.isBlank(request.getBaseRequest().getUid())) {
            response.setBaseResponse(ResponseUtil.fail("uid is null"));
            return false;
        }
        if (Objects.isNull(request.getActivityId()) || request.getActivityId() <= 0) {
            response.setBaseResponse(ResponseUtil.fail("activity id is null"));
            return false;
        }
        if (Objects.isNull(request.getOrderId()) || request.getOrderId() <= 0) {
            response.setBaseResponse(ResponseUtil.fail("order id is null"));
            return false;
        }
        return true;
    }
}
