package com.ctrip.car.market.activity.svc.util;

import com.ctrip.car.market.activity.svc.bo.HolidayDto;
import com.ctrip.car.market.activity.svc.bo.UserGroupDTO;
import com.ctrip.car.market.activity.svc.cache.neww.ActivityCityCache;
import com.ctrip.car.market.activity.svc.cache.neww.ActivityInfoCache;
import com.ctrip.car.market.common.entity.act.LimitDate;
import com.ctrip.car.market.common.entity.act.LimitRange;
import com.ctrip.car.market.common.enums.ActivityGroup;
import com.ctrip.car.market.common.enums.DeductionType;
import com.ctrip.car.market.job.common.entity.ActInfoDO;
import com.ctrip.car.market.job.common.entity.ActTempInfoDO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Component
public class ActivityListConditionUtil {

    private final static BigDecimal OneHundred = new BigDecimal("100");

    @Resource
    private ActivityCityCache activityCityCache;

    @Resource
    private ActivityInfoCache activityInfoCache;

    /**
     * 活动时间校验
     *
     * @return Predicate
     */
    public Predicate<ActTempInfoDO> filterActivityTime(Calendar current) {
        return x -> {
            if (Objects.isNull(x.getActivityStartTime()) || Objects.isNull(x.getActivityEndTime())) {
                return false;
            }
            return x.getActivityStartTime().getTime() <= current.getTimeInMillis() && x.getActivityEndTime().getTime() >= current.getTimeInMillis();
        };
    }

    /**
     * 过滤排除时间
     *
     * @return Predicate
     */
    public Predicate<ActTempInfoDO> filterExcludeData(Calendar current) {
        return x -> {
            if (StringUtils.isBlank(x.getExcludeDate()) || StringUtils.equalsIgnoreCase(x.getExcludeDate(), "null")) {
                return true;
            }
            List<LimitDate> dateList = JsonUtil.toList(x.getExcludeDate(), new TypeReference<>() {
            });
            if (CollectionUtils.isEmpty(dateList)) {
                return true;
            }
            return dateList.stream().filter(l -> Objects.nonNull(l.getStart()) && Objects.nonNull(l.getEnd())).noneMatch(l -> l.getStart().compareTo(current) <= 0 && l.getEnd().compareTo(current) >= 0);
        };
    }

    /**
     * 过滤重复周期
     *
     * @return Predicate
     */
    public Predicate<ActTempInfoDO> filterRepetitionPeriod(Calendar current) {
        return x -> {
            if (StringUtils.isBlank(x.getRepetitionPeriod())) {
                return true;
            }
            int weekIndex = current.get(Calendar.DAY_OF_WEEK);
            if (weekIndex == 1) {
                weekIndex = 7;
            } else {
                weekIndex = weekIndex - 1;
            }
            return x.getRepetitionPeriod().contains(String.valueOf(weekIndex));
        };
    }

    /**
     * 租期过滤
     *
     * @param tenancy 租期
     * @return Predicate
     */
    public Predicate<ActTempInfoDO> filterTenancy(Integer tenancy, boolean isScatteredHour) {
        return x -> {
            //满租期减、满租期折
            if (Objects.nonNull(x.getContent().getDeductionStrategy())
                    && (Objects.equals(x.getContent().getDeductionStrategy().getDeductionType(), DeductionType.Tenancy_Amount.getType())
                    || Objects.equals(x.getContent().getDeductionStrategy().getDeductionType(), DeductionType.Tenancy_Discount.getType()))) {
                if (CollectionUtils.isNotEmpty(x.getContent().getDeductionStrategy().getDeductionStrategyList())) {
                    if (x.getContent().getDeductionStrategy().getDeductionStrategyList().stream().noneMatch(l -> Optional.ofNullable(l.getStartValue()).orElse(0) <= tenancy)) {
                        return false;
                    }
                }
            }
            if (Objects.isNull(x.getContent().getSupplierCondition())) {
                return false;
            }
            if (CollectionUtils.isEmpty(x.getContent().getSupplierCondition().getTenancyRange())) {
                return true;
            }
            if (isScatteredHour) {
                return x.getContent().getSupplierCondition().getTenancyRange()
                        .stream().anyMatch(l -> (Objects.isNull(l.getFloor()) || l.getFloor() <= tenancy) && (Objects.isNull(l.getUpline()) || l.getUpline() >= tenancy + 1));
            }
            return x.getContent().getSupplierCondition().getTenancyRange()
                    .stream().anyMatch(l -> (Objects.isNull(l.getFloor()) || l.getFloor() <= tenancy) && (Objects.isNull(l.getUpline()) || l.getUpline() >= tenancy));
        };
    }

    /**
     * 取还车时间过滤
     *
     * @param pTime 取车时间
     * @param rTime 还车时间
     * @return Predicate
     */
    public Predicate<ActTempInfoDO> filterPickupTime(Calendar pTime, Calendar rTime) {
        return x -> {
            if (Objects.isNull(x.getContent().getSupplierCondition())) {
                return false;
            }
            if (CollectionUtils.isEmpty(x.getContent().getSupplierCondition().getPickUpTimeRange())) {
                return true;
            }
            List<LimitDate> list = x.getContent().getSupplierCondition().getPickUpTimeRange().stream()
                    .filter(l -> Objects.nonNull(l.getStart()) && Objects.nonNull(l.getEnd())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list)) {
                return true;
            }
            //取还车时间必须在设置区间内
            if (Objects.equals(x.getContent().getSupplierCondition().getPickUpTimeLimitType(), 0)) {
                return list.stream().anyMatch(l -> pTime.compareTo(l.getStart()) >= 0 && rTime.compareTo(l.getEnd()) <= 0);
            }
            //取还车时间与设置时间有交集即可
            return list.stream().anyMatch(l -> pTime.compareTo(l.getEnd()) <= 0 && rTime.compareTo(l.getStart()) >= 0);
        };
    }

    /**
     * 节假日过滤
     *
     * @param holidayDto 节假日信息
     * @return Predicate
     */
    public Predicate<ActInfoDO> filterHoliday(HolidayDto holidayDto, Map<Long, ActTempInfoDO> tempMap) {
        return x -> {
            ActTempInfoDO temp = tempMap.get(x.getTempId());
            if (Objects.isNull(temp) || Objects.isNull(temp.getContent().getSupplierCondition())) {
                return false;
            }
            if (Objects.isNull(holidayDto)) {
                return true;
            }
            //供应商自定义节假日规则
            if (Objects.equals(temp.getContent().getSupplierCondition().getAllowHolidayLimit(), true) && Objects.nonNull(x.getContent())
                    && Objects.nonNull(x.getContent().getSupplierCondition()) && Objects.nonNull(x.getContent().getSupplierCondition().getAllowHoliday())) {
                return Objects.equals(x.getContent().getSupplierCondition().getAllowHoliday(), true);
            }
            return Objects.equals(temp.getContent().getSupplierCondition().getAllowHoliday(), true);
        };
    }

    public Predicate<Long> filterHoliday(HolidayDto holidayDto, ActTempInfoDO temp) {
        return x -> {
            if (Objects.isNull(temp) || Objects.isNull(temp.getContent().getSupplierCondition())) {
                return false;
            }
            if (Objects.isNull(holidayDto)) {
                return true;
            }
            //供应商自定义节假日规则
            if (Objects.equals(temp.getContent().getSupplierCondition().getAllowHolidayLimit(), true)) {
                ActInfoDO actInfoDO = activityInfoCache.queryById(x);
                if (Objects.nonNull(actInfoDO) && Objects.nonNull(actInfoDO.getContent())
                        && Objects.nonNull(actInfoDO.getContent().getSupplierCondition()) && Objects.nonNull(actInfoDO.getContent().getSupplierCondition().getAllowHoliday())) {
                    return Objects.equals(actInfoDO.getContent().getSupplierCondition().getAllowHoliday(), true);
                }
            }
            return Objects.equals(temp.getContent().getSupplierCondition().getAllowHoliday(), true);
        };
    }

    public Predicate<ActTempInfoDO> filterTempHoliday(HolidayDto holidayDto) {
        return x -> {
            //未命中节假日
            if (Objects.isNull(holidayDto)) {
                return true;
            }
            //模版不允许自定义节假日规则，过滤符合条件的模版
            if (!Objects.equals(x.getContent().getSupplierCondition().getAllowHolidayLimit(), true)) {
                return Objects.equals(x.getContent().getSupplierCondition().getAllowHoliday(), true);
            }
            //模版允许自定义节假日规则，不过滤，以活动报名为准
            return true;
        };
    }

    /**
     * 平台过滤
     *
     * @param sourceFrom 平台
     * @return Predicate
     */
    public Predicate<ActTempInfoDO> filterSourceFrom(Integer sourceFrom) {
        return x -> {
            if (Objects.isNull(x.getContent().getUserCondition())) {
                return false;
            }
            if (CollectionUtils.isEmpty(x.getContent().getUserCondition().getPlatform())) {
                return true;
            }
            return x.getContent().getUserCondition().getPlatform().contains(Optional.ofNullable(sourceFrom).orElse(-1));
        };
    }

    /**
     * 渠道过滤
     *
     * @param channelId 渠道
     * @return Predicate
     */
    public Predicate<ActTempInfoDO> filterChannel(Integer channelId, Integer distributionChannelID, Set<Integer> distributionChannel, Set<Long> channelActivityConfig) {
        return x -> {
            if (Objects.isNull(x.getContent().getUserCondition())) {
                return false;
            }
            int channel = Optional.ofNullable(distributionChannelID).orElse(-1) > 0 ? distributionChannelID : Optional.ofNullable(channelId).orElse(-1);
            boolean tag;
            if (CollectionUtils.isEmpty(x.getContent().getUserCondition().getChannelIds())) {
                tag = true;
            } else {
                if (Objects.equals(x.getContent().getUserCondition().getExcludeChannel(), true)) {
                    tag = !x.getContent().getUserCondition().getChannelIds().contains(channel);
                } else {
                    tag = x.getContent().getUserCondition().getChannelIds().contains(channel);
                }
            }
            //活动模板渠道限制满足再验证分销渠道逻辑
            if (tag) {
                //先判断分销Group配置
                if (channelActivityConfig != null && channelActivityConfig.contains(x.getTmpId())) {
                    return true;
                }
                //分销渠道逻辑
                if (distributionChannel != null && distributionChannel.contains(channel)) {
                    //是否同步分销渠道
                    if (!Objects.equals(x.getContent().getUserCondition().getSyncDistributionChannel(), true)) {
                        //只返回供应商承担100%的活动
                        return Objects.nonNull(x.getContent().getShareDetail())
                                && Objects.equals(x.getContent().getShareDetail().getCostShare(), 0)
                                && Objects.equals(x.getContent().getShareDetail().getShareType(), 0)
                                && Objects.nonNull(x.getContent().getShareDetail().getPercentage())
                                && x.getContent().getShareDetail().getPercentage().compareTo(OneHundred) == 0;
                    }
                }
            }
            return tag;
        };
    }

//    /**
//     * 城市过滤
//     *
//     * @param cityIds 取车城市id
//     * @return Predicate
//     */
//    public Predicate<ActInfoDO> filterActCity(Set<Integer> cityIds) {
//        return x -> {
//            List<Integer> cityList = activityCityCache.queryCityByActId(x.getId());
//            if (CollectionUtils.isNotEmpty(cityList)) {
//                //排除
//                if (Objects.equals(x.getExcludeCity(), true)) {
//                    return !Sets.newHashSet(cityList).containsAll(cityIds);
//                } else {
//                    //包含
//                    return cityList.stream().anyMatch(cityIds::contains);
//                }
//            }
//            return true;
//        };
//    }

    public Predicate<Long> filterActCity(Set<Integer> cityIds) {
        return x -> {
            List<Integer> cityList = activityCityCache.queryCityByActId(x);
            if (CollectionUtils.isNotEmpty(cityList)) {
                //todo 排除

                //包含
                return cityList.stream().anyMatch(cityIds::contains);
            }
            return true;
        };
    }

    public Predicate<ActTempInfoDO> filterTempCity(Set<Integer> cityIds) {
        return x -> {
            if (Objects.nonNull(x.getContent().getSupplierCondition()) && CollectionUtils.isNotEmpty(x.getContent().getSupplierCondition().getCityIds())) {
                //排除城市
                if (Objects.equals(x.getContent().getSupplierCondition().getExcludeCity(), true)) {
                    return !Sets.newHashSet(x.getContent().getSupplierCondition().getCityIds()).containsAll(cityIds);
                } else {
                    //包含城市
                    return x.getContent().getSupplierCondition().getCityIds().stream().anyMatch(cityIds::contains);
                }
            }
            return true;
        };
    }

    /**
     * 用户属性过滤
     *
     * @param secretSet
     * @return
     */
    public Predicate<ActTempInfoDO> filterSecret(Set<String> secretSet, boolean isQ, Set<String> qunarUserTags) {
        return x -> {
            if (Objects.isNull(x.getContent().getUserCondition())) {
                return true;
            }
            if (CollectionUtils.isEmpty(x.getContent().getUserCondition().getUserAttributes())) {
                return true;
            }
            List<String> list;
            if (isQ) {
                list = x.getContent().getUserCondition().getUserAttributes().stream().filter(qunarUserTags::contains).toList();
            } else {
                list = x.getContent().getUserCondition().getUserAttributes().stream().filter(y -> !qunarUserTags.contains(y)).toList();
            }
            if (CollectionUtils.isEmpty(list)) {
                return true;
            }
            return list.stream().anyMatch(secretSet::contains);
        };
    }

    /**
     * 盲盒活动过滤
     *
     * @param isBlindBox
     * @return
     */
    public Predicate<ActTempInfoDO> filterBlindBox(boolean isBlindBox) {
        return x -> {
            if (isBlindBox) {
                return true;
            }
            return !Objects.equals(x.getTemplateType(), 2);
        };
    }

    /**
     * 过滤取车日期类型
     */
    public Predicate<ActTempInfoDO> filterTakeTimeType(Calendar pickUpTime) {
        return x -> {
            Integer takeTimeType = x.getContent().getSupplierCondition().getTakeTimeType();
            if (takeTimeType != null && takeTimeType == 2) {
                Calendar todayEnd = DateUtil.getDayEnd(DateUtil.toCalendar(System.currentTimeMillis()));
                return pickUpTime.before(todayEnd);
            }
            return true;
        };
    }

    /**
     * 提前预定期过滤
     */
    public Predicate<ActTempInfoDO> filterAdvanceTime(long advanceTime) {
        return x -> {
            //提前预定期满减、提前预定期满折
            if (Objects.nonNull(x.getContent().getDeductionStrategy())
                    && (Objects.equals(x.getContent().getDeductionStrategy().getDeductionType(), DeductionType.Advance_Amount.getType())
                    || Objects.equals(x.getContent().getDeductionStrategy().getDeductionType(), DeductionType.Advance_Discount.getType()))) {
                if (CollectionUtils.isNotEmpty(x.getContent().getDeductionStrategy().getDeductionStrategyList())) {
                    if (x.getContent().getDeductionStrategy().getDeductionStrategyList().stream().noneMatch(l -> Optional.ofNullable(l.getStartValue()).orElse(0) * 3600 <= advanceTime)) {
                        return false;
                    }
                }
            }
            LimitRange limitRange = x.getContent().getSupplierCondition().getAdvanceTime();
            if (limitRange == null) {
                return true;
            }
            return (Objects.isNull(limitRange.getFloor()) || limitRange.getFloor() * 3600 <= advanceTime) && (Objects.isNull(limitRange.getUpline()) || limitRange.getUpline() * 3600 >= advanceTime);
        };
    }

    /**
     * 调价活动过滤
     */
    public Predicate<ActTempInfoDO> filterActivityGroup(boolean followPrice) {
        return x -> {
            //是否需要调价活动
            //多活动共用调价标识
            if (followPrice) {
                return true;
            }
            //返回常规活动
            return Objects.equals(x.getGroupId(), ActivityGroup.Regular.getId());
        };
    }
}
