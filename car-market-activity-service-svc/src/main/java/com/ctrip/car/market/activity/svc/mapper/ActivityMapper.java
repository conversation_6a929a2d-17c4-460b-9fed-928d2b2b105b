package com.ctrip.car.market.activity.svc.mapper;

import com.ctrip.car.market.activity.repository.entity.ActCtripactinfo;
import com.ctrip.car.market.activity.repository.entity.ActCtriptempinfo;
import com.ctrip.car.market.job.common.entity.ActInfoDO;
import com.ctrip.car.market.job.common.entity.ActTempInfoDO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface ActivityMapper {

    ActTempInfoDO toTemp(ActCtriptempinfo obj);

    ActInfoDO toAct(ActCtripactinfo obj);
}
