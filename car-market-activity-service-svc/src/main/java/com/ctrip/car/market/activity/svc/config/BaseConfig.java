package com.ctrip.car.market.activity.svc.config;

import com.ctrip.car.market.activity.svc.bo.*;
import com.ctrip.car.market.activity.svc.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class BaseConfig {

    @QMapConfig("config.properties")
    public void onChange(Map<String, String> map) {
        this.sourceFromMapping = JsonUtil.toList(map.getOrDefault("sourceFromMapping", "[]"), new TypeReference<>() {
        });
        this.distributionChannel = Stream.of(map.getOrDefault("distributionChannel", "").split(",")).filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toSet());
        this.holidayList = JsonUtil.toList(map.getOrDefault("holiday", "[]"), new TypeReference<>() {
        });
        this.tenancyBuff = Long.parseLong(map.getOrDefault("tenancyBuff", "0"));
        this.activityAmountLimit = new BigDecimal(map.getOrDefault("activityAmountLimit", "3"));
        this.qunarSourceFromList = Stream.of(map.getOrDefault("qunarSourceFrom", "").split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        this.serviceUrl = map.getOrDefault("serviceUrl", "http://dev-1-car.beta.qunar.com/osdmobileservice/queryUserLabelCode");
        this.sCookieExpire = Long.parseLong(map.getOrDefault("sCookieExpire", "600"));
        this.qunarUserTagTimeout = Long.parseLong(map.getOrDefault("qunarUserTagTimeout", "400"));
        this.listFilterCity = StringUtils.equalsIgnoreCase(map.getOrDefault("listFilterCity", "0"), "1");
        this.listFilterVendor = StringUtils.equalsIgnoreCase(map.getOrDefault("listFilterVendor", "1"), "1");
    }

    @QConfig("channelGroup.json")
    private List<ChannelGroup> channelGroup;

    @QConfig("channelActivity.json")
    private List<ChannelActivity> channelActivity;

    private List<SourceFromMappingDto> sourceFromMapping;

    private Set<Integer> distributionChannel;

    private List<HolidayDto> holidayList;

    private long tenancyBuff;

    private Set<String> qunarSourceFromList;

    private BigDecimal activityAmountLimit;

    @QConfig("100011772#tmsSupportLanguages.json")
    private List<TmsLanguageDto> tmsSupportLanguages;

    @QConfig(("flightActivityLabelConfig.json"))
    private List<FlightActivityLabelItem> flightActivityLabel;

    @QConfig("qunarUserTags.json")
    private List<UserGroupDTO> qunarUserTags;

    private String serviceUrl;

    private long sCookieExpire;

    private long qunarUserTagTimeout;

    private boolean listFilterCity;

    private boolean listFilterVendor;

    public long getQunarUserTagTimeout() {
        return qunarUserTagTimeout;
    }

    public String getServiceUrl() {
        return serviceUrl;
    }

    public long getsCookieExpire() {
        return sCookieExpire;
    }

    public List<UserGroupDTO> getQunarUserTags() {
        return qunarUserTags;
    }

    public List<SourceFromMappingDto> getSourceFromMapping() {
        return sourceFromMapping;
    }

    public Set<Integer> getDistributionChannel() {
        return distributionChannel;
    }

    public List<HolidayDto> getHolidayList() {
        return holidayList;
    }

    public long getTenancyBuff() {
        return tenancyBuff;
    }

    public Set<Long> getChannelActivity(Integer channelId) {
        if (channelId == null || CollectionUtils.isEmpty(channelGroup) || CollectionUtils.isEmpty(channelActivity)) {
            return Sets.newHashSet();
        }
        Set<Integer> groupIds = channelGroup.stream().filter(l -> Objects.nonNull(l.getGroupId()) && Objects.nonNull(l.getChannelId()) && l.getChannelId().contains(channelId))
                .map(ChannelGroup::getGroupId).collect(Collectors.toSet());
        return channelActivity.stream().filter(l -> Objects.nonNull(l.getGroupId()) && Objects.nonNull(l.getActivityTempId()) && groupIds.contains(l.getGroupId()))
                .flatMap(l -> l.getActivityTempId().stream()).collect(Collectors.toSet());
    }

    public List<TmsLanguageDto> getTmsSupportLanguages() {
        return tmsSupportLanguages;
    }

    public List<FlightActivityLabelItem> getFlightActivityLabel() {
        return flightActivityLabel;
    }

    public Set<String> getQunarSourceFromList() {
        return qunarSourceFromList;
    }

    public BigDecimal getActivityAmountLimit() {
        return activityAmountLimit;
    }

    public boolean isListFilterCity() {
        return listFilterCity;
    }

    public boolean isListFilterVendor() {
        return listFilterVendor;
    }
}
