package com.ctrip.car.market.activity.svc.enums;

public enum ActivityValidationErrorMsgEnum {

    /**
     * 活动时间，校验不通过
     */
    FILTER_ACTIVITY_TIME("ActivityValidationNoPass::ActivityTime"),
    /**
     * 活动排除时间，校验不通过
     */
    FILTER_EXCLUDE_DATE("ActivityValidationNoPass::ExcludeDate"),
    /**
     * 活动重复周期，校验不通过
     */
    FILTER_REPETITION_PERIOD("ActivityValidationNoPass::RepetitionPeriod");


    private final String msg;

    ActivityValidationErrorMsgEnum(String msg) {
        this.msg = msg;
    }

    public String getMsg() {
        return msg;
    }

}
