package com.ctrip.car.market.activity.svc.cache;

import com.ctrip.car.market.activity.repository.entity.ActOsdlabel;
import com.ctrip.car.market.activity.repository.service.ActivityService;
import com.ctrip.car.sdcommon.utils.LogUtils;
import com.ctrip.framework.vi.cacheRefresh.CacheManager;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class OsdActivityLabelCache {

    private final LogUtils<OsdActivityLabelCache> log = new LogUtils<>();

    @Resource
    private ActivityService service;

    private OsdActivityLabelCacheCell cache;

    @PostConstruct
    private synchronized void init() {
        cache = new OsdActivityLabelCacheCell("OsdActivityLabelCache", "0 0/30 * * * ?", () -> {
            try {
                List<ActOsdlabel> list = service.queryAllOsdActivityLabel();
                return list.stream().collect(Collectors.groupingByConcurrent(ActOsdlabel::getActivityId, Collectors.mapping(ActOsdlabel::getLabelId, Collectors.toList())));
            } catch (Exception e) {
                log.error("OsdActivityLabelCache.init", e);
                throw new RuntimeException(e);
            }
        });
        CacheManager.add(cache);
        cache.getData();
    }

    public void refresh(Long activityId) {
        try {
            List<ActOsdlabel> label = service.queryOsdActivityLabel(activityId);
            this.cache.refresh(activityId, CollectionUtils.isNotEmpty(label) ? label.stream().map(ActOsdlabel::getLabelId).distinct().collect(Collectors.toList()) : null);
        } catch (Exception e) {
            log.error("refreshCache", e);
        }
    }

    public List<Long> getActivityLabel(Long activityId) {
        return this.cache.getData().get(activityId);
    }
}
