package com.ctrip.car.market.activity.svc.service;

import com.ctrip.car.market.activity.contract.QueryOsdActivityRequestType;
import com.ctrip.car.market.activity.contract.QueryOsdActivityResponseType;
import com.ctrip.car.market.activity.contract.dto.LabelItem;
import com.ctrip.car.market.activity.contract.dto.OsdActivityItem;
import com.ctrip.car.market.activity.contract.dto.TranslationItem;
import com.ctrip.car.market.activity.core.annotation.SOALog;
import com.ctrip.car.market.activity.core.constant.LabelConstant;
import com.ctrip.car.market.activity.core.service.BaseService;
import com.ctrip.car.market.activity.core.util.ResponseUtil;
import com.ctrip.car.market.activity.svc.bo.OsdActivity;
import com.ctrip.car.market.activity.svc.bo.TmsLanguageDto;
import com.ctrip.car.market.activity.svc.cache.LabelCache;
import com.ctrip.car.market.activity.svc.cache.OsdActivityCache;
import com.ctrip.car.market.activity.svc.cache.OsdActivityLabelCache;
import com.ctrip.car.market.activity.svc.config.BaseConfig;
import com.ctrip.car.market.activity.svc.proxy.CarCommodityServiceProxy;
import com.ctrip.car.market.activity.svc.proxy.TranslateServiceProxy;
import com.ctrip.car.market.activity.svc.util.CommonUtil;
import com.ctrip.car.market.activity.svc.util.OsdActivityConditionUtil;
import com.ctrip.car.market.job.common.entity.CpnLabelDO;
import com.ctrip.car.osd.translate.dto.TranslateResponseInfo;
import com.ctrip.car.osd.translate.dto.TranslateResultInfo;
import com.ctrip.flight.intl.common.metric.Metrics;
import com.ctrip.framework.foundation.Foundation;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class QueryOsdActivityService extends BaseService {

    @Resource
    private OsdActivityCache osdActivityCache;

    @Resource
    private OsdActivityLabelCache osdActivityLabelCache;

    @Resource
    private BaseConfig baseConfig;

    @Resource
    private LabelCache labelCache;

    private final static String logTitle = "queryOsdActivity";

    @SOALog(logTitle = logTitle)
    public QueryOsdActivityResponseType doBusiness(QueryOsdActivityRequestType request) {
        QueryOsdActivityResponseType response = new QueryOsdActivityResponseType();
        if (!this.checkRequest(request, response)) {
            return response;
        }
        try {
            response.setBaseResponse(ResponseUtil.success());
            List<OsdActivity> activityList = osdActivityCache.getVendorAct(request.getVendorId());
            if (CollectionUtils.isEmpty(activityList)) {
                return response;
            }
            String locale = StringUtils.isNotBlank(request.getBaseRequest().getLocale())
                    ? request.getBaseRequest().getLocale().replaceAll("-", "_").toUpperCase()
                    : request.getBaseRequest().getLocale();
            //根据城市id反查区域组
            Set<Long> areaIds = CarCommodityServiceProxy.queryArea(request.getCityId(), request.getBaseRequest().getRequestId());
            Calendar current = Calendar.getInstance();
            Integer tenancy = CommonUtil.getTenancy(request.getPickUpTime(), request.getReturnTime());
            boolean isScatteredHour = CommonUtil.isScatteredHour(request.getPickUpTime(), request.getReturnTime());
            List<OsdActivity> result = activityList.stream().filter(OsdActivityConditionUtil.filterActivityTime(current)
                    .and(OsdActivityConditionUtil.filterArea(areaIds))
                    .and(OsdActivityConditionUtil.filterPickupTime(request.getPickUpTime()))
                    .and(OsdActivityConditionUtil.filterReturnTime(request.getReturnTime()))
                    .and(OsdActivityConditionUtil.filterTenancy(tenancy, isScatteredHour))
                    .and(OsdActivityConditionUtil.filterStandardProduct(request.getStandardProductId()))).collect(Collectors.toList());
            response.setActivityList(result.stream().map(l -> this.convert(l, locale)).collect(Collectors.toList()));
            return response;
        } catch (Exception e) {
            response.setBaseResponse(ResponseUtil.systemError());
            return response;
        } finally {
            Metrics.withTag("city", request.getCityId().toString())
                    .withTag("sourceFrom", StringUtils.isEmpty(request.getBaseRequest().getSourceFrom()) ? "0" : request.getBaseRequest().getSourceFrom())
                    .withTag("channel", Objects.isNull(request.getBaseRequest().getChannelId()) ? "0" : request.getBaseRequest().getChannelId().toString())
                    .withTag("vendor", request.getVendorId().toString())
                    .withTag("result", CollectionUtils.isNotEmpty(response.getActivityList()) ? "1" : "0")
                    .recordOne("queryOsdActivity");
            Metrics.withTag("city", request.getCityId().toString())
                    .withTag("sourceFrom", StringUtils.isEmpty(request.getBaseRequest().getSourceFrom()) ? "0" : request.getBaseRequest().getSourceFrom())
                    .withTag("channel", Objects.isNull(request.getBaseRequest().getChannelId()) ? "0" : request.getBaseRequest().getChannelId().toString())
                    .withTag("vendor", request.getVendorId().toString())
                    .recordSize("queryOsdActivitySize", CollectionUtils.isNotEmpty(response.getActivityList()) ? response.getActivityList().size() : 0);
        }
    }

    private OsdActivityItem convert(OsdActivity activity, String locale) {
        OsdActivityItem item = new OsdActivityItem();
        item.setActivityId(activity.getAct().getId());
        item.setName(activity.getAct().getName());
        item.setPriceId(CommonUtil.getPriceId(activity.getCondition().getPriceIds(), activity.getCondition().getPriceId()));
        item.setPriceIds(CommonUtil.getPriceIds(activity.getCondition().getPriceIds(), activity.getCondition().getPriceId()));
        item.setPriority(Optional.ofNullable(activity.getCondition().getPriority()).orElse(0));
        List<Long> codes = osdActivityLabelCache.getActivityLabel(activity.getAct().getId());
        item.setLabels(getLabel(locale, codes));
        return item;
    }

    private List<LabelItem> getLabel(String locale, List<Long> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }
        try {
            List<CpnLabelDO> labelList = labelCache.getLabel(codes);
            if (CollectionUtils.isEmpty(labelList)) {
                return Lists.newArrayList();
            }
            return labelList.stream().map(l -> {
                LabelItem item = new LabelItem();
                item.setId(l.getCode());
                item.setName(l.getName());
                item.setDesc(l.getDescription());
                item.setTranslations(Lists.newArrayList());
                //空或者中文不查tms
                if (!StringUtils.isBlank(locale) && !StringUtils.equalsIgnoreCase(locale, "zh_cn")) {
                    TmsLanguageDto tmsLanguage = baseConfig.getTmsSupportLanguages().stream().filter(tms -> StringUtils.equalsIgnoreCase(tms.getBaselocale(), locale)).findFirst().orElse(null);
                    if (Objects.nonNull(tmsLanguage)) {
                        List<TranslateResponseInfo> translateList = TranslateServiceProxy.getTranslate(l.getCode(), tmsLanguage.getTmsLanguageValue());
                        TranslationItem tItem = getTranslate(translateList, l.getCode(), tmsLanguage);
                        if (Objects.nonNull(tItem)) {
                            item.setTranslations(Lists.newArrayList(tItem));
                        }
                    }
                    if (CollectionUtils.isEmpty(item.getTranslations())) {
                        //默认en_us打底
                        TmsLanguageDto languageDto = new TmsLanguageDto();
                        languageDto.setTmsLanguageValue("EN");
                        languageDto.setBaselocale("EN_US");
                        List<TranslateResponseInfo> translateList = TranslateServiceProxy.getTranslate(l.getCode(), languageDto.getTmsLanguageValue());
                        TranslationItem enItem = getTranslate(translateList, l.getCode(), languageDto);
                        if (Objects.nonNull(enItem)) {
                            item.setTranslations(Lists.newArrayList(enItem));
                        }
                    }
                }
                return item;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("getLabel", e);
            return Lists.newArrayList();
        }
    }

    private TranslationItem getTranslate(List<TranslateResponseInfo> translateList, Long code, TmsLanguageDto language) {
        if (CollectionUtils.isEmpty(translateList) || Objects.isNull(language)) {
            return null;
        }
        String nameKey = String.format(LabelConstant.nameFormat, code);
        String descKey = String.format(LabelConstant.descFormat, code);
        TranslateResponseInfo nameTranslate = translateList.stream().filter(li -> StringUtils.equalsIgnoreCase(li.getStandardKey(), nameKey)
                && CollectionUtils.isNotEmpty(li.getResults())
                && li.getResults().stream().anyMatch(lii -> StringUtils.equalsIgnoreCase(lii.getTargetLanguage(), language.getTmsLanguageValue()))).findFirst().orElse(null);
        TranslateResponseInfo descTranslate = translateList.stream().filter(li -> StringUtils.equalsIgnoreCase(li.getStandardKey(), descKey)
                && CollectionUtils.isNotEmpty(li.getResults())
                && li.getResults().stream().anyMatch(lii -> StringUtils.equalsIgnoreCase(lii.getTargetLanguage(), language.getTmsLanguageValue()))).findFirst().orElse(null);
        TranslateResultInfo nameResult = Objects.nonNull(nameTranslate) && CollectionUtils.isNotEmpty(nameTranslate.getResults()) ?
                nameTranslate.getResults().stream().filter(name -> StringUtils.equalsIgnoreCase(name.getTargetLanguage(), language.getTmsLanguageValue())).findFirst().orElse(null) : null;
        TranslateResultInfo descResult = Objects.nonNull(descTranslate) && CollectionUtils.isNotEmpty(descTranslate.getResults()) ?
                descTranslate.getResults().stream().filter(name -> StringUtils.equalsIgnoreCase(name.getTargetLanguage(), language.getTmsLanguageValue())).findFirst().orElse(null) : null;
        if (Objects.nonNull(nameResult) && Objects.nonNull(descResult)) {
            TranslationItem tItem = new TranslationItem();
            tItem.setLocale(language.getBaselocale());
            tItem.setName(nameResult.getResult());
            tItem.setDesc(descResult.getResult());
            return tItem;
        }
        return null;
    }

    public boolean checkRequest(QueryOsdActivityRequestType request, QueryOsdActivityResponseType response) {
        if (request.getBaseRequest() == null) {
            response.setBaseResponse(ResponseUtil.fail("base request is null"));
            return false;
        }
        if (request.getCityId() == null || request.getCityId() <= 0) {
            response.setBaseResponse(ResponseUtil.fail("city is null"));
            return false;
        }
        if (request.getPickUpTime() == null || request.getReturnTime() == null || request.getPickUpTime().after(request.getReturnTime())) {
            response.setBaseResponse(ResponseUtil.fail("pick time or return time error"));
            return false;
        }
        if (request.getVendorId() == null || request.getVendorId() <= 0) {
            response.setBaseResponse(ResponseUtil.fail("vendor is null"));
            return false;
        }
        if (request.getStandardProductId() == null || request.getStandardProductId() <= 0) {
            response.setBaseResponse(ResponseUtil.fail("standard product is null"));
            return false;
        }
        return true;
    }
}
