package com.ctrip.car.market.activity.svc.util;

import com.ctrip.car.market.activity.svc.bo.Activity;
import com.ctrip.car.market.activity.svc.bo.HolidayDto;
import com.ctrip.car.market.activity.svc.enums.ActivityValidationErrorMsgEnum;
import com.ctrip.car.market.common.calculate.ActCalculate;
import com.ctrip.car.market.common.entity.act.LimitDate;
import com.ctrip.car.market.common.entity.act.LimitRange;
import com.ctrip.car.market.common.enums.ActivityGroup;
import com.ctrip.car.market.common.enums.DeductionType;
import com.ctrip.car.market.common.enums.ProductType;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public class ActivityConditionUtil {

    private final static BigDecimal OneHundred = new BigDecimal("100");

    /**
     * 活动时间校验
     *
     * @return Predicate
     */
    public static Predicate<Activity> filterActivityTime(Calendar current) {
        return x -> {
            if (Objects.isNull(x.getAct().getStartTime()) || Objects.isNull(x.getAct().getEndTime())) {
                return false;
            }
            return x.getAct().getStartTime().getTime() <= current.getTimeInMillis() && x.getAct().getEndTime().getTime() >= current.getTimeInMillis();
        };
    }

    /**
     * 活动时间校验，并收集不通过的原因
     *
     * @return Predicate
     */
    public static Predicate<Activity> filterActivityTime(Calendar current, Map<Long, String> activityId2ErrorMsgMap) {
        return activity -> {
            boolean pass = filterActivityTime(current).test(activity);
            if (!pass && activityId2ErrorMsgMap != null) {
                activityId2ErrorMsgMap.put(activity.getAct().getId(), ActivityValidationErrorMsgEnum.FILTER_ACTIVITY_TIME.getMsg());
            }
            return pass;
        };
    }

    /**
     * 过滤排除时间
     *
     * @return Predicate
     */
    public static Predicate<Activity> filterExcludeData(Calendar current) {
        return x -> {
            if (StringUtils.isBlank(x.getTemp().getExcludeDate()) || StringUtils.equalsIgnoreCase(x.getTemp().getExcludeDate(), "null")) {
                return true;
            }
            List<LimitDate> dateList = JsonUtil.toList(x.getTemp().getExcludeDate(), new TypeReference<List<LimitDate>>() {
            });
            if (CollectionUtils.isEmpty(dateList)) {
                return true;
            }
            return dateList.stream().filter(l -> Objects.nonNull(l.getStart()) && Objects.nonNull(l.getEnd())).noneMatch(l -> l.getStart().compareTo(current) <= 0 && l.getEnd().compareTo(current) >= 0);
        };
    }

    /**
     * 排除时间检验，并收集不通过的原因
     *
     * @return Predicate
     */
    public static Predicate<Activity> filterExcludeData(Calendar current, Map<Long, String> activityId2ErrorMsgMap) {
        return activity -> {
            boolean pass = filterExcludeData(current).test(activity);
            if (!pass && activityId2ErrorMsgMap != null) {
                activityId2ErrorMsgMap.put(activity.getAct().getId(), ActivityValidationErrorMsgEnum.FILTER_EXCLUDE_DATE.getMsg());
            }
            return pass;
        };
    }

    /**
     * 过滤重复周期
     *
     * @return Predicate
     */
    public static Predicate<Activity> filterRepetitionPeriod(Calendar current) {
        return x -> {
            if (StringUtils.isBlank(x.getTemp().getRepetitionPeriod())) {
                return true;
            }
            int weekIndex = current.get(Calendar.DAY_OF_WEEK);
            if (weekIndex == 1) {
                weekIndex = 7;
            } else {
                weekIndex = weekIndex - 1;
            }
            return x.getTemp().getRepetitionPeriod().contains(String.valueOf(weekIndex));
        };
    }

    /**
     * 重复周期校验，并收集不通过的原因
     *
     * @return Predicate
     */
    public static Predicate<Activity> filterRepetitionPeriod(Calendar current, Map<Long, String> activityId2ErrorMsgMap) {
        return activity -> {
            boolean pass = filterRepetitionPeriod(current).test(activity);
            if (!pass && activityId2ErrorMsgMap != null) {
                activityId2ErrorMsgMap.put(activity.getAct().getId(), ActivityValidationErrorMsgEnum.FILTER_REPETITION_PERIOD.getMsg());
            }
            return pass;
        };
    }

    /**
     * 租期过滤
     *
     * @param tenancy 租期
     * @return Predicate
     */
    public static Predicate<Activity> filterTenancy(Integer tenancy, boolean isScatteredHour) {
        return x -> {
            //满租期减、满租期折
            if (Objects.nonNull(x.getTemp().getContent().getDeductionStrategy())
                    && (Objects.equals(x.getTemp().getContent().getDeductionStrategy().getDeductionType(), DeductionType.Tenancy_Amount.getType())
                    || Objects.equals(x.getTemp().getContent().getDeductionStrategy().getDeductionType(), DeductionType.Tenancy_Discount.getType()))) {
                if (CollectionUtils.isNotEmpty(x.getTemp().getContent().getDeductionStrategy().getDeductionStrategyList())) {
                    if (x.getTemp().getContent().getDeductionStrategy().getDeductionStrategyList().stream().noneMatch(l -> Optional.ofNullable(l.getStartValue()).orElse(0) <= tenancy)) {
                        return false;
                    }
                }
            }
            if (Objects.isNull(x.getTemp().getContent().getSupplierCondition())) {
                return false;
            }
            if (CollectionUtils.isEmpty(x.getTemp().getContent().getSupplierCondition().getTenancyRange())) {
                return true;
            }
            if (isScatteredHour) {
                return x.getTemp().getContent().getSupplierCondition().getTenancyRange()
                        .stream().anyMatch(l -> (Objects.isNull(l.getFloor()) || l.getFloor() <= tenancy) && (Objects.isNull(l.getUpline()) || l.getUpline() >= tenancy + 1));
            }
            return x.getTemp().getContent().getSupplierCondition().getTenancyRange()
                    .stream().anyMatch(l -> (Objects.isNull(l.getFloor()) || l.getFloor() <= tenancy) && (Objects.isNull(l.getUpline()) || l.getUpline() >= tenancy));
        };
    }

    public static Predicate<Activity> filterMinTenancy(Integer tenancy) {
        return x -> {
            if (Objects.isNull(x.getTemp().getContent().getSupplierCondition())) {
                return false;
            }
            if (CollectionUtils.isEmpty(x.getTemp().getContent().getSupplierCondition().getTenancyRange())) {
                return true;
            }
            return x.getTemp().getContent().getSupplierCondition().getTenancyRange().stream().anyMatch(l -> Objects.isNull(l.getFloor()) || l.getFloor() <= tenancy);
        };
    }

    /**
     * 取还车时间过滤
     *
     * @param pTime 取车时间
     * @param rTime 还车时间
     * @return Predicate
     */
    public static Predicate<Activity> filterPickupTime(Calendar pTime, Calendar rTime) {
        return x -> {
            if (Objects.isNull(x.getTemp().getContent().getSupplierCondition())) {
                return false;
            }
            if (CollectionUtils.isEmpty(x.getTemp().getContent().getSupplierCondition().getPickUpTimeRange())) {
                return true;
            }
            List<LimitDate> list = x.getTemp().getContent().getSupplierCondition().getPickUpTimeRange().stream()
                    .filter(l -> Objects.nonNull(l.getStart()) && Objects.nonNull(l.getEnd())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list)) {
                return true;
            }
            //取还车时间必须在设置区间内
            if (Objects.equals(x.getTemp().getContent().getSupplierCondition().getPickUpTimeLimitType(), 0)) {
                return list.stream().anyMatch(l -> pTime.compareTo(l.getStart()) >= 0 && rTime.compareTo(l.getEnd()) <= 0);
            }
            //取还车时间与设置时间有交集即可
            //return list.stream().anyMatch(l -> (pTime.compareTo(l.getStart()) >= 0 && pTime.compareTo(l.getEnd()) <= 0) || rTime.compareTo(l.getStart()) >= 0 && rTime.compareTo(l.getEnd()) <= 0);
            return list.stream().anyMatch(l -> pTime.compareTo(l.getEnd()) <= 0 && rTime.compareTo(l.getStart()) >= 0);
        };
    }

    /**
     * 节假日过滤
     *
     * @param holidayDto 节假日信息
     * @return Predicate
     */
    public static Predicate<Activity> filterHoliday(HolidayDto holidayDto) {
        return x -> {
            if (Objects.isNull(x.getTemp().getContent().getSupplierCondition())) {
                return false;
            }
            if (Objects.isNull(holidayDto)) {
                return true;
            }
            //供应商自定义节假日规则
            if (Objects.equals(x.getTemp().getContent().getSupplierCondition().getAllowHolidayLimit(), true) && Objects.nonNull(x.getAct().getContent())
                    && Objects.nonNull(x.getAct().getContent().getSupplierCondition()) && Objects.nonNull(x.getAct().getContent().getSupplierCondition().getAllowHoliday())) {
                return Objects.equals(x.getAct().getContent().getSupplierCondition().getAllowHoliday(), true);
            }
            return Objects.equals(x.getTemp().getContent().getSupplierCondition().getAllowHoliday(), true);
        };
    }

    /**
     * 平台过滤
     *
     * @param sourceFrom 平台
     * @return Predicate
     */
    public static Predicate<Activity> filterSourceFrom(Integer sourceFrom) {
        return x -> {
            if (Objects.isNull(x.getTemp().getContent().getUserCondition())) {
                return false;
            }
            if (CollectionUtils.isEmpty(x.getTemp().getContent().getUserCondition().getPlatform())) {
                return true;
            }
            return x.getTemp().getContent().getUserCondition().getPlatform().contains(Optional.ofNullable(sourceFrom).orElse(-1));
        };
    }

    /**
     * 渠道过滤
     *
     * @param channelId 渠道
     * @return Predicate
     */
    public static Predicate<Activity> filterChannel(Integer channelId, Integer distributionChannelID, Set<Integer> distributionChannel, Set<Long> channelActivityConfig) {
        return x -> {
            if (Objects.isNull(x.getTemp().getContent().getUserCondition())) {
                return false;
            }
            int channel = Optional.ofNullable(distributionChannelID).orElse(-1) > 0 ? distributionChannelID : Optional.ofNullable(channelId).orElse(-1);
            boolean tag;
            if (CollectionUtils.isEmpty(x.getTemp().getContent().getUserCondition().getChannelIds())) {
                tag = true;
            } else {
                if (Objects.equals(x.getTemp().getContent().getUserCondition().getExcludeChannel(), true)) {
                    tag = !x.getTemp().getContent().getUserCondition().getChannelIds().contains(channel);
                } else {
                    tag = x.getTemp().getContent().getUserCondition().getChannelIds().contains(channel);
                }
            }
            //活动模板渠道限制满足再验证分销渠道逻辑
            if (tag) {
                //先判断分销Group配置
                if (channelActivityConfig != null && channelActivityConfig.contains(x.getAct().getTempId())) {
                    return true;
                }
                //分销渠道逻辑
                if (distributionChannel != null && distributionChannel.contains(channel)) {
                    //是否同步分销渠道
                    if (!Objects.equals(x.getTemp().getContent().getUserCondition().getSyncDistributionChannel(), true)) {
                        //只返回供应商承担100%的活动
                        return Objects.nonNull(x.getTemp().getContent().getShareDetail())
                                && Objects.equals(x.getTemp().getContent().getShareDetail().getCostShare(), 0)
                                && Objects.equals(x.getTemp().getContent().getShareDetail().getShareType(), 0)
                                && Objects.nonNull(x.getTemp().getContent().getShareDetail().getPercentage())
                                && x.getTemp().getContent().getShareDetail().getPercentage().compareTo(OneHundred) == 0;
                    }
                }
            }
            return tag;
        };
    }

    /**
     * 支付方式过滤
     *
     * @param payMode 支付方式
     * @return Predicate
     */
    public static Predicate<Activity> filterPayMode(Integer payMode) {
        return x -> {
            if (Objects.isNull(x.getTemp().getContent().getSupplierCondition()) || CollectionUtils.isEmpty(x.getTemp().getContent().getSupplierCondition().getPayModes())) {
                return false;
            }
            return x.getTemp().getContent().getSupplierCondition().getPayModes().contains(payMode);
        };
    }

    /**
     * 城市过滤
     *
     * @param cityIds 取车城市id
     * @return Predicate
     */
    public static Predicate<Activity> filterCity(List<Integer> cityIds) {
        return x -> {
            if (CollectionUtils.isNotEmpty(x.getCity())) {
                if (Objects.equals(x.getAct().getExcludeCity(), true) && x.getCity().containsAll(cityIds)) {
                    return false;
                }
                if (!Objects.equals(x.getAct().getExcludeCity(), true) && cityIds.stream().noneMatch(l -> x.getCity().contains(l))) {
                    return false;
                }
            }
            if (Objects.nonNull(x.getTemp().getContent().getSupplierCondition()) && CollectionUtils.isNotEmpty(x.getTemp().getContent().getSupplierCondition().getCityIds())) {
                //排除城市
                if (Objects.equals(x.getTemp().getContent().getSupplierCondition().getExcludeCity(), true)) {
                    return !new HashSet<>(x.getTemp().getContent().getSupplierCondition().getCityIds()).containsAll(cityIds);
                } else {
                    //包含城市
                    return x.getTemp().getContent().getSupplierCondition().getCityIds().stream().anyMatch(cityIds::contains);
                }
            }
            return true;
        };
    }

    /**
     * 城市过滤
     *
     * @param requestReturnCityIds 还车城市id
     * @return Predicate
     */
    public static Predicate<Activity> filterReturnCity(List<Integer> requestReturnCityIds) {
        return activity -> {
            if (CollectionUtils.isNotEmpty(activity.getReturnCity())) {
                if (CollectionUtils.isEmpty(requestReturnCityIds)) return false; //桐叶一元游临时逻辑：活动中设置了还车城市但请求中没有，验证不通过

                if (Objects.equals(activity.getAct().getExcludeReturnCity(), true) && activity.getReturnCity().containsAll(requestReturnCityIds)) {
                    return false;
                }
                if (!Objects.equals(activity.getAct().getExcludeReturnCity(), true) && requestReturnCityIds.stream().noneMatch(requestReturnCity -> activity.getReturnCity().contains(requestReturnCity))) {
                    return false;
                }
            }
            return true;
        };
    }

    /**
     * 过滤供应商
     *
     * @param vendorId 供应商id
     * @return Predicate
     */
    public static Predicate<Activity> filterVendor(Long vendorId) {
        return x -> {
            //平台补贴无需报名，判断模版中限制的服务商
            if (Objects.equals(x.getTemp().getGroupId(), ActivityGroup.Subsidy.getId())) {
                if (!Objects.equals(x.getAct().getVendorId(), 0L)) {
                    return false;
                }
                if (CollectionUtils.isEmpty(x.getTemp().getContent().getSupplierCondition().getVendorIds())) {
                    return true;
                }
                if (Objects.equals(x.getTemp().getContent().getSupplierCondition().getExcludeVendor(), true)) {
                    return x.getTemp().getContent().getSupplierCondition().getVendorIds().stream().noneMatch(l -> Objects.equals(l, vendorId.intValue()));
                }
                return x.getTemp().getContent().getSupplierCondition().getVendorIds().stream().anyMatch(l -> Objects.equals(l, vendorId.intValue()));
            }
            return Objects.equals(x.getAct().getVendorId(), vendorId);
        };
    }

    /**
     * 标准产品过滤
     *
     * @param productId 标准产品id
     * @return Predicate
     */
    public static Predicate<Activity> filterProduct(Long productId) {
        return x -> {
            //判断是否满足模版标准产品限制
            if (Objects.nonNull(x.getTemp()) && Objects.nonNull(x.getTemp().getContent()) && Objects.nonNull(x.getTemp().getContent().getSupplierCondition())
                    && CollectionUtils.isNotEmpty(x.getTemp().getContent().getSupplierCondition().getStandardProductIds())) {
                boolean exclude = Objects.equals(x.getTemp().getContent().getSupplierCondition().getExcludeStandardProduct(), true);
                if (exclude && x.getTemp().getContent().getSupplierCondition().getStandardProductIds().stream().anyMatch(l -> Objects.equals(l, productId))) {
                    return false;
                }
                if (!exclude && x.getTemp().getContent().getSupplierCondition().getStandardProductIds().stream().noneMatch(l -> Objects.equals(l, productId))) {
                    return false;
                }
            }
            //判断是否满足供应商报名标准产品限制
            if (CollectionUtils.isEmpty(x.getProduct())) {
                return true;
            }
            if (Objects.equals(x.getAct().getExcludeProduct(), true)) {
                return !x.getProduct().contains(productId);
            }
            return x.getProduct().contains(productId);
        };
    }

    /**
     * 车型组过滤
     *
     * @param vehicleGroupId        车型组id
     * @param virtualVehicleGroupId 虚拟车型组id
     * @return Predicate
     */
    public static Predicate<Activity> filterVehicleGroup(Long vehicleGroupId, Long virtualVehicleGroupId) {
        return x -> {
            if (Objects.isNull(x.getTemp().getContent().getSupplierCondition())) {
                return false;
            }
            if (Objects.isNull(x.getTemp().getContent().getSupplierCondition()) || CollectionUtils.isEmpty(x.getTemp().getContent().getSupplierCondition().getVehicleGroupIds())) {
                return true;
            }
            Integer vId = Objects.isNull(vehicleGroupId) ? 0 : vehicleGroupId.intValue();
            Integer vvId = Objects.isNull(virtualVehicleGroupId) ? 0 : virtualVehicleGroupId.intValue();
            if (Objects.equals(x.getTemp().getContent().getSupplierCondition().getExcludeVendorGroupId(), true)) {
                return !(x.getTemp().getContent().getSupplierCondition().getVehicleGroupIds().contains(vId) || x.getTemp().getContent().getSupplierCondition().getVehicleGroupIds().contains(vvId));
            }
            return x.getTemp().getContent().getSupplierCondition().getVehicleGroupIds().contains(vId) || x.getTemp().getContent().getSupplierCondition().getVehicleGroupIds().contains(vvId);
        };
    }

    /**
     * 用户属性过滤
     *
     * @param secretSet
     * @return
     */
    public static Predicate<Activity> filterSecret(Set<String> secretSet, boolean isQ, Set<String> qunarUserTags) {
        return x -> {
            if (Objects.isNull(x.getTemp().getContent().getUserCondition())) {
                return true;
            }
            if (CollectionUtils.isEmpty(x.getTemp().getContent().getUserCondition().getUserAttributes())) {
                return true;
            }
            List<String> list;
            if (isQ) {
                list = x.getTemp().getContent().getUserCondition().getUserAttributes().stream().filter(qunarUserTags::contains).toList();
            } else {
                list = x.getTemp().getContent().getUserCondition().getUserAttributes().stream().filter(y -> !qunarUserTags.contains(y)).toList();
            }
            if (CollectionUtils.isEmpty(list)) {
                return true;
            }
            return list.stream().anyMatch(secretSet::contains);
        };
    }

    /**
     * 活动类型过滤
     *
     * @param activityType
     * @return
     */
    public static Predicate<Activity> filterActivityType(Integer activityType) {
        return x -> {
            //默认返回非盲盒活动
            if (activityType == null || Objects.equals(activityType, 1)) {
                return !Objects.equals(x.getTemp().getTemplateType(), 2);
            }
            return Objects.equals(x.getTemp().getTemplateType(), activityType);
        };
    }

    /**
     * 门店类型过滤
     *
     * @param storeType
     * @return
     */
    public static Predicate<Activity> filterStoreType(Integer storeType) {
        return x -> {
            if (Objects.isNull(x.getTemp().getContent().getSupplierCondition())) {
                return false;
            }
            if (CollectionUtils.isEmpty(x.getTemp().getContent().getSupplierCondition().getStoreType())) {
                return true;
            }
            Integer type = Optional.ofNullable(storeType).orElse(0);
            return x.getTemp().getContent().getSupplierCondition().getStoreType().contains(type);
        };
    }

    /**
     * 盲盒活动过滤
     *
     * @param isBlindBox
     * @return
     */
    public static Predicate<Activity> filterBlindBox(boolean isBlindBox) {
        return x -> {
            if (isBlindBox) {
                return true;
            }
            return !Objects.equals(x.getTemp().getTemplateType(), 2);
        };
    }

    /**
     * 过滤取车日期类型
     */
    public static Predicate<Activity> filterTakeTimeType(Calendar pickUpTime) {
        return x -> {
            Integer takeTimeType = x.getTemp().getContent().getSupplierCondition().getTakeTimeType();
            if (takeTimeType != null && takeTimeType == 2) {
                Calendar todayEnd = DateUtil.getDayEnd(DateUtil.toCalendar(System.currentTimeMillis()));
                return pickUpTime.before(todayEnd);
            }
            return true;
        };
    }

    /**
     * 过滤适用产品类型
     */
    public static Predicate<Activity> filterProductType(Integer pType, boolean isRpCity) {
        return x -> {
            //rp3期灰度城市不校验产品类型
            if (isRpCity) {
                return true;
            }
            if (CollectionUtils.isEmpty(x.getTemp().getContent().getSupplierCondition().getpType())) {
                return ProductType.getDefault().contains(pType);
            }
            return x.getTemp().getContent().getSupplierCondition().getpType().contains(pType);
        };
    }

    /**
     * 提前预定期过滤
     */
    public static Predicate<Activity> filterAdvanceTime(long advanceTime) {
        return x -> {
            //提前预定期满减、提前预定期满折
            if (Objects.nonNull(x.getTemp().getContent().getDeductionStrategy())
                    && (Objects.equals(x.getTemp().getContent().getDeductionStrategy().getDeductionType(), DeductionType.Advance_Amount.getType())
                    || Objects.equals(x.getTemp().getContent().getDeductionStrategy().getDeductionType(), DeductionType.Advance_Discount.getType()))) {
                if (CollectionUtils.isNotEmpty(x.getTemp().getContent().getDeductionStrategy().getDeductionStrategyList())) {
                    if (x.getTemp().getContent().getDeductionStrategy().getDeductionStrategyList().stream().noneMatch(l -> Optional.ofNullable(l.getStartValue()).orElse(0) * 3600 <= advanceTime)) {
                        return false;
                    }
                }
            }
            LimitRange limitRange = x.getTemp().getContent().getSupplierCondition().getAdvanceTime();
            if (limitRange == null) {
                return true;
            }
            return (Objects.isNull(limitRange.getFloor()) || limitRange.getFloor() * 3600 <= advanceTime) && (Objects.isNull(limitRange.getUpline()) || limitRange.getUpline() * 3600 >= advanceTime);
        };
    }

    /**
     * sku过滤
     */
    public static Predicate<Activity> filterSku(Long skuId) {
        return x -> {
            //非自动报名活动，无序校验sku
            if (!Objects.equals(x.getAct().getSignUpMode(), 1)) {
                return true;
            }
            //自动报名活动，必须满足sku限制
            if (skuId == null) {
                return false;
            }
            return Optional.ofNullable(x.getSku()).orElse(Sets.newHashSet()).contains(skuId);
        };
    }

    /**
     * 供应商资源类型过滤
     */
    public static Predicate<Activity> filterPrefer(Integer prefer) {
        return x -> {
            //存量数据未配置时，默认全部可用
            if (CollectionUtils.isEmpty(x.getTemp().getContent().getSupplierCondition().getPrefer())) {
                return true;
            }
            return x.getTemp().getContent().getSupplierCondition().getPrefer().contains(Optional.ofNullable(prefer).orElse(0));
        };
    }

    /**
     * 活动分组过滤
     */
    public static Predicate<Activity> filterActivityGroup(List<Long> activityList, boolean isModifyOrder, boolean followPrice) {
        return x -> {
            //非调价版本只返回常规活动
            if (!followPrice) {
                return Objects.equals(x.getTemp().getGroupId(), ActivityGroup.Regular.getId());
            }
            //修改订单场景，判断活动是否支持修改订单
            if (isModifyOrder) {
                return Objects.equals(x.getTemp().getSupportModifyOrder(), 1);
            }
            if (Objects.nonNull(x.getTemp()) && Objects.equals(x.getTemp().getGroupId(), ActivityGroup.Adjust.getId())) {
                //调价活动，仅在需要时返回
                return Optional.ofNullable(activityList).orElse(Lists.newArrayList()).contains(x.getAct().getId());
            }
            return true;
        };
    }

    /**
     * 补贴范围
     */
    public static Predicate<Activity> filterSubsidyRange(BigDecimal adjustAmount, BigDecimal rentAmount) {
        return x -> {
            //调价活动校验金额是否在补贴范围
            if (Objects.equals(x.getTemp().getGroupId(), ActivityGroup.Adjust.getId())) {
                if (x.getTemp().getContent() == null || x.getTemp().getContent().getDeductionStrategy() == null) {
                    return false;
                }
                //没有调价金额、租车费
                if (adjustAmount == null || adjustAmount.compareTo(BigDecimal.ZERO) <= 0 || rentAmount == null || rentAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    return false;
                }
                //单均补贴率=调价金额 / 原始租车费
                BigDecimal subsidyRate = adjustAmount.divide(rentAmount, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                //下限
                BigDecimal min = x.getTemp().getContent().getDeductionStrategy().getSubsidyRangeMin();
                //上限
                BigDecimal max = x.getTemp().getContent().getDeductionStrategy().getSubsidyRangeMax();
                return ActCalculate.inRange(min, max, subsidyRate);
            }
            return true;
        };
    }
}
