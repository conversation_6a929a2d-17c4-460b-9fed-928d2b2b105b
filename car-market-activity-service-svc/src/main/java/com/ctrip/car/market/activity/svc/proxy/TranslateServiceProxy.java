package com.ctrip.car.market.activity.svc.proxy;

import com.ctrip.car.market.activity.core.constant.LabelConstant;
import com.ctrip.car.market.activity.svc.util.JsonUtil;
import com.ctrip.car.market.activity.svc.util.RedisUtil;
import com.ctrip.car.osd.translate.dto.*;
import com.ctrip.car.osd.translate.methodtype.CarTranslateServiceClient;
import com.ctrip.car.sdcommon.utils.LogUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

public class TranslateServiceProxy {

    private final static CarTranslateServiceClient translateService = CarTranslateServiceClient.getInstance();

    private static final LogUtils<TranslateServiceProxy> log = new LogUtils<>();

    private static final String keyFormat = "car.market.activity.label.tms.";

    //过期时间(秒)
    private static final long expiredTime = 60 * 30;

    public static List<TranslateResponseInfo> getTranslate(Long code, String language) {
        try {
            List<TranslateResponseInfo> cache = getCache(code, language);
            if (CollectionUtils.isNotEmpty(cache)) {
                return cache;
            }
            List<TranslateRequestInfo> translateList = Lists.newArrayList();
            TranslateRequestInfo nameTranslate = new TranslateRequestInfo();
            nameTranslate.setStandardKey(String.format(LabelConstant.nameFormat, code));
            nameTranslate.setTargetLanguage(language);
            translateList.add(nameTranslate);
            TranslateRequestInfo descTranslate = new TranslateRequestInfo();
            descTranslate.setStandardKey(String.format(LabelConstant.descFormat, code));
            descTranslate.setTargetLanguage(language);
            translateList.add(descTranslate);
            TranslateRequestType request = new TranslateRequestType();
            request.setBuCode("CAR");
            request.setTranslateType(TranslateType.tms);
            request.setParams(translateList);
            TranslateResponseType response = translateService.translate(request);
            List<TranslateResponseInfo> result = Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getResponseInfo())
                    ? response.getResponseInfo() : Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(result)) {
                setCache(code, result, language);
            }
            return result;
        } catch (Exception e) {
            log.error("getTranslate", e);
            return Lists.newArrayList();
        }
    }

    private static List<TranslateResponseInfo> getCache(Long code, String language) {
        try {
            String value = RedisUtil.get(keyFormat + code + "." + language);
            if (StringUtils.isEmpty(value)) {
                return null;
            }
            return JsonUtil.toList(value, new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("getCache", e);
            return null;
        }
    }

    private static void setCache(Long code, List<TranslateResponseInfo> data, String language) {
        try {
            RedisUtil.set(keyFormat + code + "." + language, JsonUtil.toString(data), expiredTime);
        } catch (Exception e) {
            log.error("setCache", e);
        }
    }
}
