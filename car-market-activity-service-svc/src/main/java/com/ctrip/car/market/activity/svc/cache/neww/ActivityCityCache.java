package com.ctrip.car.market.activity.svc.cache.neww;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheBean;
import com.alicp.jetcache.anno.IncUpdateConsumerByRedis;
import com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst;
import com.ctrip.car.market.job.common.consts.CacheName;
import com.ctrip.car.market.job.common.entity.ActCityInfoDO;
import com.ctrip.car.market.job.common.entity.ActReturnCityInfoDO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@CreateCacheBean
public class ActivityCityCache {

    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.IsdActivityCityCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.ACT_CITY_KEY)
    public Cache<Long, List<ActCityInfoDO>> cityCache;

    public List<Integer> queryCityByActId(Long actId) {
        List<ActCityInfoDO> list = cityCache.get(actId);
        return CollectionUtils.isNotEmpty(list)
                ? list.stream().map(ActCityInfoDO::getCityId).collect(Collectors.toList())
                : Lists.newArrayList();
    }

}
