package com.ctrip.car.market.activity.svc.cache;

import com.ctrip.arch.canal.DataChange;
import com.ctrip.car.market.activity.svc.util.JsonUtil;
import com.ctrip.car.sdcommon.utils.LogUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import javax.annotation.Resource;
import java.util.Objects;

@Component
public class CacheRefresh {

    private final LogUtils<CacheRefresh> log = new LogUtils<>();

    @Resource
    private OsdActivityCache osdActivityCache;

    @Resource
    private OsdActivityLabelCache osdActivityLabelCache;

    @QmqConsumer(prefix = "car.market.activity.data.change", consumerGroup = "100041788", isBroadcast = true)
    public void dataChange(Message message) {
        try {
            String data = message.getStringProperty("dataChange");
            if (StringUtils.isBlank(data)) {
                return;
            }
            DataChange dataChangeObject = JsonUtil.toObject(data, DataChange.class);
            if (Objects.isNull(dataChangeObject)) {
                return;
            }
            if (StringUtils.equalsIgnoreCase(dataChangeObject.getTableName(), "act_osdactinfo")) {
                osdActivityCache.refreshCache(Long.valueOf(dataChangeObject.getAfterColumnValue("id")));
            }
            if (StringUtils.equalsIgnoreCase(dataChangeObject.getTableName(), "act_osdlabel")) {
                osdActivityLabelCache.refresh(Long.valueOf(dataChangeObject.getAfterColumnValue("activityId")));
            }
        } catch (Exception e) {
            log.error("CacheRefresh.dataChange", e);
        }
    }

}
