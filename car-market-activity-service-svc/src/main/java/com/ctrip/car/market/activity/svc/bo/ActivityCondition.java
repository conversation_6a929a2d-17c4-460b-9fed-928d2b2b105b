package com.ctrip.car.market.activity.svc.bo;

import java.util.Calendar;

public class ActivityCondition {

    public ActivityCondition(Integer tenancy, Calendar pTime, Calendar bTime, boolean checkAdjustPrice) {
        this.tenancy = tenancy;
        this.pTime = pTime;
        this.bTime = bTime;
        this.checkAdjustPrice = checkAdjustPrice;
    }

    //租期
    private Integer tenancy;

    //取车时间
    private Calendar pTime;

    //下单时间
    private Calendar bTime;

    //校验调价
    private boolean checkAdjustPrice;

    public Integer getTenancy() {
        return tenancy;
    }

    public void setTenancy(Integer tenancy) {
        this.tenancy = tenancy;
    }

    public Calendar getpTime() {
        return pTime;
    }

    public void setpTime(Calendar pTime) {
        this.pTime = pTime;
    }

    public Calendar getbTime() {
        return bTime;
    }

    public void setbTime(Calendar bTime) {
        this.bTime = bTime;
    }

    public boolean isCheckAdjustPrice() {
        return checkAdjustPrice;
    }

    public void setCheckAdjustPrice(boolean checkAdjustPrice) {
        this.checkAdjustPrice = checkAdjustPrice;
    }
}
