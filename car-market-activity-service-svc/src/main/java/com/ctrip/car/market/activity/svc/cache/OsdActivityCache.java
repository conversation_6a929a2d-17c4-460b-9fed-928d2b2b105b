package com.ctrip.car.market.activity.svc.cache;

import com.ctrip.car.market.activity.repository.entity.ActOsdactinfo;
import com.ctrip.car.market.activity.repository.service.ActivityService;
import com.ctrip.car.market.activity.svc.bo.OsdActivity;
import com.ctrip.car.market.common.entity.act.OsdCondition;
import com.ctrip.car.sdcommon.utils.GsonUtil;
import com.ctrip.car.sdcommon.utils.LogUtils;
import com.ctrip.framework.vi.cacheRefresh.CacheManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class OsdActivityCache {

    private final LogUtils<OsdActivityCache> log = new LogUtils<>();

    @Resource
    private ActivityService service;

    private OsdActivityCacheCell cache;

    @PostConstruct
    private synchronized void init() {
        cache = new OsdActivityCacheCell("OsdActivityCache", "0 0/30 * * * ?", () -> {
            try {
                List<ActOsdactinfo> data = service.queryAllOsdActivity();
                Map<Long, OsdActivity> result = Maps.newConcurrentMap();
                for (ActOsdactinfo act : data) {
                    try {
                        OsdActivity item = new OsdActivity();
                        item.setAct(act);
                        item.setCondition(GsonUtil.fromJson(act.getConditions(), OsdCondition.class));
                        result.put(act.getId(), item);
                    } catch (Exception e) {
                        log.error("OsdActivityCache", e);
                    }
                }
                return result;
            } catch (Exception e) {
                log.error("OsdActivityCache.init", e);
                throw new RuntimeException(e);
            }
        });
        CacheManager.add(cache);
        cache.getData();
    }

    public void refreshCache(Long id) {
        try {
            ActOsdactinfo actInfo = service.queryOsdAct(id);
            if (Objects.isNull(actInfo)) {
                return;
            }
            if (Objects.equals(actInfo.getStatus(), 1)) {
                OsdActivity activity = new OsdActivity();
                activity.setAct(actInfo);
                activity.setCondition(GsonUtil.fromJson(actInfo.getConditions(), OsdCondition.class));
                this.cache.refresh(actInfo.getId(), activity);

            } else {
                this.cache.refresh(actInfo.getId(), null);
            }
        } catch (Exception e) {
            log.error("refreshCache", e);
        }
    }

    public List<OsdActivity> getAll() {
        return Lists.newArrayList(this.cache.getData().values());
    }

    public List<OsdActivity> getVendorAct(Long vendorId) {
        return this.cache.getData().values().stream().filter(l -> Objects.nonNull(l.getCondition())
                && (CollectionUtils.isEmpty(l.getCondition().getVendorIds()) || l.getCondition().getVendorIds().contains(vendorId.intValue()))).collect(Collectors.toList());
    }
}
