package com.ctrip.car.market.activity.svc.cache;

import com.ctrip.car.market.activity.core.util.CacheHolder;
import com.ctrip.car.market.activity.svc.bo.OsdActivity;
import com.ctrip.framework.vi.cacheRefresh.CacheCell;
import com.google.common.collect.Maps;

import java.util.Map;
import java.util.function.Supplier;
import java.util.stream.Collectors;

public class OsdActivityCacheCell implements CacheCell {

    private CacheHolder<OsdActivity> cache;

    private String name;

    public OsdActivityCacheCell(String name, String cron, Supplier<Map<Long, OsdActivity>> supplier) {
        this.name = name;
        this.cache = new CacheHolder<>(supplier, cron);
    }

    public Map<Long, OsdActivity> getData() {
        return this.cache.get();
    }

    @Override
    public String id() {
        return this.name;
    }

    @Override
    public boolean refresh() {
        this.cache.refresh();
        return true;
    }

    public void refresh(Long activityId, OsdActivity act) {
        this.cache.refresh(activityId, act);
    }

    @Override
    public Map<String, Object> getStatus() {
        Map<String, Object> map = Maps.newHashMap();
        map.put("refreshTime", this.cache.toString());
        return map;
    }

    @Override
    public Object getByKey(String s) {
        return this.cache.get().get(Long.valueOf(s));
    }

    @Override
    public Iterable<String> keys() {
        return this.cache.get().keySet().stream().map(String::valueOf).collect(Collectors.toList());
    }

    @Override
    public int size() {
        return this.cache.get().size();
    }
}
