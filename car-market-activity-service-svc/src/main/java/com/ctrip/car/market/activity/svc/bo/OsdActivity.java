package com.ctrip.car.market.activity.svc.bo;

import com.ctrip.car.market.activity.repository.entity.ActOsdactinfo;
import com.ctrip.car.market.common.entity.act.OsdCondition;

public class OsdActivity {

    private ActOsdactinfo act;

    private OsdCondition condition;

    public ActOsdactinfo getAct() {
        return act;
    }

    public void setAct(ActOsdactinfo act) {
        this.act = act;
    }

    public OsdCondition getCondition() {
        return condition;
    }

    public void setCondition(OsdCondition condition) {
        this.condition = condition;
    }
}
