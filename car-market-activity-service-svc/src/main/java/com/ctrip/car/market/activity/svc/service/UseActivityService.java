package com.ctrip.car.market.activity.svc.service;

import com.ctrip.car.market.activity.contract.UseActivityRequestType;
import com.ctrip.car.market.activity.contract.UseActivityResponseType;
import com.ctrip.car.market.activity.core.annotation.SOALog;
import com.ctrip.car.market.activity.core.service.BaseService;
import com.ctrip.car.market.activity.core.util.ResponseUtil;
import com.ctrip.car.market.activity.repository.entity.ActCtripactinfo;
import com.ctrip.car.market.activity.repository.entity.CpnUseactivity;
import com.ctrip.car.market.activity.repository.service.ActivityService;
import com.ctrip.car.market.common.enums.ActivityStatus;
import com.ctrip.flight.intl.common.metric.Metrics;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Component
public class UseActivityService extends BaseService {

    @Resource
    private ActivityService activityService;

    private static final String logTitle = "UseActivity";

    @SOALog(logTitle = logTitle)
    public UseActivityResponseType doBusiness(UseActivityRequestType request) {
        UseActivityResponseType response = new UseActivityResponseType();
        if (!this.checkRequest(request, response)) {
            return response;
        }
        try {
            ActCtripactinfo actInfo = activityService.queryByPk(request.getActivityId());
            String checkActivityMsg = this.checkActivity(actInfo);
            if (StringUtils.isNotBlank(checkActivityMsg)) {
                response.setBaseResponse(ResponseUtil.fail(checkActivityMsg));
                return response;
            }
            List<CpnUseactivity> useList = activityService.queryUseActivity(request.getOrderId(), request.getActivityId(), request.getBaseRequest().getUid());
            if (CollectionUtils.isNotEmpty(useList)) {
                response.setBaseResponse(ResponseUtil.fail("do not reuse"));
                return response;
            }
            int result = activityService.insertUseActivity(this.convert(request));
            response.setBaseResponse(result <= 0 ? ResponseUtil.fail("use failed") : ResponseUtil.success());
            return response;
        } catch (Exception e) {
            log.error(logTitle, e);
            response.setBaseResponse(ResponseUtil.systemError());
            return response;
        } finally {
            Metrics.withTag("result", Objects.equals(response.getBaseResponse().getCode(), "000000") ? "1" : "0").recordOne("useActivity");
        }
    }

    private CpnUseactivity convert(UseActivityRequestType request) {
        CpnUseactivity item = new CpnUseactivity();
        item.setConfigId(request.getActivityId());
        item.setOrderAmount(request.getOrderOriginalAmount());
        item.setActivityAmount(request.getActivityAmount());
        item.setUID(request.getBaseRequest().getUid());
        item.setStatus(0);
        item.setOrderID(request.getOrderId());
        return item;
    }

    private String checkActivity(ActCtripactinfo actInfo) {
        if (Objects.isNull(actInfo)) {
            return "activity non-existent";
        }
        if (!Objects.equals(ActivityStatus.Reviewed.getStatus(), actInfo.getStatus())) {
            return "activity expired";
        }
        if (actInfo.getStartTime().getTime() > System.currentTimeMillis()) {
            return "activity not started";
        }
        if (actInfo.getEndTime().getTime() < System.currentTimeMillis()) {
            return "activity expired";
        }
        return null;
    }

    private boolean checkRequest(UseActivityRequestType request, UseActivityResponseType response) {
        if (Objects.isNull(request.getBaseRequest())) {
            response.setBaseResponse(ResponseUtil.fail("base request is null"));
            return false;
        }
        if (StringUtils.isBlank(request.getBaseRequest().getUid())) {
            response.setBaseResponse(ResponseUtil.fail("uid is null"));
            return false;
        }
        if (Objects.isNull(request.getActivityId()) || request.getActivityId() <= 0) {
            response.setBaseResponse(ResponseUtil.fail("activity id is null"));
            return false;
        }
        if (Objects.isNull(request.getOrderId()) || request.getOrderId() <= 0) {
            response.setBaseResponse(ResponseUtil.fail("order id is null"));
            return false;
        }
        if (Objects.isNull(request.getActivityAmount()) || request.getActivityAmount().compareTo(BigDecimal.ZERO) <= 0) {
            response.setBaseResponse(ResponseUtil.fail("activity amount error"));
            return false;
        }
        if (Objects.isNull(request.getOrderOriginalAmount()) || request.getOrderOriginalAmount().compareTo(request.getActivityAmount()) < 0) {
            response.setBaseResponse(ResponseUtil.fail("order original amount error"));
            return false;
        }
        return true;
    }
}
