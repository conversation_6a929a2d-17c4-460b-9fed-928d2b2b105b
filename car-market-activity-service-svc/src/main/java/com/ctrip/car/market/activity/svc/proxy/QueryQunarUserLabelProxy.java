package com.ctrip.car.market.activity.svc.proxy;


import com.ctrip.car.market.activity.svc.bo.QueryUserLabelRequestType;
import com.ctrip.car.market.activity.svc.bo.QueryUserLabelResponseType;
import com.ctrip.car.market.activity.svc.config.BaseConfig;
import com.ctrip.car.market.activity.svc.util.JsonUtil;
import com.ctrip.car.market.activity.svc.util.JsonUtils;
import com.ctrip.car.market.activity.svc.util.RedisUtil;
import com.ctrip.flight.intl.common.metric.Metrics;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.util.concurrent.ListenableFuture;
import com.ning.http.client.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.core.util.UuidUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qunar.hc.QHttpOption;
import qunar.hc.QunarAsyncClient;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class QueryQunarUserLabelProxy {
    @Autowired
    private BaseConfig baseConfig;

    private static final ILog log = LogManager.getLogger(QueryQunarUserLabelProxy.class);

    private final QunarAsyncClient qunarAsyncClient = new QunarAsyncClient();

    private final static String KEY_FORMAT = "car.market.qunar.uid.%s";

    public Map<String, String> queryQunarUid(String uid, List<String> qunarCodeList, boolean isQmq) {
        try {
            long startTime = System.currentTimeMillis();
            if (StringUtils.isEmpty(uid) || CollectionUtils.isEmpty(qunarCodeList)) {
                return null;
            }
            String key = String.format(KEY_FORMAT, uid);
            String cacheValue = RedisUtil.get(key);
            if (StringUtils.isNotEmpty(cacheValue)) {
                // 命中率埋点
                Metrics.withTag("result",  "1").withTag("isQmq", isQmq ? "1" : "0").recordOne("hit_qunar_user_tag");
                return JsonUtils.toObject(cacheValue, new TypeReference<>() {});
            }
            Metrics.withTag("result",  "0").withTag("isQmq", isQmq ? "1" : "0").recordOne("hit_qunar_user_tag");
            QueryUserLabelRequestType req = new QueryUserLabelRequestType();
            req.setUserId(uid);
            req.setLabelCodes(qunarCodeList);
            req.setRequestId(UuidUtil.getTimeBasedUuid().toString());
            String res = doPostProxyWithProxy(baseConfig.getServiceUrl(), req);
            QueryUserLabelResponseType response = JsonUtils.toObject(res, QueryUserLabelResponseType.class);
            if (response == null || response.getBaseResponse() == null || response.getUserTagMap() == null) {
                return null;
            }
            RedisUtil.set(key, JsonUtil.toString(response.getUserTagMap()), baseConfig.getsCookieExpire());
            Map<String, String> userTagMap = response.getUserTagMap();
            for(Map.Entry<String, String> entry : userTagMap.entrySet()) {
               // 埋点
               Metrics.withTag("qunar_tag", entry.getKey()).withTag("value", entry.getValue()).recordOne("qunar_user_tag");
            }
            Metrics.recordOne("qunar_user_tag_count", System.currentTimeMillis() - startTime);
            return userTagMap;

        } catch (Exception e) {
            log.error("queryQunarUid", e);
            return new HashMap<>();
        }
    }

    public String doPostProxyWithProxy(String url, QueryUserLabelRequestType req) {
        String res = "";
        log.info("getQunarUser", url + "\n"  + JsonUtil.toString(req));
        try {
            QHttpOption option = new QHttpOption();
            option.addHeader("Content-Type", "application/json; charset=utf-8");
            option.setPostBodyData(JsonUtil.toString(req));
            ListenableFuture<Response> responseFuture = qunarAsyncClient.post(url, option);
            Response response = responseFuture.get(baseConfig.getQunarUserTagTimeout(), java.util.concurrent.TimeUnit.MILLISECONDS);
            if (response != null && response.getStatusCode() == 200) {
                res = response.getResponseBody("UTF-8");
            }
        } catch (Exception ex) {
            log.error("getQunarUser", ex + "\n" + url + "\n");
        }
        log.info("getQunarUser", url + "\n"  + res);
        return res;
    }
}
