package com.ctrip.car.market.activity.svc.service;

import com.ctrip.car.market.activity.contract.QueryVendorActivityRequestType;
import com.ctrip.car.market.activity.contract.QueryVendorActivityResponseType;
import com.ctrip.car.market.activity.core.annotation.SOALog;
import com.ctrip.car.market.activity.core.service.BaseService;
import com.ctrip.car.market.activity.core.util.ResponseUtil;
import com.ctrip.car.market.activity.svc.cache.neww.ActivityInfoCache;
import com.ctrip.car.market.job.common.entity.ActInfoDO;
import com.ctrip.flight.intl.common.metric.Metrics;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class QueryVendorActivityService extends BaseService {

    @Resource
    private ActivityInfoCache activityInfoCache;

    private final static String logTitle = "QueryVendorActivity";

    @SOALog(logTitle = logTitle)
    public QueryVendorActivityResponseType doBusiness(QueryVendorActivityRequestType request) {
        QueryVendorActivityResponseType response = new QueryVendorActivityResponseType();
        if (Optional.ofNullable(request.getVendorId()).orElse(0L) <= 0) {
            response.setBaseResponse(ResponseUtil.fail("vendor error"));
            return response;
        }
        try {
            response.setBaseResponse(ResponseUtil.success());
            List<ActInfoDO> actList = activityInfoCache.queryByVendorId(request.getVendorId());
            response.setActivityList(Optional.ofNullable(actList).orElse(Lists.newArrayList()).stream().map(ActInfoDO::getId).distinct().sorted().collect(Collectors.toList()));
            return response;
        } catch (Exception e) {
            log.error(logTitle, e);
            response.setBaseResponse(ResponseUtil.systemError());
            return response;
        } finally {
            Metrics.withTag("vendorId", request.getVendorId().toString())
                    .withTag("result", CollectionUtils.isNotEmpty(response.getActivityList()) ? "1" : "0")
                    .recordOne("queryVendorActivity");
        }
    }
}
