package com.ctrip.car.market.activity.svc.cache.neww;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheBean;
import com.alicp.jetcache.anno.IncUpdateConsumerByRedis;
import com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst;
import com.ctrip.car.market.job.common.consts.CacheName;
import com.ctrip.car.market.job.common.entity.ActVendorSkuInfoDO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@CreateCacheBean
public class ActivityVendorSkuCache {

    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.IsdActivityVendorSKuCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.ACT_VENDOR_SKU_KEY)
    public Cache<Long, List<ActVendorSkuInfoDO>> vendorSkuCache;

    public List<Long> queryByActId(Long actId) {
        List<ActVendorSkuInfoDO> list = vendorSkuCache.get(actId);
        return CollectionUtils.isNotEmpty(list)
                ? list.stream().map(ActVendorSkuInfoDO::getSkuId).collect(Collectors.toList())
                : Lists.newArrayList();
    }
}
