package com.ctrip.car.market.activity.svc.service;

import com.ctrip.car.market.activity.contract.QueryActivityListRequestType;
import com.ctrip.car.market.activity.contract.QueryActivityListResponseType;
import com.ctrip.car.market.activity.core.annotation.SOALog;
import com.ctrip.car.market.activity.core.service.BaseService;
import com.ctrip.car.market.activity.core.util.ResponseUtil;
import com.ctrip.car.market.activity.svc.bo.HolidayDto;
import com.ctrip.car.market.activity.svc.bo.UserGroupDTO;
import com.ctrip.car.market.activity.svc.cache.neww.ActivityCityCache;
import com.ctrip.car.market.activity.svc.cache.neww.ActivityInfoCache;
import com.ctrip.car.market.activity.svc.cache.neww.ActivityTempCache;
import com.ctrip.car.market.activity.svc.config.BaseConfig;
import com.ctrip.car.market.activity.svc.proxy.CdpServiceProxy;
import com.ctrip.car.market.activity.svc.proxy.QueryQunarUserLabelProxy;
import com.ctrip.car.market.activity.svc.util.CommonUtil;
import com.ctrip.car.market.activity.svc.util.ActivityListConditionUtil;
import com.ctrip.car.market.activity.svc.util.VerificationUtil;
import com.ctrip.car.market.job.common.entity.ActInfoDO;
import com.ctrip.car.market.job.common.entity.ActTempInfoDO;
import com.ctrip.flight.intl.common.metric.Metrics;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class QueryActivityListNewService extends BaseService {

    @Resource
    private ActivityTempCache activityTempCache;

    @Resource
    private ActivityInfoCache activityInfoCache;

    @Resource
    private ActivityCityCache activityCityCache;

    @Resource
    private ActivityListConditionUtil activityListConditionUtil;

    @Resource
    private BaseConfig baseConfig;

    @Resource
    private QueryQunarUserLabelProxy queryQunarUserLabelProxy;

    private static final String logTitle = "QueryNewActivityList";

    @SOALog(logTitle = logTitle)
    public QueryActivityListResponseType doBusiness(QueryActivityListRequestType request) {
        QueryActivityListResponseType response = new QueryActivityListResponseType();
        if (!this.checkRequest(request, response)) {
            return response;
        }
        //是否需要盲盒活动
        boolean isBlindBox = CommonUtil.isBlindBox(request.getBaseRequest().getExtList());
        try {
            response.setBaseResponse(ResponseUtil.success());
            List<Long> result = getAct(request);
            response.setActivityList(result.stream().distinct().sorted().collect(Collectors.toList()));
            return response;
        } catch (Exception e) {
            log.error(logTitle, e);
            response.setBaseResponse(ResponseUtil.systemError());
            return response;
        } finally {
            Metrics.withTag("sourceFrom", StringUtils.isEmpty(request.getBaseRequest().getSourceFrom()) ? "0" : request.getBaseRequest().getSourceFrom())
                    .withTag("channel", Objects.isNull(request.getBaseRequest().getChannelId()) ? "0" : request.getBaseRequest().getChannelId().toString())
                    .withTag("result", CollectionUtils.isNotEmpty(response.getActivityList()) ? "1" : "0")
                    .withTag("blindBox", isBlindBox ? "1" : "0")
                    .recordOne("queryActivityList");
        }
    }

    private List<Long> getAct(QueryActivityListRequestType request) {
        try {
            //获取所有模版
            List<ActTempInfoDO> actTempList = activityTempCache.getAll();
            if (CollectionUtils.isEmpty(actTempList)) {
                log.warn("getAct", "temp null");
                return Lists.newArrayList();
            }
            //是否需要盲盒活动
            boolean isBlindBox = CommonUtil.isBlindBox(request.getBaseRequest().getExtList());
            //节假日规则
            HolidayDto holidayDto = CommonUtil.getHoliday(request.getProductCondition().getPickUpTime(), request.getProductCondition().getReturnTime(), baseConfig.getHolidayList());
            Calendar current = Calendar.getInstance();
            //租期
            Integer tenancy = CommonUtil.getTenancy(request.getProductCondition().getPickUpTime(), request.getProductCondition().getReturnTime());
            //是否零散小时
            boolean isScatteredHour = CommonUtil.isScatteredHour(request.getProductCondition().getPickUpTime(), request.getProductCondition().getReturnTime());
            Integer sourceFrom = CommonUtil.getSourceFrom(request.getBaseRequest().getSourceFrom(), baseConfig.getSourceFromMapping());
            //分销渠道活动配置
            Set<Long> channelActivityConfig = baseConfig.getChannelActivity(request.getBaseRequest().getChannelId());
            //获取用户画像
            Set<String> userSet = getUserSecret(request.getBaseRequest().getUid(), actTempList, request.getBaseRequest().getSourceFrom());
            //提前预定期
            long advanceTime = CommonUtil.getAdvanceTime(request.getProductCondition().getPickUpTime());
            Set<Integer> pCitySet = getCitySet(request.getProductCondition().getCityIds());
            // 判断是否是qunar
            boolean isQ = baseConfig.getQunarSourceFromList().contains(request.getBaseRequest().getSourceFrom());
            Set<String> qunarUserTags = baseConfig.getQunarUserTags().stream().map(UserGroupDTO::getSecret).collect(Collectors.toSet());
            //是否调价版本
            boolean followPrice = CommonUtil.extContain(request.getBaseRequest().getExtList(), "followPrice");
            //过滤符合条件的活动模版
            List<ActTempInfoDO> list = actTempList.stream().filter(activityListConditionUtil.filterSecret(userSet, isQ, qunarUserTags)
                    .and(activityListConditionUtil.filterActivityGroup(followPrice))
                    .and(activityListConditionUtil.filterActivityTime(current))
                    .and(activityListConditionUtil.filterExcludeData(current))
                    .and(activityListConditionUtil.filterTenancy(tenancy, isScatteredHour))
                    .and(activityListConditionUtil.filterPickupTime(request.getProductCondition().getPickUpTime(), request.getProductCondition().getReturnTime()))
                    .and(activityListConditionUtil.filterSourceFrom(sourceFrom))
                    .and(activityListConditionUtil.filterChannel(request.getBaseRequest().getChannelId(), request.getBaseRequest().getDistributionChannelId(), baseConfig.getDistributionChannel(), channelActivityConfig))
                    .and(activityListConditionUtil.filterBlindBox(isBlindBox))
                    .and(activityListConditionUtil.filterTakeTimeType(request.getProductCondition().getPickUpTime()))
                    .and(activityListConditionUtil.filterAdvanceTime(advanceTime))
                    .and(activityListConditionUtil.filterRepetitionPeriod(current))
                    .and(activityListConditionUtil.filterTempHoliday(holidayDto))
                    .and(activityListConditionUtil.filterTempCity(pCitySet))).toList();
            Set<Long> vendorActSet = Objects.equals(baseConfig.isListFilterVendor(), true) ? getVendorAct(request.getProductCondition().getVendorIdList()) : null;
            return filterAct(list, holidayDto, pCitySet, vendorActSet);
        } catch (Exception e) {
            log.error("getAct", e);
            return Lists.newArrayList();
        }
    }

    private Set<Long> getVendorAct(List<Long> vendorIdList) {
        if (CollectionUtils.isEmpty(vendorIdList)) {
            return null;
        }
        //兼容平台补贴活动
        vendorIdList.add(0L);
        return vendorIdList.stream().flatMap(l -> activityInfoCache.queryActIdByVendor(l).stream()).collect(Collectors.toSet());
    }

    private List<Long> filterAct(List<ActTempInfoDO> tempList, HolidayDto holidayDto, Set<Integer> citySet, Set<Long> vendorActSet) {
        List<Long> result = Lists.newArrayList();
        for (ActTempInfoDO actTempInfoDO : tempList) {
            List<Long> idList = activityInfoCache.queryActId(actTempInfoDO.getTmpId());
            if (CollectionUtils.isEmpty(idList)) {
                continue;
            }
            //过滤供应商活动
            if (CollectionUtils.isNotEmpty(vendorActSet)) {
                idList = idList.stream().filter(vendorActSet::contains).collect(Collectors.toList());
            }
            //节假日过滤
            if (holidayDto != null) {
                idList = idList.stream().filter(l -> {
                    //供应商自定义节假日规则
                    if (Objects.equals(actTempInfoDO.getContent().getSupplierCondition().getAllowHolidayLimit(), true)) {
                        ActInfoDO actInfoDO = activityInfoCache.queryById(l);
                        if (Objects.nonNull(actInfoDO) && Objects.nonNull(actInfoDO.getContent())
                                && Objects.nonNull(actInfoDO.getContent().getSupplierCondition()) && Objects.nonNull(actInfoDO.getContent().getSupplierCondition().getAllowHoliday())) {
                            return Objects.equals(actInfoDO.getContent().getSupplierCondition().getAllowHoliday(), true);
                        }
                    }
                    return Objects.equals(actTempInfoDO.getContent().getSupplierCondition().getAllowHoliday(), true);
                }).toList();
            }
            //城市过滤
            if (baseConfig.isListFilterCity() && CollectionUtils.isNotEmpty(citySet)) {
                idList = idList.stream().filter(l -> {
                    List<Integer> cityList = activityCityCache.queryCityByActId(l);
                    return CollectionUtils.isEmpty(cityList) || cityList.stream().anyMatch(citySet::contains);
                }).toList();
            }
            result.addAll(idList);
        }
        return result;
    }

    private Set<Integer> getCitySet(List<Integer> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Sets.newHashSet();
        }
        return list.stream().filter(Objects::nonNull).collect(Collectors.toSet());
    }

    public Set<String> getUserSecret(String uid, List<ActTempInfoDO> actTempList, String sourceFrom) {
        if (StringUtils.isBlank(uid)) {
            return Sets.newHashSet();
        }
        if (CollectionUtils.isEmpty(actTempList)) {
            return Sets.newHashSet();
        }

        List<String> secrets = actTempList.stream().filter(l -> Objects.nonNull(l.getContent()) && Objects.nonNull(l.getContent().getUserCondition())
                        && CollectionUtils.isNotEmpty(l.getContent().getUserCondition().getUserAttributes()))
                .flatMap(l -> l.getContent().getUserCondition().getUserAttributes().stream()).distinct().collect(Collectors.toList());

        Set<String> qunarSets = baseConfig.getQunarUserTags().stream().map(UserGroupDTO::getSecret).collect(Collectors.toSet());
        if (baseConfig.getQunarSourceFromList().contains(sourceFrom)) {
            List<String> qunarList = secrets.stream().filter(qunarSets::contains).toList();
            Map<String, String> qunarUserTagMap = queryQunarUserLabelProxy.queryQunarUid(uid, qunarList, false);
            if (qunarUserTagMap != null && !qunarUserTagMap.isEmpty()) {
                // 遍历qunarUserTagMap，收集map中value = 1 的 key
                return qunarUserTagMap.entrySet().stream()
                        .filter(entry -> Objects.equals(entry.getValue(), "1"))
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toSet());
            }
            return Sets.newHashSet();
        } else {
            List<String> list = secrets.stream().filter(x -> !qunarSets.contains(x)).toList();
            return CdpServiceProxy.userHitIn(uid, list);
        }
    }

    private boolean checkRequest(QueryActivityListRequestType request, QueryActivityListResponseType response) {
        if (Objects.isNull(request.getBaseRequest())) {
            response.setBaseResponse(ResponseUtil.fail("base request is null"));
            return false;
        }
        String productConditionCheck = VerificationUtil.checkProductCondition(request.getProductCondition(), 0);
        if (StringUtils.isNotBlank(productConditionCheck)) {
            response.setBaseResponse(ResponseUtil.fail(productConditionCheck));
            return false;
        }
        return true;
    }
}
