package com.ctrip.car.market.activity.svc.dto.old;

public class ActivityInfoErrorInfoDTO {

    //活动ID 一定要给
    private Long configId;

    //取车城市ID
    private int pickUpCityId;

    //还车城市ID
    private int returnCityId;

    //供应商ID
    private String vendorId;

    //供应商名称
    private String vendorName;

    //取车城市名字
    private String pickUpCityName;

    //还车城市名字
    private String returnCityName;

    //错误原因
    private String errorInfo;

    //报错时间，取当前时间即可
    private String happenDate;

    // 活动code
    private String activityCode;

    //报错用户ID 一定要给
    private String uid;

    //shopping的requestid，用来查日志的，i
    private String requestId;

    //车辆ID
    private String vehicleId;

    //车辆名称
    private String vehicleName;

    private String pickVendorStoreId;

    private String pickVendorStoreName;

    private String returnVendorStoreId;

    private String returnVendorStoreName;

    public Long getConfigId() {
        return configId;
    }

    public void setConfigId(Long configId) {
        this.configId = configId;
    }

    public int getPickUpCityId() {
        return pickUpCityId;
    }

    public void setPickUpCityId(int pickUpCityId) {
        this.pickUpCityId = pickUpCityId;
    }

    public int getReturnCityId() {
        return returnCityId;
    }

    public void setReturnCityId(int returnCityId) {
        this.returnCityId = returnCityId;
    }

    public String getVendorId() {
        return vendorId;
    }

    public void setVendorId(String vendorId) {
        this.vendorId = vendorId;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getPickUpCityName() {
        return pickUpCityName;
    }

    public void setPickUpCityName(String pickUpCityName) {
        this.pickUpCityName = pickUpCityName;
    }

    public String getReturnCityName() {
        return returnCityName;
    }

    public void setReturnCityName(String returnCityName) {
        this.returnCityName = returnCityName;
    }

    public String getErrorInfo() {
        return errorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        this.errorInfo = errorInfo;
    }

    public String getHappenDate() {
        return happenDate;
    }

    public void setHappenDate(String happenDate) {
        this.happenDate = happenDate;
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getVehicleName() {
        return vehicleName;
    }

    public void setVehicleName(String vehicleName) {
        this.vehicleName = vehicleName;
    }

    public String getPickVendorStoreId() {
        return pickVendorStoreId;
    }

    public void setPickVendorStoreId(String pickVendorStoreId) {
        this.pickVendorStoreId = pickVendorStoreId;
    }

    public String getPickVendorStoreName() {
        return pickVendorStoreName;
    }

    public void setPickVendorStoreName(String pickVendorStoreName) {
        this.pickVendorStoreName = pickVendorStoreName;
    }

    public String getReturnVendorStoreId() {
        return returnVendorStoreId;
    }

    public void setReturnVendorStoreId(String returnVendorStoreId) {
        this.returnVendorStoreId = returnVendorStoreId;
    }

    public String getReturnVendorStoreName() {
        return returnVendorStoreName;
    }

    public void setReturnVendorStoreName(String returnVendorStoreName) {
        this.returnVendorStoreName = returnVendorStoreName;
    }
}
