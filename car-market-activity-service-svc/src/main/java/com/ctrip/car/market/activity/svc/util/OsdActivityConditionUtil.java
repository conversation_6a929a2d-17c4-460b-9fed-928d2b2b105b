package com.ctrip.car.market.activity.svc.util;

import com.ctrip.car.market.activity.svc.bo.OsdActivity;
import com.ctrip.car.market.common.entity.act.LimitRange;
import com.ctrip.car.market.common.enums.OsdActivityType;
import org.apache.commons.collections.CollectionUtils;

import java.util.Calendar;
import java.util.Objects;
import java.util.Set;
import java.util.function.Predicate;

public class OsdActivityConditionUtil {

    public static Predicate<OsdActivity> filterActivityTime(Calendar current) {
        return x -> {
            if (Objects.isNull(x.getCondition()) || CollectionUtils.isEmpty(x.getCondition().getActivityTimeRange())) {
                return false;
            }
            return x.getCondition().getActivityTimeRange().stream().anyMatch(l -> {
                if (Objects.isNull(l.getStart()) || Objects.isNull(l.getEnd())) {
                    return false;
                }
                return l.getStart().getTimeInMillis() <= current.getTimeInMillis() && l.getEnd().getTimeInMillis() >= current.getTimeInMillis();
            });
        };
    }

    public static Predicate<OsdActivity> filterPickupTime(Calendar pTime) {
        return x -> {
            if (Objects.isNull(x.getCondition())) {
                return false;
            }
            //供应商套餐活动不验证
            if (Objects.equals(x.getAct().getType(), OsdActivityType.VENDOR_PACKAGE.getStatus())) {
                return true;
            }
            if (CollectionUtils.isEmpty(x.getCondition().getPickUpTimeRange())) {
                return true;
            }
            return x.getCondition().getPickUpTimeRange().stream().anyMatch(l -> (l.getEnd() == null || pTime.compareTo(l.getEnd()) <= 0)
                    && (l.getStart() == null || pTime.compareTo(l.getStart()) >= 0));
        };
    }

    public static Predicate<OsdActivity> filterReturnTime(Calendar rTime) {
        return x -> {
            if (Objects.isNull(x.getCondition())) {
                return false;
            }
            //供应商套餐活动不验证
            if (Objects.equals(x.getAct().getType(), OsdActivityType.VENDOR_PACKAGE.getStatus())) {
                return true;
            }
            if (CollectionUtils.isEmpty(x.getCondition().getReturnTimeRange())) {
                return true;
            }
            return x.getCondition().getReturnTimeRange().stream().anyMatch(l -> (l.getEnd() == null || rTime.compareTo(l.getEnd()) <= 0)
                    && (l.getStart() == null || rTime.compareTo(l.getStart()) >= 0));
        };
    }

    public static Predicate<OsdActivity> filterTenancy(Integer tenancy, boolean isScatteredHour) {
        return x -> {
            if (Objects.isNull(x.getCondition())) {
                return false;
            }
            LimitRange limitRange = x.getCondition().getTenancyRange();
            if (limitRange == null) {
                return true;
            }
            //最小租期=最大租期
            if (Objects.nonNull(limitRange.getFloor()) && Objects.nonNull(limitRange.getUpline()) && Objects.equals(limitRange.getFloor(), limitRange.getUpline())) {
                return !isScatteredHour && Objects.equals(tenancy, limitRange.getFloor());
            }
            //存在零散小时
            if (isScatteredHour) {
                return (Objects.isNull(limitRange.getFloor()) || limitRange.getFloor() <= tenancy) && (Objects.isNull(limitRange.getUpline()) || limitRange.getUpline() >= tenancy + 1);
            }
            return (Objects.isNull(limitRange.getFloor()) || limitRange.getFloor() <= tenancy) && (Objects.isNull(limitRange.getUpline()) || limitRange.getUpline() >= tenancy);
        };
    }

    public static Predicate<OsdActivity> filterArea(Set<Long> areaSet) {
        return x -> {
            if (Objects.isNull(x.getCondition())) {
                return false;
            }
            if (CollectionUtils.isEmpty(x.getCondition().getAreaIds())) {
                return true;
            }
            if (Objects.equals(x.getCondition().getExcludeAreaId(), true)) {
               return x.getCondition().getAreaIds().stream().noneMatch(areaSet::contains);
            }
            return x.getCondition().getAreaIds().stream().anyMatch(areaSet::contains);
        };
    }

    public static Predicate<OsdActivity> filterStandardProduct(Long standardProductId) {
        return x -> {
            if (Objects.isNull(x.getCondition())) {
                return false;
            }
            //有价格信息id时不需要验证
            if (Objects.nonNull(x.getCondition().getPriceId()) && x.getCondition().getPriceId() > 0L) {
                return true;
            }
            return CollectionUtils.isEmpty(x.getCondition().getStandardProductIds()) || x.getCondition().getStandardProductIds().contains(standardProductId);
        };
    }
}
