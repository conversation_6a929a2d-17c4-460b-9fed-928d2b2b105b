package com.ctrip.car.market.activity.svc.util;

import com.ctrip.car.market.activity.contract.KeyValueDTO;
import com.ctrip.car.market.activity.svc.bo.Activity;
import com.ctrip.car.market.activity.svc.bo.HolidayDto;
import com.ctrip.car.market.activity.svc.bo.SourceFromMappingDto;
import com.ctrip.car.market.activity.svc.proxy.CdpServiceProxy;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

public class CommonUtil {

//    public static List<Activity> filterSecret(String uid, List<Activity> activityList) {
//        if (CollectionUtils.isEmpty(activityList)) {
//            return Lists.newArrayList();
//        }
//        List<String> secrets = activityList.stream().filter(l -> Objects.nonNull(l.getTemp().getContent()) && Objects.nonNull(l.getTemp().getContent().getUserCondition())
//                        && CollectionUtils.isNotEmpty(l.getTemp().getContent().getUserCondition().getUserAttributes()))
//                .flatMap(l -> l.getTemp().getContent().getUserCondition().getUserAttributes().stream()).distinct().collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(secrets)) {
//            return activityList;
//        }
//        Set<String> secretSet = CdpServiceProxy.userHitIn(uid, secrets);
//        return activityList.stream().filter(ActivityConditionUtil.filterSecret(secretSet)).collect(Collectors.toList());
//    }

    public static Integer getTenancy(Calendar pTime, Calendar rTime) {
        long t = (rTime.getTimeInMillis() - pTime.getTimeInMillis()) / (24 * 3600000);
        return (int) t;
    }

    public static HolidayDto getHoliday(Calendar pTime, Calendar rTime, List<HolidayDto> holidayList) {
        if (CollectionUtils.isEmpty(holidayList)) {
            return null;
        }
        return holidayList.stream().filter(l -> Objects.nonNull(l.getStart()) && Objects.nonNull(l.getEnd())
                && pTime.getTimeInMillis() <= l.getEnd().getTime() && rTime.getTimeInMillis() >= l.getStart().getTime()).findFirst().orElse(null);
    }

    public static Integer getSourceFrom(String sourceFrom, List<SourceFromMappingDto> mapping) {
        return mapping.stream().filter(l -> l.getMapping().stream().anyMatch(li -> StringUtils.equalsIgnoreCase(li, sourceFrom))).findFirst().map(SourceFromMappingDto::getSourceFrom).orElse(null);
    }

    public static boolean isBlindBox(List<KeyValueDTO> extList) {
        return CollectionUtils.isNotEmpty(extList) && extList.stream().anyMatch(l -> StringUtils.equalsIgnoreCase("blindBox", l.getKey()));
    }

    public static boolean extContain(List<KeyValueDTO> extList, String key) {
        return CollectionUtils.isNotEmpty(extList) && extList.stream().anyMatch(l -> StringUtils.equalsIgnoreCase(key, l.getKey()));
    }

    public static boolean isScatteredHour(Calendar pTime, Calendar rTime) {
        long t = (rTime.getTimeInMillis() - pTime.getTimeInMillis()) % (24 * 3600000);
        return t > 0;
    }

    public static Long getPriceId(List<Long> list, Long id) {
        if (CollectionUtils.isEmpty(list)) {
            return id;
        }
        return list.stream().filter(Objects::nonNull).findFirst().orElse(id);
    }

    public static List<Long> getPriceIds(List<Long> list, Long id) {
        if (CollectionUtils.isNotEmpty(list) && list.stream().anyMatch(Objects::nonNull)) {
            return list.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }
        if (Objects.nonNull(id)) {
            return Lists.newArrayList(id);
        }
        return null;
    }

    public static long getAdvanceTime(Calendar pTime) {
        return (pTime.getTimeInMillis() - System.currentTimeMillis()) / 1000;
    }

    public static List<Long> getActivityIdList(Long activityId, List<Long> activityIdList) {
        List<Long> list = Lists.newArrayList();
        if (Optional.ofNullable(activityId).orElse(0L) > 0) {
            list.add(activityId);
        }
        if (CollectionUtils.isNotEmpty(activityIdList)) {
            list.addAll(activityIdList);
        }
        return list.stream().filter(Objects::nonNull).distinct().toList();
    }
}
