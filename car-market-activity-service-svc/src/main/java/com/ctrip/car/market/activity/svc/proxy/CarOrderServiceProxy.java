package com.ctrip.car.market.activity.svc.proxy;

import com.ctrip.car.carsdqueryorderservice.soa.autogenerate.CarSDQueryOrderServiceClient;
import com.ctrip.car.carsdqueryorderservice.soa.autogenerate.QueryOrdOrderStaticsByOrderIdRequestType;
import com.ctrip.car.carsdqueryorderservice.soa.autogenerate.QueryOrdOrderStaticsByOrderIdResponseType;
import com.ctrip.car.market.activity.svc.util.JsonUtil;
import com.ctrip.car.sdcommon.utils.LogUtils;
import com.google.common.collect.Maps;

import java.util.Calendar;
import java.util.Map;

public class CarOrderServiceProxy {

    private static final CarSDQueryOrderServiceClient orderClient = CarSDQueryOrderServiceClient.getInstance();

    private static final LogUtils<CarOrderServiceProxy> log = new LogUtils<>();

    public static Calendar queryOrderDate(Long orderId) {
        try {
            if (orderId == null) {
                return null;
            }
            QueryOrdOrderStaticsByOrderIdRequestType request = new QueryOrdOrderStaticsByOrderIdRequestType();
            request.setOrderId(orderId);
            QueryOrdOrderStaticsByOrderIdResponseType response = orderClient.queryOrdOrderStaticByOrderId(request);
            Map<String, String> tag = Maps.newHashMap();
            tag.put("orderId", orderId.toString());
            log.info("queryOrder", JsonUtil.toString(response), tag);
            return response != null && response.getOrdOrderStatic() != null ? response.getOrdOrderStatic().getOrderDate() : null;
        } catch (Exception e) {
            log.error("queryOrder", e);
            return null;
        }
    }
}
