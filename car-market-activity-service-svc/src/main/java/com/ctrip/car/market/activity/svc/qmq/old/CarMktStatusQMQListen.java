package com.ctrip.car.market.activity.svc.qmq.old;

import com.ctrip.car.market.activity.core.service.BaseService;
import com.ctrip.car.market.activity.svc.dto.old.ActivityInfoErrorInfoDTO;
import com.ctrip.car.market.activity.svc.service.old.AutoCloseActivityService;
import com.ctrip.car.market.activity.svc.util.JsonUtil;
import com.ctrip.car.sdcommon.utils.HandlerContextMgt;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Objects;

@Component
public class CarMktStatusQMQListen extends BaseService {

    @Autowired
    private AutoCloseActivityService autoCloseActivityService;

    @QmqConsumer(prefix = "car.marekt.status", consumerGroup = "car.mkt.coupon.consumer.8873", consumeMostOnce = true)
    public void listenCarMktStatusQMQ(Message message) {
        HandlerContextMgt.destoryCurrentHandlerContext();
        String uid = message.getStringProperty("uid");
        String content = message.getStringProperty("content");
        String msgType = message.getStringProperty("msgType");
        getHandlerContext().writeTagMap("uid", uid);
        getHandlerContext().writeTagMap("msgType", msgType);
        log.info("CarMktStatusQMQListen.msgType", msgType, getHandlerContext().getTagMap());
        if (StringUtils.isNotBlank(msgType)) {
            switch (msgType) {
                case "autoCloseActivity":
                    log.info("CarMktStatusQMQListen.content", content, getHandlerContext().getTagMap());
                    if (StringUtils.isNotEmpty(content)) {
                        ActivityInfoErrorInfoDTO activityInfoErrorInfoDTO = JsonUtil.toObject(content, ActivityInfoErrorInfoDTO.class);
                        if (Objects.nonNull(activityInfoErrorInfoDTO)) {
                            activityInfoErrorInfoDTO.setUid(uid);
                            autoCloseActivityService.autoCloseActivityServiceV2(activityInfoErrorInfoDTO);
                        } else {
                            log.warn("CarMktStatusQMQListen.listenCarMktStatusQMQ", "activityInfoErrorInfoDTO is null", getHandlerContext().getTagMap());
                        }
                    } else {
                        log.warn("CarMktStatusQMQListen.listenCarMktStatusQMQ", "content is null", getHandlerContext().getTagMap());
                    }
                    return;
                default:
                    log.warn("CarMktStatusQMQListen.listen_message_invalid", JsonUtil.toString((message)), getHandlerContext().getTagMap());
            }
        } else {
            log.warn("CarMktStatusQMQListen.listen_message_invalid", JsonUtil.toString((message)), getHandlerContext().getTagMap());
        }
        HandlerContextMgt.destoryCurrentHandlerContext();
    }
}
