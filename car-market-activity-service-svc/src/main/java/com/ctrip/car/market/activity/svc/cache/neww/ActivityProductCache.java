package com.ctrip.car.market.activity.svc.cache.neww;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheBean;
import com.alicp.jetcache.anno.IncUpdateConsumerByRedis;
import com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst;
import com.ctrip.car.market.job.common.consts.CacheName;
import com.ctrip.car.market.job.common.entity.ActProductDO;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@CreateCacheBean
public class ActivityProductCache {

    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.IsdActivityProductCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.ACT_PRODUCT_KEY)
    public Cache<Long, List<ActProductDO>> cache;

    public List<Long> queryByActId(Long actId) {
        List<ActProductDO> list = cache.get(actId);
        return CollectionUtils.isNotEmpty(list)
                ? list.stream().map(ActProductDO::getStandardPId).collect(Collectors.toList())
                : Lists.newArrayList();
    }
}
