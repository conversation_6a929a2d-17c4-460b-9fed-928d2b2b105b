package com.ctrip.car.market.activity.svc.bo;

import java.math.BigDecimal;

public class ActivityPrice {

    private BigDecimal activityAmount;

    private String vendorCheckCode;

    private BigDecimal realActivityAmount;

    public BigDecimal getActivityAmount() {
        return activityAmount;
    }

    public void setActivityAmount(BigDecimal activityAmount) {
        this.activityAmount = activityAmount;
    }

    public String getVendorCheckCode() {
        return vendorCheckCode;
    }

    public void setVendorCheckCode(String vendorCheckCode) {
        this.vendorCheckCode = vendorCheckCode;
    }

    public BigDecimal getRealActivityAmount() {
        return realActivityAmount;
    }

    public void setRealActivityAmount(BigDecimal realActivityAmount) {
        this.realActivityAmount = realActivityAmount;
    }
}
