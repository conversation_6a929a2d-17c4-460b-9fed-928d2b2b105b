package com.ctrip.car.market.activity.svc.service;

import com.ctrip.car.market.activity.contract.QueryOsdActivityListRequestType;
import com.ctrip.car.market.activity.contract.QueryOsdActivityListResponseType;
import com.ctrip.car.market.activity.core.annotation.SOALog;
import com.ctrip.car.market.activity.core.service.BaseService;
import com.ctrip.car.market.activity.core.util.ResponseUtil;
import com.ctrip.car.market.activity.svc.bo.OsdActivity;
import com.ctrip.car.market.activity.svc.cache.OsdActivityCache;
import com.ctrip.car.market.activity.svc.proxy.CarCommodityServiceProxy;
import com.ctrip.car.market.activity.svc.util.CommonUtil;
import com.ctrip.car.market.activity.svc.util.OsdActivityConditionUtil;
import com.ctrip.flight.intl.common.metric.Metrics;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class QueryOsdActivityListService extends BaseService {

    @Resource
    private OsdActivityCache osdActivityCache;

    private final static String logTitle = "queryOsdActivityList";

    @SOALog(logTitle = logTitle)
    public QueryOsdActivityListResponseType doBusiness(QueryOsdActivityListRequestType request) {
        QueryOsdActivityListResponseType response = new QueryOsdActivityListResponseType();
        if (!this.checkRequest(request, response)) {
            return response;
        }
        try {
            response.setBaseResponse(ResponseUtil.success());
            //根据城市id反查区域组
            Set<Long> areaIds = CarCommodityServiceProxy.queryArea(request.getCityId(), request.getBaseRequest().getRequestId());
            List<OsdActivity> activityList = getAreaAct(areaIds);
            if (CollectionUtils.isEmpty(activityList)) {
                return response;
            }
            Calendar current = Calendar.getInstance();
            Integer tenancy = CommonUtil.getTenancy(request.getPickUpTime(), request.getReturnTime());
            boolean isScatteredHour = CommonUtil.isScatteredHour(request.getPickUpTime(), request.getReturnTime());
            List<OsdActivity> result = activityList.stream().filter(OsdActivityConditionUtil.filterActivityTime(current)
                    .and(OsdActivityConditionUtil.filterArea(areaIds))
                    .and(OsdActivityConditionUtil.filterPickupTime(request.getPickUpTime()))
                    .and(OsdActivityConditionUtil.filterReturnTime(request.getReturnTime()))
                    .and(OsdActivityConditionUtil.filterTenancy(tenancy, isScatteredHour))).collect(Collectors.toList());
            response.setActivityList(result.stream().map(l -> l.getAct().getId()).collect(Collectors.toList()));
            return response;
        } catch (Exception e) {
            response.setBaseResponse(ResponseUtil.systemError());
            return response;
        } finally {
            Metrics.withTag("city", request.getCityId().toString())
                    .withTag("sourceFrom", StringUtils.isEmpty(request.getBaseRequest().getSourceFrom()) ? "0" : request.getBaseRequest().getSourceFrom())
                    .withTag("channel", Objects.isNull(request.getBaseRequest().getChannelId()) ? "0" : request.getBaseRequest().getChannelId().toString())
                    .withTag("result", CollectionUtils.isNotEmpty(response.getActivityList()) ? "1" : "0")
                    .recordOne("queryOsdActivityList");
        }

    }

    public List<OsdActivity> getAreaAct(Set<Long> areaSet) throws Exception {
        List<OsdActivity> allActivity = osdActivityCache.getAll();
        if (CollectionUtils.isEmpty(allActivity)) {
            return Lists.newArrayList();
        }
        return allActivity.stream().filter(l -> {
            if (Objects.isNull(l.getCondition())) {
                return false;
            }
            if (CollectionUtils.isEmpty(l.getCondition().getAreaIds())) {
                return true;
            }
            if (Objects.equals(l.getCondition().getExcludeAreaId(), true)) {
                return l.getCondition().getAreaIds().stream().noneMatch(areaSet::contains);
            }
            return l.getCondition().getAreaIds().stream().anyMatch(areaSet::contains);
        }).collect(Collectors.toList());
    }

    public boolean checkRequest(QueryOsdActivityListRequestType request, QueryOsdActivityListResponseType response) {
        if (request.getBaseRequest() == null) {
            response.setBaseResponse(ResponseUtil.fail("base request is null"));
            return false;
        }
        if (request.getCityId() == null || request.getCityId() <= 0) {
            response.setBaseResponse(ResponseUtil.fail("city is null"));
            return false;
        }
        if (request.getPickUpTime() == null || request.getReturnTime() == null || request.getPickUpTime().after(request.getReturnTime())) {
            response.setBaseResponse(ResponseUtil.fail("pick time or return time error"));
            return false;
        }
        return true;
    }
}
