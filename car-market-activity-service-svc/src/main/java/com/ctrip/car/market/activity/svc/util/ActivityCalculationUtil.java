package com.ctrip.car.market.activity.svc.util;

import com.ctrip.car.market.activity.contract.dto.ProductPrice;
import com.ctrip.car.market.activity.svc.bo.Activity;
import com.ctrip.car.market.activity.svc.bo.ActivityCondition;
import com.ctrip.car.market.activity.svc.bo.ActivityPrice;
import com.ctrip.car.market.common.calculate.ActCalculate;
import com.ctrip.car.market.common.entity.act.*;
import com.ctrip.car.market.common.enums.ActivityGroup;
import com.ctrip.car.market.common.enums.Fee;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

public class ActivityCalculationUtil {

    public static List<Activity> calculation(List<Activity> activityList, ProductPrice productPrice, ActivityCondition activityCondition) {
        if (CollectionUtils.isEmpty(activityList)) {
            return Lists.newArrayList();
        }
        PriceInfo priceInfo = buildPriceInfo(productPrice, activityCondition);
        List<Activity> result = Lists.newArrayList();
        //优先级按从小到大排序
        List<Integer> groupPrioritySortList = activityList.stream().map(Activity::getGroupPriority).distinct().sorted().toList();
        //优先级对应的活动分组
        Map<Integer, Integer> groupPriorityMap = activityList.stream().collect(Collectors.toMap(Activity::getGroupPriority, l -> l.getTemp().getGroupId(), (k1, k2) -> k1));
        //常规活动金额
        BigDecimal regularActivityAmount = BigDecimal.ZERO;
        for (Integer groupPriority : groupPrioritySortList) {
            //租车费扣完了，不再继续计算
            if (priceInfo.getActivityAmount().compareTo(productPrice.getRentAmount()) >= 0) {
                break;
            }
            Integer groupId = groupPriorityMap.get(groupPriority);
            List<Activity> list = activityList.stream().filter(l -> Objects.equals(l.getTemp().getGroupId(), groupId)).toList();
            Activity activity;
            //调价活动基于常规活动金额计算
            if (Objects.equals(groupId, ActivityGroup.Adjust.getId())) {
                activity = adjustActivityProcess(list, productPrice, regularActivityAmount, activityCondition.isCheckAdjustPrice());
            } else {
                activity = calculationActivityAmount(list, priceInfo);
            }
            if (activity == null || Optional.ofNullable(activity.getActivityAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            //常规活动
            if (Objects.equals(groupId, ActivityGroup.Regular.getId())) {
                regularActivityAmount = activity.getActivityAmount();
            }
            //最多扣完租车费
            if (priceInfo.getActivityAmount().add(activity.getActivityAmount()).compareTo(productPrice.getRentAmount()) > 0) {
                activity.setActivityAmount(productPrice.getRentAmount().subtract(priceInfo.getActivityAmount()));
                activity.setRealActivityAmount(activity.getActivityAmount());
            }
            result.add(activity);
            priceInfo.setActivityAmount(addActivityAmount(priceInfo.getActivityAmount(), activity.getActivityAmount()));
        }
        return result;
    }

    //调价活动处理
    private static Activity adjustActivityProcess(List<Activity> activityList, ProductPrice price, BigDecimal activityAmount, boolean isCheckAdjustPrice) {
        if (CollectionUtils.isEmpty(activityList) || Optional.ofNullable(price.getAdjustAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        Activity activity = activityList.getFirst();
        BigDecimal rentAmount = Optional.ofNullable(price.getRentAmount()).orElse(BigDecimal.ZERO);
        BigDecimal originalRentAmount = Optional.ofNullable(price.getOriginalRentAmount()).orElse(BigDecimal.ZERO);
        BigDecimal adjustAmount = Optional.ofNullable(price.getAdjustAmount()).orElse(BigDecimal.ZERO);
        if (isCheckAdjustPrice) {
            //重新计算校验调价金额
            if (rentAmount.compareTo(BigDecimal.ZERO) <= 0 || originalRentAmount.compareTo(BigDecimal.ZERO) <= 0 || adjustAmount.compareTo(BigDecimal.ZERO) <= 0) {
                return null;
            }
            //实际调价金额 = 实际租车费 / 原单租车费 * 原单调价金额
            BigDecimal newAdjustAmount = rentAmount.divide(originalRentAmount, 2, RoundingMode.UP).multiply(adjustAmount);
            //若实际调价金额 > 实际租车费-实际活动金额，实际调价金额 = 实际租车费-实际活动金额
            BigDecimal newRentAmount = rentAmount.subtract(activityAmount);
            if (newAdjustAmount.compareTo(newRentAmount) > 0) {
                newAdjustAmount = newRentAmount;
            }
            if (newAdjustAmount.compareTo(BigDecimal.ZERO) <= 0) {
                return null;
            }
            activity.setActivityAmount(newAdjustAmount.setScale(0, RoundingMode.UP));
            activity.setRealActivityAmount(newAdjustAmount.setScale(0, RoundingMode.UP));
            return activity;
        } else {
            //调价活动不计算金额，使用上游传的
            activity.setActivityAmount(adjustAmount);
            activity.setRealActivityAmount(adjustAmount);
            activity.setVendorCheckCodeIndex(0);
        }
        return activity;
    }

    private static Activity calculationActivityAmount(List<Activity> activityList, PriceInfo priceInfo) {
        if (CollectionUtils.isEmpty(activityList)) {
            return null;
        }
        for (Activity activity : activityList) {
            DeductionStrategy deductionStrategy = getDeductionStrategy(activity);
            DeductionAmountDto deductionAmountDto = ActCalculate.calculateDiscountAmountAndIndex(priceInfo, deductionStrategy);
            activity.setActivityAmount(deductionAmountDto.getAmount());
            activity.setVendorCheckCodeIndex(deductionAmountDto.getVendorCheckCodeIndex());
            activity.setRealActivityAmount(ActCalculate.calculateDiscountAmountV2(priceInfo, deductionStrategy));
        }
        return activityList.stream().filter(l -> Optional.ofNullable(l.getActivityAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) > 0).sorted(createActivityComparator()).findFirst().orElse(null);
    }

    private static PriceInfo buildPriceInfo(ProductPrice productPrice, ActivityCondition activityCondition) {
        PriceInfo pi = new PriceInfo();
        //租期
        pi.setTenancy(activityCondition.getTenancy());
        //取车时间
        pi.setpTime(activityCondition.getpTime());
        //下单时间
        pi.setbTime(activityCondition.getbTime());
        pi.setFeeList(Lists.newArrayList());
        //租车费
        pi.getFeeList().add(new FeeItem(Fee.RentAmount.getCode(), productPrice.getRentAmount()));
        //总价
        pi.getFeeList().add(new FeeItem(Fee.OrderAmount.getCode(), productPrice.getOrderAmount()));
        //日均价
        pi.getFeeList().add(new FeeItem(Fee.FirstRentAmount.getCode(), productPrice.getFirstRentAmount()));
        pi.getFeeList().add(new FeeItem(Fee.DailyPriceAmount.getCode(), productPrice.getDailyPrice()));
        //活动金额
        pi.setActivityAmount(BigDecimal.ZERO);
        return pi;
    }

    private static BigDecimal addActivityAmount(BigDecimal... activityAmount) {
        return Arrays.stream(activityAmount).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    private static DeductionStrategy getDeductionStrategy(Activity activity) {
        DeductionStrategy deductionStrategy = new DeductionStrategy();
        deductionStrategy.setDeductionType(activity.getTemp().getContent().getDeductionStrategy().getDeductionType());
        deductionStrategy.setStartAmount(activity.getTemp().getContent().getDeductionStrategy().getStartAmount());
        deductionStrategy.setLimitPrice(activity.getTemp().getContent().getDeductionStrategy().getLimitPrice());
        deductionStrategy.setShareWithCoupon(activity.getTemp().getContent().getDeductionStrategy().getShareWithCoupon());
        deductionStrategy.setIncludeFees(activity.getTemp().getContent().getDeductionStrategy().getIncludeFees());
        deductionStrategy.setDeductionAmount(activity.getTemp().getContent().getDeductionStrategy().getDeductionAmount());
        deductionStrategy.setDeductionAmountLimit(activity.getTemp().getContent().getDeductionStrategy().getDeductionAmountLimit());
        deductionStrategy.setDeductionStrategyList(CollectionUtils.isNotEmpty(activity.getTemp().getContent().getDeductionStrategy().getDeductionStrategyList())
                ? activity.getTemp().getContent().getDeductionStrategy().getDeductionStrategyList().stream().map(l -> {
            DeductionStrategyItem item = new DeductionStrategyItem();
            item.setStartValue(l.getStartValue());
            item.setDeductionAmount(l.getDeductionAmount());
            return item;
        }).collect(Collectors.toList()) : Lists.newArrayList());
        //自定义内容
        if (StringUtils.isNotBlank(activity.getAct().getCustomContent())) {
            CustomContent customContent = JsonUtil.toObject(activity.getAct().getCustomContent(), CustomContent.class);
            if (Objects.nonNull(customContent) && Objects.nonNull(customContent.getDeductionStrategy()) && Objects.nonNull(customContent.getDeductionStrategy().getDeductionAmount())) {
                deductionStrategy.setDeductionAmount(customContent.getDeductionStrategy().getDeductionAmount());
            }
            if (Objects.nonNull(customContent) && Objects.nonNull(customContent.getDeductionStrategy()) && CollectionUtils.isNotEmpty(customContent.getDeductionStrategy().getDeductionStrategyList())) {
                deductionStrategy.setDeductionStrategyList(customContent.getDeductionStrategy().getDeductionStrategyList().stream().map(l -> {
                    DeductionStrategyItem item = new DeductionStrategyItem();
                    item.setStartValue(l.getStartValue());
                    item.setDeductionAmount(l.getDeductionAmount());
                    return item;
                }).collect(Collectors.toList()));
            }
        }
        deductionStrategy.setComparisonTarget(activity.getTemp().getContent().getDeductionStrategy().getComparisonTarget());
        deductionStrategy.setSubsidyRangeMin(activity.getTemp().getContent().getDeductionStrategy().getSubsidyRangeMin());
        deductionStrategy.setSubsidyRangeMax(activity.getTemp().getContent().getDeductionStrategy().getSubsidyRangeMax());
        deductionStrategy.setAdjustType(activity.getTemp().getContent().getDeductionStrategy().getAdjustType());
        deductionStrategy.setDiscountBase(activity.getTemp().getDiscountBase());
        return deductionStrategy;
    }

    /**
     * 创建活动排序比较器
     * 排序规则：
     * 1. 优先取activityAmount最大的（降序）
     * 2. 金额一样取priority小的（升序，因为越小优先级越高）
     * 3. 优先级一样取活动id最大的（降序）
     */
    private static Comparator<Activity> createActivityComparator() {
        return Comparator
                // 1. 按活动金额降序排序（金额大的优先）
                .comparing(Activity::getActivityAmount, Comparator.reverseOrder())
                // 2. 按优先级升序排序（数值小的优先级高）
                .thenComparing(activity -> activity.getTemp().getPriority())
                // 3. 按活动ID降序排序（ID大的优先）
                .thenComparing(activity -> activity.getAct().getId(), Comparator.reverseOrder());
    }
}
