package com.ctrip.car.market.activity.svc.util;

import com.ctrip.car.market.activity.contract.dto.ActivityPriceItem;
import com.ctrip.car.market.activity.contract.dto.ShareDetail;
import com.ctrip.car.market.activity.svc.bo.Activity;
import com.ctrip.car.market.common.entity.act.ActivityTempContent;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class ConvertUtil {

    public static List<ActivityPriceItem> convert(List<Activity> activityList) {
        if (CollectionUtils.isEmpty(activityList)) {
            return Lists.newArrayList();
        }
        return activityList.stream().filter(l -> Objects.nonNull(l.getActivityAmount()) && l.getActivityAmount().compareTo(BigDecimal.ZERO) > 0).map(ConvertUtil::activityConvert).sorted(Comparator.comparing(ActivityPriceItem::getGroupPriority)).collect(Collectors.toList());
    }

    public static List<ActivityPriceItem> activityConvert(List<Activity> activityList) {
        return activityList.stream().map(l -> {
            ActivityPriceItem item = activityConvert(l);
            item.setShareDetail(shareConvert(l));
            return item;
        }).toList();
    }

    public static ActivityPriceItem activityConvert(Activity activity) {
        ActivityPriceItem item = new ActivityPriceItem();
        item.setActivityId(activity.getAct().getId());
        item.setRemark(activity.getTemp().getRemark());
        item.setLabelId(Objects.nonNull(activity.getTemp().getLabelId()) ? activity.getTemp().getLabelId().longValue() : 0L);
        item.setShareWithCoupon(Objects.nonNull(activity.getTemp().getContent().getDeductionStrategy()) && Objects.equals(activity.getTemp().getContent().getDeductionStrategy().getShareWithCoupon(), true));
        item.setPriority(activity.getTemp().getPriority());
        item.setVendorCouponCode(getVendorCheckCode(activity.getAct().getVendorCouponCode(), activity.getVendorCheckCodeIndex()));
        item.setActivityAmount(activity.getActivityAmount());
        item.setRealActivityAmount(activity.getRealActivityAmount());
        item.setPayOffType(Optional.ofNullable(activity.getTemp().getContent()).map(ActivityTempContent::getPayOffType).orElse(2));
        item.setIsNeedUse(true);
        item.setLimitPrice(Objects.nonNull(activity.getTemp().getContent().getDeductionStrategy().getLimitPrice()) ? activity.getTemp().getContent().getDeductionStrategy().getLimitPrice() : false);
        //只区分盲盒跟其他活动
        item.setActivityType(Objects.equals(activity.getTemp().getTemplateType(), 2) ? 2 : 1);
        item.setGroupId(activity.getTemp().getGroupId());
        item.setGroupPriority(activity.getGroupPriority());
        return item;
    }

    public static ShareDetail shareConvert(Activity activity) {
        ShareDetail item = new ShareDetail();
        if (Objects.nonNull(activity.getTemp().getContent()) && Objects.nonNull(activity.getTemp().getContent().getShareDetail())) {
            item.setCostShare(activity.getTemp().getContent().getShareDetail().getCostShare());
            item.setShareType(activity.getTemp().getContent().getShareDetail().getShareType());
            item.setCostType(activity.getTemp().getContent().getShareDetail().getCostType());
            if (Objects.equals(activity.getTemp().getContent().getShareDetail().getShareType(), 0)) {
                item.setShareAmount(activity.getTemp().getContent().getShareDetail().getPercentage());
            } else {
                item.setShareAmount(activity.getTemp().getContent().getShareDetail().getFixedAmount());
            }
            //分摊方式 0百分比 1固定金额
            if (Objects.equals(activity.getTemp().getContent().getShareDetail().getShareType(), 1)) {
                item.setRealAmount(activity.getActivityAmount().min(activity.getTemp().getContent().getShareDetail().getFixedAmount()));
            } else if (Objects.equals(activity.getTemp().getContent().getShareDetail().getShareType(), 0)) {
                //成本分摊方 0-供应商 1-携程
                if (Objects.equals(activity.getTemp().getContent().getShareDetail().getCostShare(), 0)) {
                    item.setRealAmount(activity.getActivityAmount().multiply(activity.getTemp().getContent().getShareDetail().getPercentage()).divide(new BigDecimal("100"), 0, RoundingMode.DOWN));
                } else {
                    item.setRealAmount(activity.getActivityAmount().multiply(activity.getTemp().getContent().getShareDetail().getPercentage()).divide(new BigDecimal("100"), 0, RoundingMode.UP));
                }
            }
        }
        return item;
    }

    private static String getVendorCheckCode(String code, Integer index) {
        int idx = Optional.ofNullable(index).orElse(0);
        if (StringUtils.isEmpty(code) || idx < 0 || !code.contains(",")) {
            return code;
        }
        String[] codeArr = code.split(",");
        if (idx <= codeArr.length - 1) {
            return codeArr[idx];
        }
        return code;
    }
}
