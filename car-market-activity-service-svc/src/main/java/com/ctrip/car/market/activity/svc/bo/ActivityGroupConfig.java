package com.ctrip.car.market.activity.svc.bo;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Component
public class ActivityGroupConfig {

    private final Integer defaultPriority = 999;

    @QConfig("100041788#activityGroupConfig.json")
    private List<ActivityGroupItem> activityGroupConfigList;

    public ActivityGroupItem find(Integer groupId) {
        if (CollectionUtils.isEmpty(this.activityGroupConfigList)) {
            return null;
        }
        return this.activityGroupConfigList.stream().filter(l -> Objects.equals(l.getGroupId(), groupId)).findFirst().orElse(new ActivityGroupItem());
    }

    public Integer findPriority(Integer groupId) {
        if (CollectionUtils.isEmpty(this.activityGroupConfigList)) {
            return defaultPriority;
        }
        ActivityGroupItem item = this.activityGroupConfigList.stream().filter(l -> Objects.equals(l.getGroupId(), groupId)).findFirst().orElse(null);
        return item != null ? Optional.ofNullable(item.getGroupPriority()).orElse(defaultPriority) : defaultPriority;
    }
}
