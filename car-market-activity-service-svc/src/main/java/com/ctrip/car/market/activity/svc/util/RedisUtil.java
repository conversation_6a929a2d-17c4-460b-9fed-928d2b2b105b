package com.ctrip.car.market.activity.svc.util;

import com.ctrip.car.market.activity.svc.config.RedisConfig;
import com.ctrip.framework.foundation.Foundation;
import credis.java.client.CacheProvider;
import credis.java.client.util.CacheFactory;
import org.apache.commons.lang3.StringUtils;

public class RedisUtil {

    private static String getMarketCacheClusterName() {
        if (StringUtils.equalsIgnoreCase(Foundation.server().getClusterName(), RedisConfig.getClusterName())) {
            return RedisConfig.getCacheName();
        }
        return "car_market_cache";
    }

    private static final CacheProvider provider = CacheFactory.getProvider(getMarketCacheClusterName());

    public static String get(String key) {
        return provider.get(key);
    }

    public static void set(String key, String value, long second) {
        provider.set(key, value);
        provider.expire(key, second);
    }
}
