package com.ctrip.car.market.activity.svc.util;

import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import credis.java.client.CacheProvider;
import credis.java.client.util.CacheFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Component
public class CarMarketCacheHelper {

    public static final String redisClusterName = "car_market_cache";

    public CacheProvider provider = CacheFactory.getProvider(redisClusterName);

    private static final ILog log = LogManager.getLogger(CarMarketCacheHelper.class);

    public Boolean setString(String redisKey, int seconds, String redisValue) {
        Map<String, String> logTagMap = new HashMap<>();
        logTagMap.put("redisClusterName", redisClusterName);
        logTagMap.put("redisKey", redisKey);
        try {
            if (StringUtils.isNotBlank(redisKey) && StringUtils.isNotBlank(redisValue)) {
                if (Objects.nonNull(provider)) {
                    try {
                        return provider.setex(redisKey, seconds, redisValue);
                    } catch (Exception e) {
                        log.error("RedisHelper.setString.setex", e, logTagMap);
                    }
                }
            }
        } catch (Exception e) {
            log.error("RedisHelper.setString", e, logTagMap);
            return false;
        }
        return false;
    }

    public String getString(String key) {
        try {
            return provider.get(key);
        } catch (Exception e) {
            Map<String, String> tags = new HashMap<String, String>();
            tags.put("REDIS_KEY", key);
            log.error("CarMarketCacheHelper.getString", e, tags);
        }
        return null;
    }


    public void del(String key) {
        try {
            provider.del(key);
        } catch (Exception e) {
            Map<String, String> tags = new HashMap<String, String>();
            tags.put("REDIS_KEY", key);
            log.error("CarMarketCacheHelper.del", e, tags);
        }
    }

}
