package com.ctrip.car.market.activity.svc.dto.old;

public class AutoCloseActivityDTO {

    private long configId;

    private String vendorId;

    private String vendorName;

    private long createTime;

    private long inValidTime;

    private String uid;

    private String requestId;

    private String cityName;

    private int cityId;

    private String reason;

    private String vehicleId;

    //车辆名称
    private String vehicleName;

    private String pickVendorStoreId;

    private String pickVendorStoreName;

    private String returnVendorStoreId;

    private String returnVendorStoreName;

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public long getConfigId() {
        return configId;
    }

    public void setConfigId(long configId) {
        this.configId = configId;
    }

    public String getVendorId() {
        return vendorId;
    }

    public void setVendorId(String vendorId) {
        this.vendorId = vendorId;
    }


    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public int getCityId() {
        return cityId;
    }

    public void setCityId(int cityId) {
        this.cityId = cityId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }


    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public long getInValidTime() {
        return inValidTime;
    }

    public void setInValidTime(long inValidTime) {
        this.inValidTime = inValidTime;
    }

    public AutoCloseActivityDTO() {
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getVehicleName() {
        return vehicleName;
    }

    public void setVehicleName(String vehicleName) {
        this.vehicleName = vehicleName;
    }

    public String getPickVendorStoreId() {
        return pickVendorStoreId;
    }

    public void setPickVendorStoreId(String pickVendorStoreId) {
        this.pickVendorStoreId = pickVendorStoreId;
    }

    public String getPickVendorStoreName() {
        return pickVendorStoreName;
    }

    public void setPickVendorStoreName(String pickVendorStoreName) {
        this.pickVendorStoreName = pickVendorStoreName;
    }

    public String getReturnVendorStoreId() {
        return returnVendorStoreId;
    }

    public void setReturnVendorStoreId(String returnVendorStoreId) {
        this.returnVendorStoreId = returnVendorStoreId;
    }

    public String getReturnVendorStoreName() {
        return returnVendorStoreName;
    }

    public void setReturnVendorStoreName(String returnVendorStoreName) {
        this.returnVendorStoreName = returnVendorStoreName;
    }
}
