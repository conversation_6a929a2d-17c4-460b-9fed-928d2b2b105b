package com.ctrip.car.market.activity.svc.cache.neww;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheBean;
import com.alicp.jetcache.anno.IncUpdateConsumerByRedis;
import com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst;
import com.ctrip.car.market.job.common.consts.CacheName;
import com.ctrip.car.market.job.common.entity.ActInfoDO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@CreateCacheBean
public class ActivityInfoCache {

    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.IsdActivity_TempMappingCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.ACT_INFO_TEMP_MAPPING_KEY)
    public Cache<Long, List<Long>> tempMappingCache;


    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.IsdActivity_VendorCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.ACT_INFO_VENDOR_KEY)
    public Cache<Long, List<ActInfoDO>> actVendorCache;

    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.IsdActivityCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.ACT_INFO_KEY)
    public Cache<Long, ActInfoDO> cache;

    public List<Long> queryByTempId(Long tempId) {
        return tempMappingCache.get(tempId);
    }

    public List<ActInfoDO> queryByVendorId(Long vendorId) {
        return actVendorCache.get(vendorId);
    }

    public List<ActInfoDO> queryByTempIds(List<Long> tempIds) {
        if (CollectionUtils.isEmpty(tempIds)) {
            return Lists.newArrayList();
        }
        List<Long> actIdList = tempIds.stream().flatMap(l -> Optional.ofNullable(tempMappingCache.get(l)).orElse(Lists.newArrayList()).stream()).collect(Collectors.toList());
        return actIdList.stream().map(li -> cache.get(li)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public List<Long> queryActId(Long tempId) {
        return tempMappingCache.get(tempId);
    }

    public ActInfoDO queryById(Long activityId) {
        return cache.get(activityId);
    }

    public List<Long> queryActIdByVendor(Long vendorId) {
        List<ActInfoDO> actInfoList = actVendorCache.get(vendorId);
        if (CollectionUtils.isEmpty(actInfoList)) {
            return Lists.newArrayList();
        }
        return actInfoList.stream().filter(Objects::nonNull).map(ActInfoDO::getId).collect(Collectors.toList());
    }
}
