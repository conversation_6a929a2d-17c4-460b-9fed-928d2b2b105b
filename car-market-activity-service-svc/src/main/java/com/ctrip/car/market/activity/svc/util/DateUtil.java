package com.ctrip.car.market.activity.svc.util;

import java.sql.Timestamp;
import java.util.Calendar;

public class DateUtil {

    public static Calendar toCalendar(Timestamp ts) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(ts.getTime());
        return calendar;
    }

    public static Calendar toCalendar(long timeInMillis) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(timeInMillis);
        return calendar;
    }

    public static Calendar getDayStart(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }

    public static Calendar getDayEnd(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar;
    }
}
