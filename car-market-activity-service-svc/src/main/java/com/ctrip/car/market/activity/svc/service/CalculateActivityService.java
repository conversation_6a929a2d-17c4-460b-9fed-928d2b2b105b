package com.ctrip.car.market.activity.svc.service;

import com.ctrip.car.market.activity.contract.CalculateActivityRequestType;
import com.ctrip.car.market.activity.contract.CalculateActivityResponseType;
import com.ctrip.car.market.activity.contract.dto.FeeItem;
import com.ctrip.car.market.activity.contract.dto.ProductCondition;
import com.ctrip.car.market.activity.contract.dto.ProductPrice;
import com.ctrip.car.market.activity.core.annotation.SOALog;
import com.ctrip.car.market.activity.core.service.BaseService;
import com.ctrip.car.market.activity.core.util.ResponseUtil;
import com.ctrip.car.market.activity.repository.entity.*;
import com.ctrip.car.market.activity.repository.service.ActivityService;
import com.ctrip.car.market.activity.svc.bo.Activity;
import com.ctrip.car.market.activity.svc.bo.ActivityCondition;
import com.ctrip.car.market.activity.svc.bo.ActivityGroupConfig;
import com.ctrip.car.market.activity.svc.bo.HolidayDto;
import com.ctrip.car.market.activity.svc.config.BaseConfig;
import com.ctrip.car.market.activity.svc.mapper.ActivityMapper;
import com.ctrip.car.market.activity.svc.proxy.CarOrderServiceProxy;
import com.ctrip.car.market.activity.svc.util.*;
import com.ctrip.car.market.common.entity.act.ActivityTempContent;
import com.ctrip.car.market.common.entity.act.CustomContent;
import com.ctrip.car.market.common.enums.ActivityGroup;
import com.ctrip.car.market.common.enums.DeductionType;
import com.ctrip.car.market.job.common.entity.ActInfoDO;
import com.ctrip.car.market.job.common.entity.ActTempInfoDO;
import com.ctrip.flight.intl.common.metric.Metrics;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class CalculateActivityService extends BaseService {

    @Resource
    private ActivityService service;

    @Resource
    private BaseConfig baseConfig;

    @Resource
    private ActivityMapper activityMapper;

    @Resource
    private ActivityGroupConfig activityGroupConfig;

    private final static String logTitle = "CalculateActivity";

    @SOALog(logTitle = logTitle)
    public CalculateActivityResponseType doBusiness(CalculateActivityRequestType request) {
        CalculateActivityResponseType response = new CalculateActivityResponseType();
        if (!this.checkRequest(request, response)) {
            return response;
        }
        try {
            List<Long> activityIds = CommonUtil.getActivityIdList(request.getActivityId(), request.getActivityIdList());
            List<Activity> activityList = Lists.newArrayList();
            for (Long activityId : activityIds) {
                ActCtripactinfo actInfo = service.queryByPk(activityId);
                if (actInfo == null) {
                    log.error("CalculateActivity", "act is null");
                    continue;
                }
                ActCtriptempinfo tempInfo = service.queryByTempId(actInfo.getTempId());
                if (tempInfo == null) {
                    log.error("CalculateActivity", "act temp is null");
                    continue;
                }
                activityList.add(new Activity(actConvert(actInfo), tempConvert(tempInfo), activityGroupConfig.findPriority(tempInfo.getGroupId())));
            }
            HolidayDto holidayDto = CommonUtil.getHoliday(request.getProductCondition().getPickUpTime(), request.getProductCondition().getReturnTime(), baseConfig.getHolidayList());
            Integer tenancy = getTenancy(request.getProductCondition().getPickUpTime(), request.getProductCondition().getReturnTime());
            List<Activity> result = activityList.stream().filter((ActivityConditionUtil.filterMinTenancy(tenancy))
                            .and(ActivityConditionUtil.filterHoliday(holidayDto))
                            .and(ActivityConditionUtil.filterPickupTime(request.getProductCondition().getPickUpTime(), request.getProductCondition().getReturnTime()))
            ).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(result)) {
                response.setBaseResponse(ResponseUtil.fail("activity verification failed"));
                return response;
            }
            //提前预定期满减、提前预定期满折使用下单时间计算提前预期期
            Calendar bTime = null;
            if (result.stream().anyMatch(l -> DeductionType.Advance_Amount.equal(l.getTemp().getContent().getDeductionStrategy().getDeductionType())
                    || DeductionType.Advance_Discount.equal(l.getTemp().getContent().getDeductionStrategy().getDeductionType()))) {
                bTime = CarOrderServiceProxy.queryOrderDate(request.getOrderId());
            }
            //计算优惠金额
            List<Activity> list = ActivityCalculationUtil.calculation(result, request.getProductPrice(), new ActivityCondition(tenancy, request.getProductCondition().getPickUpTime(), bTime, true));
            if (CollectionUtils.isEmpty(list)) {
                response.setBaseResponse(ResponseUtil.fail("verification of preferential amount failed"));
                return response;
            }
            response.setActivityList(ConvertUtil.activityConvert(list));
            response.setActivityInfo(CollectionUtils.isNotEmpty(list) ? ConvertUtil.activityConvert(list.getFirst()) : null);
            response.setShareDetail(CollectionUtils.isNotEmpty(list) ? ConvertUtil.shareConvert(list.getFirst()) : null);
            response.setBaseResponse(ResponseUtil.success());
            return response;
        } catch (Exception e) {
            response.setBaseResponse(ResponseUtil.systemError());
            return response;
        } finally {
            getHandlerContext().writeTagMap("orderId", Optional.ofNullable(request.getOrderId()).orElse(0L).toString());
            getHandlerContext().writeTagMap("result", Objects.equals(response.getBaseResponse().getCode(), "000000") ? "1" : "0");
            Metrics.withTag("sourceFrom", StringUtils.isEmpty(request.getBaseRequest().getSourceFrom()) ? "0" : request.getBaseRequest().getSourceFrom())
                    .withTag("channel", Objects.isNull(request.getBaseRequest().getChannelId()) ? "0" : request.getBaseRequest().getChannelId().toString())
                    .withTag("result", Objects.equals(response.getBaseResponse().getCode(), "000000") ? "1" : "0")
                    .recordOne("calculateActivity");
        }
    }

    private ActTempInfoDO tempConvert(ActCtriptempinfo temp) {
        ActTempInfoDO actTempInfoDO = activityMapper.toTemp(temp);
        actTempInfoDO.setContent(JsonUtil.toObject(temp.getTempContent(), ActivityTempContent.class));
        return actTempInfoDO;
    }

    private ActInfoDO actConvert(ActCtripactinfo act) {
        ActInfoDO actInfoDO = activityMapper.toAct(act);
        actInfoDO.setContent(JsonUtil.toObject(act.getCustomContent(), CustomContent.class));
        return actInfoDO;
    }

    private boolean checkRequest(CalculateActivityRequestType request, CalculateActivityResponseType response) {
        if (Objects.isNull(request.getBaseRequest())) {
            response.setBaseResponse(ResponseUtil.fail("base request is null"));
            return false;
        }
        if (Optional.ofNullable(request.getActivityId()).orElse(0L) <= 0 && CollectionUtils.isEmpty(request.getActivityIdList())) {
            response.setBaseResponse(ResponseUtil.fail("activity id is null"));
            return false;
        }
        ProductCondition condition = request.getProductCondition();
        if (Objects.isNull(condition)) {
            response.setBaseResponse(ResponseUtil.fail("product condition is null"));
            return false;
        }
        if (Objects.isNull(condition.getVendorId()) || condition.getVendorId() <= 0) {
            response.setBaseResponse(ResponseUtil.fail("vendor id error"));
            return false;
        }
        if (Objects.isNull(request.getProductPrice())) {
            response.setBaseResponse(ResponseUtil.fail("product price is null"));
            return false;
        }
        if (Objects.isNull(request.getProductPrice().getFirstRentAmount()) || request.getProductPrice().getFirstRentAmount().compareTo(BigDecimal.ZERO) < 0) {
            request.getProductPrice().setFirstRentAmount(BigDecimal.ZERO);
        }
        if (Objects.isNull(request.getProductPrice().getDailyPrice()) || request.getProductPrice().getDailyPrice().compareTo(BigDecimal.ZERO) < 0) {
            request.getProductPrice().setDailyPrice(BigDecimal.ZERO);
        }
        //租车费
        FeeItem feeItem = Optional.ofNullable(request.getProductPrice().getFeeList()).orElse(Lists.newArrayList()).stream().filter(l -> Objects.equals(l.getCode(), "1001") || Objects.equals(l.getCode(), "20000001")).findFirst().orElse(null);
        //没传租车费使用费用list中的兜底
        if (Optional.ofNullable(request.getProductPrice().getRentAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) <= 0 && feeItem != null) {
            request.getProductPrice().setRentAmount(feeItem.getAmount());
        }
        if (Optional.ofNullable(request.getProductPrice().getRentAmount()).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) <= 0) {
            response.setBaseResponse(ResponseUtil.fail("rent amount is null"));
            return false;
        }
        return true;
    }

    /**
     * 提前还车租期特殊逻辑 iDev：2233727
     */
    private Integer getTenancy(Calendar pTime, Calendar rTime) {
        long day = 24 * 3600000;
        //租期
        long tenancy = rTime.getTimeInMillis() - pTime.getTimeInMillis();
        long t = tenancy / day;
        //是否存在零散小时
        long remainder = tenancy % day;
        if (remainder > 0) {
            long tenancyBuff = baseConfig.getTenancyBuff();
            tenancyBuff = tenancyBuff <= 0L ? 30 : tenancyBuff;
            //零散小时在23:30~24:00之间租期+1
            long begin = day - tenancyBuff * 60000;
            if (remainder > begin) {
                t += 1;
            }
        }
        return (int) t;
    }
}
