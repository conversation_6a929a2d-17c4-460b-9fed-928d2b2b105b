package com.ctrip.car.market.activity.svc.service;

import com.ctrip.car.market.activity.contract.QueryAllOsdActivityListRequestType;
import com.ctrip.car.market.activity.contract.QueryAllOsdActivityListResponseType;
import com.ctrip.car.market.activity.contract.dto.OsdActivityDto;
import com.ctrip.car.market.activity.core.annotation.SOALog;
import com.ctrip.car.market.activity.core.service.BaseService;
import com.ctrip.car.market.activity.core.util.ResponseUtil;
import com.ctrip.car.market.activity.repository.entity.ActOsdactinfo;
import com.ctrip.car.market.activity.repository.entity.ActOsdlabel;
import com.ctrip.car.market.activity.repository.po.QueryActivityPara;
import com.ctrip.car.market.activity.repository.service.ActivityService;
import com.ctrip.car.market.activity.svc.util.CommonUtil;
import com.ctrip.car.market.common.entity.act.OsdCondition;
import com.ctrip.car.sdcommon.utils.GsonUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class QueryAllOsdActivityListService extends BaseService {

    @Resource
    private ActivityService service;

    private final static String logTitle = "queryAllOsdActivityList";

    @SOALog(logTitle = logTitle)
    public QueryAllOsdActivityListResponseType doBusiness(QueryAllOsdActivityListRequestType request) {
        QueryAllOsdActivityListResponseType response = new QueryAllOsdActivityListResponseType();
        response.setTotal(0);
        response.setBaseResponse(ResponseUtil.success());
        try {
            if (!this.checkRequest(request, response)) {
                return response;
            }
            QueryActivityPara para = new QueryActivityPara();
            para.setActivityIds(request.getActivityIdList());
            List<ActOsdactinfo> activityList = service.queryOsdActivityByPage(para, request.getPageNo(), request.getPageSize());
            response.setTotal(service.queryOsdActivityCount(para));
            if (CollectionUtils.isNotEmpty(activityList)) {
                Map<Long, List<ActOsdlabel>> labelMap = getLabel(activityList.stream().map(ActOsdactinfo::getId).distinct().collect(Collectors.toList()));
                response.setActivityList(activityList.stream().map(l -> {
                    List<Long> labelIds = labelMap.getOrDefault(l.getId(), Lists.newArrayList()).stream().map(ActOsdlabel::getLabelId).distinct().collect(Collectors.toList());
                    return this.convert(l, labelIds);
                }).collect(Collectors.toList()));
            }
        } catch (Exception e) {
            response.setBaseResponse(ResponseUtil.systemError());
        }
        return response;
    }

    public Map<Long, List<ActOsdlabel>> getLabel(List<Long> activityIds) throws Exception {
        List<List<Long>> idList = Lists.partition(activityIds, 100);
        List<ActOsdlabel> data = Lists.newArrayList();
        for (List<Long> ids : idList) {
            List<ActOsdlabel> temp = service.queryOsdActivityLabel(ids);
            if (CollectionUtils.isNotEmpty(temp)) {
                data.addAll(temp);
            }
        }
        return data.stream().collect(Collectors.groupingBy(ActOsdlabel::getActivityId));
    }

    public OsdActivityDto convert(ActOsdactinfo actInfo, List<Long> labelIds) {
        OsdActivityDto item = new OsdActivityDto();
        item.setActivityId(actInfo.getId());
        item.setName(actInfo.getName());
        item.setStatus(1);
        item.setType(actInfo.getType());
        item.setLabelIds(labelIds);

        OsdCondition condition = GsonUtil.fromJson(actInfo.getConditions(), OsdCondition.class);
        if (Objects.nonNull(condition)) {
            item.setPriceId(CommonUtil.getPriceId(condition.getPriceIds(), condition.getPriceId()));
            item.setVendorIds(CollectionUtils.isNotEmpty(condition.getVendorIds()) ? condition.getVendorIds().stream().map(Integer::longValue).collect(Collectors.toList()) : null);
            item.setAreaIds(condition.getAreaIds());
            item.setStandardProductIds(condition.getStandardProductIds());
            item.setPriority(Optional.ofNullable(condition.getPriority()).orElse(0));
            item.setPriceIds(CommonUtil.getPriceIds(condition.getPriceIds(), condition.getPriceId()));
        }
        return item;
    }

    public boolean checkRequest(QueryAllOsdActivityListRequestType request, QueryAllOsdActivityListResponseType response) {
        if (Objects.isNull(request.getPageNo()) || request.getPageNo() <= 0) {
            response.setBaseResponse(ResponseUtil.fail("page no error"));
            return false;
        }
        if (Objects.isNull(request.getPageSize()) || request.getPageSize() <= 0) {
            response.setBaseResponse(ResponseUtil.fail("page size error"));
            return false;
        }
        if (request.getPageSize() > 1000) {
            response.setBaseResponse(ResponseUtil.fail("page size not more than 1000"));
            return false;
        }
        return true;
    }
}
