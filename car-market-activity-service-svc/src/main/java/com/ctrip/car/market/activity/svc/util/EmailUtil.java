package com.ctrip.car.market.activity.svc.util;

import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.soa.platform.basesystem.emailservice.v1.EmailServiceClient;
import com.ctrip.soa.platform.basesystem.emailservice.v1.SendEmailRequest;
import com.ctrip.soa.platform.basesystem.emailservice.v1.SendEmailResponse;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Objects;

public class EmailUtil {

    private static final ILog log = LogManager.getLogger(EmailUtil.class);

    private static final EmailServiceClient emailServiceClient = EmailServiceClient.getInstance();

    public static EmailResultDTO sendEmail(EmailRequestDTO args) {
        EmailResultDTO result = new EmailResultDTO();
        try {
            SendEmailRequest request = new SendEmailRequest();
            request.setAppID(100041788);
            request.setBodyContent(String.format("<entry><content><![CDATA[<p>%s</p>]]></content></entry>", args.getBodyContent()));
            request.setBodyTemplateID(30030079);
            request.setSubject(args.getSubject());
            request.setBcc(Collections.singletonList("<EMAIL>"));
            request.setCc(Arrays.asList(args.getCc().split(";")));
            request.setEid("");
            request.setCharset("GB2312");
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DATE, 1);
            request.setExpiredTime(cal);
            request.setIsBodyHtml(true);
            request.setOrderID(args.getOrderId());
            request.setRecipient(Arrays.asList(args.getRecipient().split(";")));
            request.setRecipientName(args.getRecipientName());
            request.setSendCode("30030079");
            request.setSender("<EMAIL>");
            request.setSenderName("Car Activity System");
            request.setSourceID(0);
            request.setUid("ctrip.car-market-activity-service");
            SendEmailResponse response = emailServiceClient.sendEmail(request);
            log.info("EmailUtil.SendEmail", "request: " + JsonUtil.toString(request) + "\r\n\r\n" + "response: " + JsonUtil.toString(response));
            if (Objects.nonNull(response)) {
                result.setCode(response.getResultCode());
                result.setSuccess(true);
            } else {
                result.setCode(-1);
                result.setMsg("invoke sendEmail response null.");
                result.setSuccess(false);
            }
        } catch (Exception ex) {
            // ex.printStackTrace();
            result.setCode(-1);
            result.setMsg("invoke sendEmail error. " + ex.getMessage());
            result.setSuccess(false);
            log.error("EmailUtil.SendEmail", ex);
        }
        return result;
    }

    static public class EmailRequestDTO {
        private String recipient;

        private String recipientName;

        private String cc;

        private String subject;

        private String senderName;

        private String bodyContent;

        private Long orderId;

        public String getRecipient() {
            return recipient;
        }

        public void setRecipient(String recipient) {
            this.recipient = recipient;
        }

        public String getRecipientName() {
            return recipientName;
        }

        public void setRecipientName(String recipientName) {
            this.recipientName = recipientName;
        }

        public String getCc() {
            return cc;
        }

        public void setCc(String cc) {
            this.cc = cc;
        }

        public String getSubject() {
            return subject;
        }

        public void setSubject(String subject) {
            this.subject = subject;
        }

        public String getSenderName() {
            return senderName;
        }

        public void setSenderName(String senderName) {
            this.senderName = senderName;
        }

        public String getBodyContent() {
            return bodyContent;
        }

        public void setBodyContent(String bodyContent) {
            this.bodyContent = bodyContent;
        }

        public Long getOrderId() {
            return orderId;
        }

        public void setOrderId(Long orderId) {
            this.orderId = orderId;
        }
    }

    static public class EmailResultDTO {
        public int code;

        public String msg;

        public Boolean isSuccess;

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public Boolean getSuccess() {
            return isSuccess;
        }

        public void setSuccess(Boolean success) {
            isSuccess = success;
        }
    }

}
