package com.ctrip.car.market.activity.svc.service;

import com.ctrip.car.market.activity.contract.QueryActivityRequestType;
import com.ctrip.car.market.activity.contract.QueryActivityResponseType;
import com.ctrip.car.market.activity.core.annotation.SOALog;
import com.ctrip.car.market.activity.core.constant.LogConstant;
import com.ctrip.car.market.activity.core.service.BaseService;
import com.ctrip.car.market.activity.core.util.ResponseUtil;
import com.ctrip.car.market.activity.svc.bo.Activity;
import com.ctrip.car.market.activity.svc.bo.ActivityCondition;
import com.ctrip.car.market.activity.svc.bo.ActivityGroupConfig;
import com.ctrip.car.market.activity.svc.bo.HolidayDto;
import com.ctrip.car.market.activity.svc.cache.neww.*;
import com.ctrip.car.market.activity.svc.config.BaseConfig;
import com.ctrip.car.market.activity.svc.util.*;
import com.ctrip.car.market.common.enums.ActivityType;
import com.ctrip.car.market.common.enums.ProductType;
import com.ctrip.car.market.job.common.entity.ActInfoDO;
import com.ctrip.car.market.job.common.entity.ActTempInfoDO;
import com.ctrip.flight.intl.common.metric.Metrics;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class QueryActivityService extends BaseService {

    @Resource
    private ActivityInfoCache activityInfoCache;

    @Resource
    private ActivityTempCache activityTempCache;

    @Resource
    private ActivityCityCache activityCityCache;

    @Resource
    private ActivityProductCache activityProductCache;

    @Resource
    private ActivityVendorSkuCache activityVendorSkuCache;

    @Resource
    private BaseConfig baseConfig;

    @Resource
    private FilterSecretUtils filterSecretUtils;

    @Resource
    private ActivityGroupConfig activityGroupConfig;

    private final static String logTitle = "QueryActivity";


    @SOALog(logTitle = logTitle)
    public QueryActivityResponseType doBusiness(QueryActivityRequestType request) {
        QueryActivityResponseType response = new QueryActivityResponseType();
        if (!this.checkRequest(request, response)) {
            return response;
        }
        try {
            response.setBaseResponse(ResponseUtil.success());
            List<Activity> actList = getVendorAct(request.getProductCondition().getVendorId());
            if (CollectionUtils.isEmpty(actList)) {
                log.warn(logTitle, "vendor no activity," + request.getProductCondition().getVendorId());
                return response;
            }
            HolidayDto holidayDto = CommonUtil.getHoliday(request.getProductCondition().getPickUpTime(), request.getProductCondition().getReturnTime(), baseConfig.getHolidayList());
            Calendar current = Calendar.getInstance();
            Integer tenancy = CommonUtil.getTenancy(request.getProductCondition().getPickUpTime(), request.getProductCondition().getReturnTime());
            boolean isScatteredHour = CommonUtil.isScatteredHour(request.getProductCondition().getPickUpTime(), request.getProductCondition().getReturnTime());
            Integer sourceFrom = CommonUtil.getSourceFrom(request.getBaseRequest().getSourceFrom(), baseConfig.getSourceFromMapping());
            //分销渠道活动配置
            Set<Long> channelActivityConfig = baseConfig.getChannelActivity(request.getBaseRequest().getChannelId());
            //提前预定期
            long advanceTime = CommonUtil.getAdvanceTime(request.getProductCondition().getPickUpTime());
            //是否修改订单场景
            boolean isModifyOrder = CommonUtil.extContain(request.getBaseRequest().getExtList(), "modifyOrder");
            //是否调价版本
            boolean followPrice = CommonUtil.extContain(request.getBaseRequest().getExtList(), "followPrice");
            List<Activity> result = actList.stream().filter(ActivityConditionUtil.filterActivityTime(current)
                    .and(ActivityConditionUtil.filterVendor(request.getProductCondition().getVendorId()))
                    .and(ActivityConditionUtil.filterExcludeData(current))
                    .and(ActivityConditionUtil.filterRepetitionPeriod(current))
                    .and(ActivityConditionUtil.filterHoliday(holidayDto))
                    .and(ActivityConditionUtil.filterCity(request.getProductCondition().getCityIds()))
                    .and(ActivityConditionUtil.filterTenancy(tenancy, isScatteredHour))
                    .and(ActivityConditionUtil.filterPickupTime(request.getProductCondition().getPickUpTime(), request.getProductCondition().getReturnTime()))
                    .and(ActivityConditionUtil.filterTakeTimeType(request.getProductCondition().getPickUpTime()))
                    .and(ActivityConditionUtil.filterSourceFrom(sourceFrom))
                    .and(ActivityConditionUtil.filterChannel(request.getBaseRequest().getChannelId(), request.getBaseRequest().getDistributionChannelId(), baseConfig.getDistributionChannel(), channelActivityConfig))
                    .and(ActivityConditionUtil.filterPayMode(request.getProductCondition().getPayMode()))
                    .and(ActivityConditionUtil.filterVehicleGroup(request.getProductCondition().getVehicleGroupId(), request.getProductCondition().getVirtualVehicleGroupId()))
                    .and(ActivityConditionUtil.filterStoreType(request.getProductCondition().getStoreType()))
                    .and(ActivityConditionUtil.filterProduct(request.getProductCondition().getStandardProductId()))
                    .and(ActivityConditionUtil.filterSku(request.getProductCondition().getSkuId()))
                    .and(ActivityConditionUtil.filterPrefer(request.getProductCondition().getPrefer()))
                    .and(ActivityConditionUtil.filterActivityGroup(request.getActivityIdList(), isModifyOrder, followPrice))
                    .and(ActivityConditionUtil.filterSubsidyRange(request.getProductPrice().getAdjustAmount(), request.getProductPrice().getRentAmount()))
                    .and(ActivityConditionUtil.filterAdvanceTime(advanceTime))).collect(Collectors.toList());
            //用户属性过滤
            result = filterSecretUtils.filterSecret(request.getBaseRequest().getUid(), result, request.getBaseRequest().getSourceFrom());
            //计算活动金额
            List<Activity> activityList = ActivityCalculationUtil.calculation(result, request.getProductPrice(), new ActivityCondition(tenancy, request.getProductCondition().getPickUpTime(), null, false));
            response.setActivityList(ConvertUtil.convert(activityList));
            return response;
        } catch (Exception e) {
            log.error(logTitle, e);
            response.setBaseResponse(ResponseUtil.systemError());
            return response;
        } finally {
            getHandlerContext().writeTagMap("vendorId", Optional.ofNullable(request.getProductCondition().getVendorId()).orElse(0L).toString());
            Metrics.withTag("sourceFrom", StringUtils.isEmpty(request.getBaseRequest().getSourceFrom()) ? "0" : request.getBaseRequest().getSourceFrom())
                    .withTag("channel", Objects.isNull(request.getBaseRequest().getChannelId()) ? "0" : request.getBaseRequest().getChannelId().toString())
                    .withTag("result", CollectionUtils.isNotEmpty(response.getActivityList()) ? "1" : "0")
                    .withTag("blindBox", Objects.equals(request.getProductCondition().getActivityType(), 2) ? "1" : "0")
                    .recordOne("queryActivity");
        }
    }

    private List<Activity> getVendorAct(Long vendorId) {
        //供应商活动
        List<ActInfoDO> vendorActList = activityInfoCache.queryByVendorId(vendorId);
        //不需要报名的活动
        List<ActInfoDO> allVendorActList = activityInfoCache.queryByVendorId(0L);
        List<ActInfoDO> actList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(vendorActList)) {
            actList.addAll(vendorActList);
        }
        if (CollectionUtils.isNotEmpty(allVendorActList)) {
            actList.addAll(allVendorActList);
        }
        if (CollectionUtils.isEmpty(actList)) {
            return Lists.newArrayList();
        }
        return actList.stream().map(l -> {
            ActTempInfoDO temp = activityTempCache.queryByTmpId(l.getTempId());
            if (Objects.isNull(temp)) {
                return null;
            }
            List<Integer> city = activityCityCache.queryCityByActId(l.getId());
            List<Long> product = activityProductCache.queryByActId(l.getId());
            List<Long> sku = activityVendorSkuCache.queryByActId(l.getVendorId());
            Set<Integer> citySet = CollectionUtils.isEmpty(city) ? Sets.newHashSet() : Sets.newHashSet(city);
            Set<Integer> returnCitySet = Sets.newHashSet();
            Set<Long> productSet = CollectionUtils.isEmpty(product) ? Sets.newHashSet() : Sets.newHashSet(product);
            Set<Long> skuSet = CollectionUtils.isEmpty(sku) ? Sets.newHashSet() : Sets.newHashSet(sku);
            Integer groupPriority = activityGroupConfig.findPriority(temp.getGroupId());
            return new Activity(l, temp, citySet, returnCitySet, productSet, skuSet, groupPriority);
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private boolean checkRequest(QueryActivityRequestType request, QueryActivityResponseType response) {
        if (Objects.isNull(request.getBaseRequest())) {
            response.setBaseResponse(ResponseUtil.fail("base request is null"));
            return false;
        }
        String productConditionCheck = VerificationUtil.checkProductCondition(request.getProductCondition(), 1);
        if (StringUtils.isNotBlank(productConditionCheck)) {
            response.setBaseResponse(ResponseUtil.fail(productConditionCheck));
            return false;
        }
        String productPriceCheck = VerificationUtil.checkProductPrice(request.getProductPrice());
        if (StringUtils.isNotBlank(productPriceCheck)) {
            response.setBaseResponse(ResponseUtil.fail(productPriceCheck));
            return false;
        }
        return true;
    }
}
