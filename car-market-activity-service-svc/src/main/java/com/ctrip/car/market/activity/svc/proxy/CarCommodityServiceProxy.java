package com.ctrip.car.market.activity.svc.proxy;

import com.ctrip.car.commodity.common.CarCommodityCommonApiClient;
import com.ctrip.car.commodity.common.service.types.AreaGroupDto;
import com.ctrip.car.commodity.common.service.types.CommodityCommonRequest;
import com.ctrip.car.commodity.common.service.types.QueryAreaGroupByCityAndCountryRequestType;
import com.ctrip.car.commodity.common.service.types.QueryAreaGroupByCityAndCountryResponseType;
import com.ctrip.car.market.activity.svc.util.JsonUtil;
import com.ctrip.car.sdcommon.utils.LogUtils;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

public class CarCommodityServiceProxy {

    private final static CarCommodityCommonApiClient client = CarCommodityCommonApiClient.getInstance();

    private static final LogUtils<CarCommodityServiceProxy> log = new LogUtils<>();

    public static Set<Long> queryArea(Integer cityId, String requestId) {
        try {
            Map<String, String> tag = Maps.newHashMap();
            tag.put("requestId", requestId);
            QueryAreaGroupByCityAndCountryRequestType requestType = new QueryAreaGroupByCityAndCountryRequestType();
            requestType.setCommonRequest(new CommodityCommonRequest());
            requestType.getCommonRequest().setRequestId(requestId);
            requestType.setCityId(cityId);
            log.info("queryArea_req", JsonUtil.toString(requestType), tag);
            QueryAreaGroupByCityAndCountryResponseType responseType = client.queryAreaGroupByCityAndCountry(requestType);
            log.info("queryArea_res", JsonUtil.toString(responseType), tag);
            return Objects.nonNull(responseType) && CollectionUtils.isNotEmpty(responseType.getAreaGroupList()) ?
                    responseType.getAreaGroupList().stream().map(AreaGroupDto::getAreaGroupId).filter(Objects::nonNull).collect(Collectors.toSet()) : Sets.newHashSet();
        } catch (Exception e) {
            log.error("queryArea", e);
            return Sets.newHashSet();
        }
    }
}
