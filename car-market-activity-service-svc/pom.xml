<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>car-market-activity-service</artifactId>
        <groupId>com.ctrip.car.market</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>car-market-activity-service-svc</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.ctrip.framework.ckafka</groupId>
            <artifactId>client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.ckafka</groupId>
            <artifactId>codec</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>car-market-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>car-market-activity-service-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>car-market-activity-service-repository</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.25057</groupId>
            <artifactId>carcommoditycommonservice</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.osd.translate</groupId>
            <artifactId>client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>javax.servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>dto</artifactId>
                    <groupId>com.ctrip.car.osd.framework</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.osd.framework</groupId>
            <artifactId>dto</artifactId>
            <version>2.2.30</version>
            <exclusions>
                <exclusion>
                    <artifactId>javax.servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>car-market-job-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>objenesis</artifactId>
                    <groupId>org.objenesis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>groovy-all</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.sd</groupId>
            <artifactId>carcache-all</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>HdrHistogram</artifactId>
                    <groupId>org.hdrhistogram</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>compiler</artifactId>
                    <groupId>com.github.spullara.mustache.java</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>dto</artifactId>
                    <groupId>com.ctrip.car.osd.framework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>objenesis</artifactId>
                    <groupId>org.objenesis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>javax.servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>javax.annotation-api</artifactId>
                    <groupId>javax.annotation</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>elasticsearch</artifactId>
                    <groupId>org.elasticsearch</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car.osd.framework</groupId>
            <artifactId>common</artifactId>
            <version>2.14.31</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.hermes</groupId>
                    <artifactId>hermes-kafka</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>dto</artifactId>
                    <groupId>com.ctrip.car.osd.framework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>annotations</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>javax.servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>carosdnotificationcenter</artifactId>
                    <groupId>com.ctrip.soa.car.osd.notificationcenter.v1</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.soa.21994</groupId>
            <artifactId>carsdqueryorderservice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.car.sd.ctqrestfulshopping.v1</groupId>
            <artifactId>ctqrestfulshopping</artifactId>
        </dependency>
    </dependencies>
</project>