package com.ctrip.car.market.activity.core.service;

import com.ctrip.car.market.activity.core.constant.LogConstant;
import com.ctrip.car.sdcommon.utils.HandlerContext;
import com.ctrip.car.sdcommon.utils.HandlerContextMgt;
import com.ctrip.car.sdcommon.utils.LogUtils;

/**
 * <AUTHOR>
 * @date 2022/08/02
 **/
public class BaseService {

    public LogUtils<BaseService> log = new LogUtils<>();

    public HandlerContext getHandlerContext() {
        return HandlerContextMgt.getCurrentHandlerContext();
    }

    public String getLogTitle(String defaultLogTitle) {
        return getHandlerContext().getTagMap().getOrDefault(LogConstant.LogTitle, defaultLogTitle);
    }

    public void writeLogTag(String tag, String value) {
        getHandlerContext().writeLogTag(tag, value);
    }

    public String getRequestId() {
        return getHandlerContext().getTagMap().get(LogConstant.RequestId);
    }
}
