package com.ctrip.car.market.activity.core.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import qunar.tc.qmq.MessageProducer;
import qunar.tc.qmq.producer.MessageProducerProvider;

/**
 * <AUTHOR>
 * @date 2021/08/23
 **/
@Configuration
public class QmqProductConfig {
    @Bean
    MessageProducer producer() {
        MessageProducerProvider provider = new MessageProducerProvider();
        provider.init();
        return provider;
    }
}

