package com.ctrip.car.market.activity.core.util;


import com.ctrip.car.market.activity.contract.BaseResponse;

public class ResponseUtil {

    public static BaseResponse success() {
        BaseResponse result = new BaseResponse();
        result.setCode("000000");
        result.setMessage("success");
        return result;
    }

    public static BaseResponse fail(String message) {
        BaseResponse result = new BaseResponse();
        result.setCode("100000");
        result.setMessage(message);
        return result;
    }

    public static BaseResponse systemError() {
        BaseResponse result = new BaseResponse();
        result.setCode("500000");
        result.setMessage("system error");
        return result;
    }
}
