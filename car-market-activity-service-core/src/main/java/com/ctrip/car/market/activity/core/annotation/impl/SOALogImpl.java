package com.ctrip.car.market.activity.core.annotation.impl;

import com.ctrip.car.market.activity.contract.BaseRequest;
import com.ctrip.car.market.activity.contract.BaseResponse;
import com.ctrip.car.market.activity.core.annotation.SOALog;
import com.ctrip.car.market.activity.core.constant.LogConstant;
import com.ctrip.car.market.activity.core.service.BaseService;
import com.ctrip.car.market.activity.core.util.ReflectUtil;
import com.ctrip.car.market.activity.core.util.ResponseUtil;
import com.ctrip.car.sdcommon.utils.LogUtils;
import com.ctrip.car.sdcommon.utils.SerializerUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Component
@Aspect
public class SOALogImpl extends BaseService {

    public static final String BASE_REQUEST_NAME = "baseRequest";
    public static final String BASE_RESPONSE_NAME = "baseResponse";

    LogUtils<SOALogImpl> log = new LogUtils<>();

    @Around("@annotation(com.ctrip.car.market.activity.core.annotation.SOALog)")
    private Object record(ProceedingJoinPoint pjp) {
        Object requestObj = pjp.getArgs()[0];
        BaseRequest baseRequest = getBaseRequest(requestObj);
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        Method method = signature.getMethod();
        Object responseObj = null;
        long before = System.currentTimeMillis();
        try {
            SOALog soaLogAnnotation = method.getAnnotation(SOALog.class);
            getHandlerContext().writeTagMap(LogConstant.LogTitle, soaLogAnnotation.logTitle());
            if (baseRequest != null) {
                if (StringUtils.isNotEmpty(baseRequest.getRequestId())) {
                    getHandlerContext().writeTagMap(LogConstant.RequestId, baseRequest.getRequestId());
                }
                if (StringUtils.isNotEmpty(baseRequest.getUid())) {
                    getHandlerContext().writeTagMap(LogConstant.uid, baseRequest.getUid());
                }
            }
            getHandlerContext().writeLogTag(LogConstant.Request, SerializerUtils.fastJSONSerialize(requestObj));
            responseObj = pjp.proceed();
            getHandlerContext().writeLogTag(LogConstant.Response, SerializerUtils.fastJSONSerialize(responseObj));
            return responseObj;
        } catch (Throwable e) {
            log.error(getLogTitle("SOA"), e, getHandlerContext().getTagMap());

            BaseResponse baseResponse = ResponseUtil.fail("system error");
            baseResponse.setDuration(System.currentTimeMillis() - before);
            try {
                if (responseObj == null) {
                    Class<?> responseType = method.getReturnType();
                    responseObj = responseType.getDeclaredConstructor().newInstance();
                }
                ReflectUtil.setFieldValue(responseObj, BASE_RESPONSE_NAME, baseResponse);
            } catch (Exception ex) {
                log.error(getLogTitle("SOA"), e, getHandlerContext().getTagMap());
            }
            getHandlerContext().writeLogTag(LogConstant.Response, SerializerUtils.fastJSONSerialize(responseObj));
            return responseObj;
        } finally {
            log.info(getHandlerContext().getTagMap().getOrDefault(LogConstant.LogTitle, "SOA"), getHandlerContext().getTagsContent(), getHandlerContext().getTagMap());
            getHandlerContext().close();
        }
    }

    private BaseRequest getBaseRequest(Object requestObj) {
        try {
            Object baseRequestObj = ReflectUtil.getFieldValue(requestObj, BASE_REQUEST_NAME);
            if (baseRequestObj instanceof BaseRequest) {
                return (BaseRequest) baseRequestObj;
            }
        } catch (Exception e) {
        }
        return null;
    }


}
