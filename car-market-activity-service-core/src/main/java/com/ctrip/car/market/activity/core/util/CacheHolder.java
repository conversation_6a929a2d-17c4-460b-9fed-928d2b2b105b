package com.ctrip.car.market.activity.core.util;

import org.quartz.CronExpression;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;

public class CacheHolder<T> {

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private final Supplier<Map<Long, T>> getter;

    private final CronExpression expression;

    private final ReentrantLock lock = new ReentrantLock();

    private Map<Long, T> cache;

    private long timeout;

    private AtomicBoolean init = new AtomicBoolean(false);

    public CacheHolder(Supplier<Map<Long, T>> getter, String cron) {
        if (!CronExpression.isValidExpression(cron))
            throw new IllegalArgumentException("cron fail");
        this.getter = getter;
        try {
            this.expression = new CronExpression(cron);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    public Map<Long, T> get() {
        long current = System.currentTimeMillis();
        if (timeout < current) {
            timeout = expression.getNextValidTimeAfter(new Date(current)).getTime();
            if (init.get()) {
                CompletableFuture.runAsync(this::update);
            } else {
                init.set(true);
                this.update();
            }
        }
        return cache;
    }

    private void update() {
        lock.lock();
        try {
            cache = getter.get();
        } finally {
            lock.unlock();
        }
    }

    public void refresh() {
        this.timeout = 0;
        this.get();
    }

    public void refresh(Long key, T data) {
        if (lock.isLocked()) {
            return;
        }
        if (data == null) {
            this.cache.remove(key);
        } else {
            this.cache.put(key, data);
        }
    }

    @Override
    public String toString() {
        return "next refresh time：" + sdf.format(new Date(timeout));
    }
}
