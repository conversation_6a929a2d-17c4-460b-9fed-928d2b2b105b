<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>car-market-activity-service</artifactId>
        <groupId>com.ctrip.car.market</groupId>
        <version>1.0.0</version>
    </parent>

    <artifactId>car-market-activity-service-core</artifactId>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>car-market-activity-contract</artifactId>
            <exclusions>
<!--                <exclusion>-->
<!--                    <artifactId>cglib-nodep</artifactId>-->
<!--                    <groupId>cglib</groupId>-->
<!--                </exclusion>-->
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.soa.22084</groupId>
            <artifactId>cdpsoaservice</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car</groupId>
            <artifactId>car-sd-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.platform</groupId>
            <artifactId>ctrip-dal-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.platform</groupId>
            <artifactId>ctrip-datasource</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctriposs.baiji</groupId>
            <artifactId>baiji-rpc-extensions</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.framework</groupId>
            <artifactId>vi</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>mockito-core</artifactId>
                    <groupId>org.mockito</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-jdk8</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.flight.intl.common</groupId>
            <artifactId>metric-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.framework</groupId>
            <artifactId>canal-json</artifactId>
        </dependency>

        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
        </dependency>
    </dependencies>

</project>
