<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.ctrip.car.market</groupId>
        <artifactId>car-market-activity-service</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>car-market-activity-service-api</artifactId>
    <packaging>war</packaging>

    <name>car-market-activity-service-api</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>car-market-activity-service-svc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>car-market-activity-service-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car</groupId>
            <artifactId>maiar-client</artifactId>
        </dependency>

    </dependencies>

    <build>

    </build>

</project>
