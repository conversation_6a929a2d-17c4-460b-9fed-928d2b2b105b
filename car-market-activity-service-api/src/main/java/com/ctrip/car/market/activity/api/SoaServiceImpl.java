package com.ctrip.car.market.activity.api;

import com.ctrip.car.maiar.soa.aop.MaiarNode;
import com.ctrip.car.market.activity.contract.*;
import com.ctrip.car.market.activity.svc.service.*;
import com.ctrip.car.market.activity.svc.service.old.OldActivityQueryService;
import com.ctrip.car.market.activity.svc.service.old.OldActivityUseCancelService;
import com.ctriposs.baiji.rpc.common.types.CheckHealthRequestType;
import com.ctriposs.baiji.rpc.common.types.CheckHealthResponseType;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

@MaiarNode(logType = "SOA", exclude = "checkHealth")
@Service
public class SoaServiceImpl implements CarMarketActivitySoaService {

    private final static Long newActivityId = 200000L;

    @Override
    public CheckHealthResponseType checkHealth(CheckHealthRequestType checkHealthRequestType) throws Exception {
        return new CheckHealthResponseType();
    }

    @Resource
    private QueryAllActivityListService queryAllActivityListService;

    @Override
    public QueryAllActivityListResponseType queryAllActivityList(QueryAllActivityListRequestType queryAllActivityListRequestType) throws Exception {
        return queryAllActivityListService.doBusiness(queryAllActivityListRequestType);
    }

    @Resource
    private QueryActivityListNewService queryActivityListNewService;

    @Override
    public QueryActivityListResponseType queryActivityList(QueryActivityListRequestType queryActivityListRequestType) throws Exception {
        return queryActivityListNewService.doBusiness(queryActivityListRequestType);
    }

    @Resource
    private QueryActivityService queryActivityService;

    @Override
    public QueryActivityResponseType queryActivity(QueryActivityRequestType queryActivityRequestType) throws Exception {
        return queryActivityService.doBusiness(queryActivityRequestType);
    }

    @Resource
    private ValidationActivityService validationActivityService;

    @Override
    public ValidationActivityResponseType validationActivity(ValidationActivityRequestType validationActivityRequestType) throws Exception {
        return validationActivityService.doBusiness(validationActivityRequestType);
    }

    @Resource
    private UseActivityService useActivityService;

    @Resource
    private OldActivityUseCancelService oldActivityUseCancelService;

    @Override
    public UseActivityResponseType useActivity(UseActivityRequestType requestType) throws Exception {
        if (Objects.nonNull(requestType.getActivityId()) && requestType.getActivityId() >= newActivityId) {
            return useActivityService.doBusiness(requestType);
        } else {
            return oldActivityUseCancelService.useActivity(requestType);
        }
    }

    @Resource
    private CancelActivityService cancelActivityService;

    @Override
    public CancelActivityResponseType cancelActivity(CancelActivityRequestType requestType) throws Exception {
        if (Objects.nonNull(requestType.getActivityId()) && requestType.getActivityId() >= newActivityId) {
            return cancelActivityService.doBusiness(requestType);
        } else {
            return oldActivityUseCancelService.cancelActivity(requestType);
        }
    }

    @Resource
    private ActivityDetailService activityDetailService;

    @Resource
    private OldActivityQueryService oldActivityQueryService;

    @Override
    public ActivityDetailResponseType activityDetail(ActivityDetailRequestType requestType) throws Exception {
        if (Optional.ofNullable(requestType.getActivityId()).orElse(0L) >= newActivityId || CollectionUtils.isNotEmpty(requestType.getActivityIdList())) {
            return activityDetailService.doBusiness(requestType);
        }
        return oldActivityQueryService.getActivityDetail(requestType);
    }

    @Resource
    private CalculateActivityService calculateActivityService;

    @Override
    public CalculateActivityResponseType calculateActivity(CalculateActivityRequestType calculateActivityRequestType) throws Exception {
        return calculateActivityService.doBusiness(calculateActivityRequestType);
    }

    @Resource
    private QueryAllOsdActivityListService queryAllOsdActivityListService;

    @Override
    public QueryAllOsdActivityListResponseType queryAllOsdActivityList(QueryAllOsdActivityListRequestType queryAllOsdActivityListRequestType) throws Exception {
        return queryAllOsdActivityListService.doBusiness(queryAllOsdActivityListRequestType);
    }

    @Resource
    private QueryOsdActivityListService queryOsdActivityListService;

    @Override
    public QueryOsdActivityListResponseType queryOsdActivityList(QueryOsdActivityListRequestType queryOsdActivityListRequestType) throws Exception {
        return queryOsdActivityListService.doBusiness(queryOsdActivityListRequestType);
    }

    @Resource
    private QueryOsdActivityService queryOsdActivityService;

    @Override
    public QueryOsdActivityResponseType queryOsdActivity(QueryOsdActivityRequestType queryOsdActivityRequestType) throws Exception {
        return queryOsdActivityService.doBusiness(queryOsdActivityRequestType);
    }

    @Resource
    private QueryVendorActivityService queryVendorService;

    @Override
    public QueryVendorActivityResponseType queryVendorActivity(QueryVendorActivityRequestType queryVendorActivityRequestType) throws Exception {
        return queryVendorService.doBusiness(queryVendorActivityRequestType);
    }

    @Resource
    private QueryFlightActivityService queryFlightActivityService;

    @Override
    public QueryFlightActivityResponseType queryFlightActivity(QueryFlightActivityRequestType queryFlightActivityRequestType) throws Exception {
        return queryFlightActivityService.doBusiness(queryFlightActivityRequestType);
    }
}
